import { type RechargeOrderInfo, type SubmissionOrderInfo } from '@/services/types/order'
import { type Response } from '@/services/types/reponse'
import { type PageResult } from './typing'


// 写作订单
export function getSubmissionOrderList(
  params: any
): Promise<Response<PageResult<SubmissionOrderInfo>>> {

  return request.get('/submission/list', { params })
}

export function getSubmissionLastDone(
): Promise<Response<PageResult<SubmissionOrderInfo>>> {
  // const params = { teamId: '' }

  return request.get('/submission/getLastDone')
}

// 订单删除
export function submissionDelete(params: any): Promise<Response<PageResult<any>>> {
  return request.delete('/submission/delete', { params })
}

// 充值订单
export function getRechargeOrderList(
  params: any
): Promise<Response<PageResult<RechargeOrderInfo>>> {

  return request.get('/order/list', { params })
}
