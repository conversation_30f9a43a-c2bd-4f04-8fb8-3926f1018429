<template>
    <!---图书专著支付弹窗-->
    <ClientOnly>
        <a-modal v-model:open="visible" :title-align-center="true" :title="payData.title" :width="500"
            :show-footer="false" :footer="null" :z-index="1101" @cancel="handleClose" :maskStyle="{ zIndex: 1100 }"
            :destroy-on-close="true">
            <a-spin :spinning="loading">
                <div class="py-6 pt-10 pb-0 space-y-4 text-center text-sm">
                    <div v-if="chapterStore.payTriggerType == BOOK_PAY_TRIGGER_TYPE.AI_EDITOR">
                        <div @click="() => rechargeStore.openRechargeModal()"
                            class="flex justify-between items-center cursor-pointer px-[20px] py-[12px] bg-[#F5F7FF] border border-[#C3D5FD] rounded-[10px] mb-[20px]">
                            <div class="text-[#2551B5] text-[15px]">升级会员，免费送编辑次数</div>
                            <div class="text-[#FF2442] text-[15px]">去升级
                                <RightOutlined color="#ff4242" font-size="15px" />
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-[20px] bg-[#F5F7FF] rounded-[10px]">
                            <div class="text-[#777777] text-[15px]">本次编辑费用：</div>
                            <div class="text-[#333333] text-[15px]">{{ formatCoinAmount(payData.needPayCoins, 0) }}硬币
                            </div>
                        </div>
                    </div>

                    <div class="py-1.5 bg-blue-50 text-gray-500 rounded-lg " v-else>
                        <div class="flex justify-between px-5 py-1.5" v-if="payData.docStudyCoins">
                            <div>文档学习费用：</div>
                            <div class=" text-gray-700">{{ formatCoinAmount(payData.docStudyCoins, 2) }}硬币
                            </div>
                        </div>
                        <div class="flex justify-between px-5 py-1.5" v-if="payData.needPayCoins">
                            <div>写作费用：</div>
                            <div class=" text-gray-700">{{ formatCoinAmount(payData.needPayCoins, 0) }}硬币</div>
                        </div>
                    </div>
                    <div class="flex items-center px-5 py-3 bg-blue-50 text-gray-700 rounded-lg text-left ">
                        <Iconfont name="yingbi" :size="20"></Iconfont> <span class="pl-1">剩余硬币：{{
                            coinBalance }}</span>
                        <!-- formatCoinAmount(coinBalance) -->
                    </div>

                    <div class="pt-4">
                        <button v-if="isNeedRechargeCoinsOrVip" @click="handleRecharge"
                            class="px-8 py-2 rounded-lg transition-colors whitespace-nowrap bg-blue-700 hover:bg-blue-800 text-white text-base self-center cursor-pointer">充值</button>

                        <button v-else @click="handleCreate"
                            class="px-8 py-2 rounded-lg transition-colors whitespace-nowrap bg-blue-700 hover:bg-blue-800 text-white text-base self-center cursor-pointer">确认</button>
                    </div>
                </div>
            </a-spin>
        </a-modal>
    </ClientOnly>

</template>

<script setup lang="ts">
import Iconfont from '@/components/Iconfont.vue';
import { useChapterStore } from '@/stores/chapter';
import { formatCoinAmount } from '@/utils/utils';
import { RightOutlined } from '@ant-design/icons-vue';
import { computed, ref } from 'vue';
import { UserService } from '~/services/user';
import { useRechargeStore } from '~/stores/recharge';
import { BOOK_PAY_TRIGGER_TYPE, RechargeModalTab } from '~/utils/constants';

const rechargeStore = useRechargeStore();

const chapterStore = useChapterStore()


const { $eventBus } = useNuxtApp();
const props = defineProps({
    modelValue: {
        type: Boolean,
        required: true
    },
});

const emit = defineEmits(['update:modelValue', 'download']);

const loading = ref(true)

const payDataParams = ref({
    title: '确认支付',
    needPayCoins: 0,
    docStudyCoins: 0,
    isNeedRecharge: false,
    isNeedUpgrade: false,
}
)
const visible = ref(props.modelValue)

watch(() => props.modelValue, (val) => {
    visible.value = val
})
watch(() => visible.value, (val) => {
    emit('update:modelValue', val)
})

const handleClose = () => {
    emit('update:modelValue', false);
    if (chapterStore.payTriggerType == BOOK_PAY_TRIGGER_TYPE.GENERATE_CHAPTER_ATTACHMENT) {
        $eventBus.emit(StarloveConstants.keyOfEventBus.bookEditorCreateAfterConfirmPayCancelModal, chapterStore.payTriggerType)
    }
};


const coinBalance = computed(() => {
    return UserService.getCurrentLoginInfo()?.coinBalance || 0
})

// const knowledgeAssistantMemberInfo = computed(() => {
//     return UserService.getKnowledgeAssistantMemberInfo()
// })

const payData = computed(() => {
    const result = payDataParams.value
    // 上传参考资料充值-只有普通用户进入
    if (chapterStore.payTriggerType == BOOK_PAY_TRIGGER_TYPE.GENERATE_CHAPTER_ATTACHMENT) {
        return {
            ...result,
            needPayCoins: 0,
            docStudyCoins: chapterStore.uploadFilesToStudyFees,
            isNeedRecharge: chapterStore.uploadFilesToStudyFees > coinBalance.value,
        }
        // 创作全部章节
    } else if (chapterStore.payTriggerType == BOOK_PAY_TRIGGER_TYPE.GENERAT_TRAVERSE_CHAPTERS) {

        let coin = 0
        chapterStore.bookUploadTemplateFileList.map((item: { needCoin: number; }) => coin += item.needCoin)
        return {
            ...result,
            docStudyCoins: coin,
            isNeedRecharge: result.needPayCoins > coinBalance.value,
        }
        // 创作本章节
    }
    else if (chapterStore.payTriggerType == BOOK_PAY_TRIGGER_TYPE.CREATE_CHAPTER) {

        let coin = 0
        chapterStore.bookUploadTemplateFileList.map((item: { needCoin: number; }) => coin += item.needCoin)
        return {
            ...result,
            docStudyCoins: coin
        }
        // AI编辑
    } else if (chapterStore.payTriggerType == BOOK_PAY_TRIGGER_TYPE.AI_EDITOR) {
        result.needPayCoins = 10000

    }
    return result
})

const isNeedRechargeCoinsOrVip = computed((): boolean => {

    // 其他会员或团队会员
    return coinBalance.value - payData.value.needPayCoins < 0
})

// 获取编辑器选中文本的公共方法
// const getEditorSelectedText = () => {
//     if (!chapterStore.currentChapter) {
//         message.warning('请先选择一个章节')
//         return null
//     }

//     if (!editor || !editor.value) return null

//     const selection = editor.value.state?.selection
//     if (!selection) return null

//     const from = selection.from
//     const to = selection.to
//     const selectedText = editor.value.view?.state?.doc?.textBetween(from, to)

//     if (selectedText && selectedText.length > 10) {
//         chapterStore.referceText = selectedText
//     }

//     return selectedText
// }


// 重命名充值方法保持不变
const handleRecharge = () => {

    rechargeStore.openRechargeModal(RechargeModalTab.vip, payData.value.needPayCoins + payData.value.docStudyCoins)
}

// 确认创作
const handleCreate = () => {
    chapterStore.showPayModal = false
    chapterStore.bookUploadDataBaseFileList = []
    $eventBus.emit(StarloveConstants.keyOfEventBus.bookEditorCreateAfterConfirmPayModal, chapterStore.payTriggerType)
}


onMounted(async () => {
    const { needCoin, is_free } = await chapterStore.loadChapterCoinData(FILE_COIN_FREE_TYPE.DATABANK)
    if (is_free) {
        handleCreate()
        return
    }
    loading.value = false
    if (needCoin > 0) {
        payDataParams.value.needPayCoins = needCoin
        return
    }

    if (!chapterStore.payTriggerType) {
        return
    }
    if ([
        BOOK_PAY_TRIGGER_TYPE.GENERATE_IMG,
        BOOK_PAY_TRIGGER_TYPE.GENERATE_TABLE,
        BOOK_PAY_TRIGGER_TYPE.GENERATE_CHAT,
        BOOK_PAY_TRIGGER_TYPE.GENERATE_MATH,
        BOOK_PAY_TRIGGER_TYPE.GENERATE_SVG
    ].includes(chapterStore.payTriggerType)) {
        payDataParams.value.needPayCoins = 10000
        return
    }
    if (BOOK_PAY_TRIGGER_TYPE.GENERAT_TRAVERSE_CHAPTERS == chapterStore.payTriggerType) {
        const countChapters = (chapters: any[] | undefined): number => {
            if (!chapters || !Array.isArray(chapters)) return 0;
            return chapters.reduce((total, chapter) => {
                return total + 1 + countChapters(chapter.children);
            }, 0);
        };

        const totalChapters = countChapters(chapterStore.bookValue?.flattened_chapters);
        payDataParams.value.needPayCoins = totalChapters * 20000;
    }
})
</script>