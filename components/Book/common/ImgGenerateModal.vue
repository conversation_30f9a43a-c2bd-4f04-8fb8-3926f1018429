<template>
  <a-modal :open="modelValue" title="AI图片生成" @ok="handleOk" @cancel="handleCancel" :confirmLoading="loading"
    width="800px" cancelText="取消" okText="生成" :zIndex="48">

    <a-form layout="vertical">
      <!-- 引用内容 -->
      <div v-if="chapterStore.referceText" class="my-5 px-5 py-3 bg-blue-50 rounded-lg text-gray-700">
        <div class="text-ellipsis-wrapper">
          <div class="text-ellipsis">{{ chapterStore.referceText }}</div>
        </div>
      </div>
      <div v-else class="flex gap-2 mb-4">
        上下文：
        <a-tag color="blue" class="flex items-center">
          <template #icon>
            <book-outlined />
          </template>
          已包含著作信息
        </a-tag>
        <a-tag color="green" class="flex items-center">
          <template #icon>
            <file-text-outlined />
          </template>
          已包含本章内容
        </a-tag>
      </div>

      <div class="flex justify-start items-center mb-2">
        <span>图片比例：</span>
        <a-radio-group v-model:value="ratio">
          <a-radio-button :value="item" v-for="item in ratioList" :key="item">{{ item }}</a-radio-button>
        </a-radio-group>
      </div>

      <a-form-item>
        <a-textarea v-model:value="defaultPrompt" :rows="8" placeholder="请输入你想要生成的图片内容或补充要求" :maxlength="1000"
          showCount />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { useChapterStore } from '@/stores/chapter';
import { generateImg } from '@/utils/api/generate_img';
import { BOOK_PAY_TRIGGER_TYPE } from '@/utils/constants';
import { BookOutlined, FileTextOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { onBeforeUnmount, onMounted, ref } from 'vue';
import { UserService } from '~/services/user';
const { $eventBus } = useNuxtApp();

const defaultPrompt = ref('')


const chapterStore = useChapterStore()

const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', value: { content: any }): void
}>()

const loading = ref(false)


interface GenerateFormulaResult {
  // formula: string
  // description: string
  content: any
}

const ratioList = ['1:1', '3:4', '4:3', '3:2', '16:9', '9:16']

const ratio = ref(ratioList[2])

const generateImgContent = async (): Promise<GenerateFormulaResult> => {
  if (!chapterStore.currentChapter?.key) {
    message.warning('请先选择章节')
    return { content: [] }
  }

  loading.value = true
  try {
    let user_context = ''
    if (chapterStore.referceText) {
      user_context = `${chapterStore.referceText}, \n 请结合上下文生成一张图片`
    }
    const params: any = {
      chapter_key: chapterStore.currentChapter.key,
      user_prompt: defaultPrompt.value,
      user_context: user_context,
      image_ratio: ratio.value,
      // teamId: chapterStore.teamId
    }

    const response = await generateImg(params)
    if (response.success && response.content) {

      if (response.content.final_files.length) {
        return {
          content: response.content.final_files[0],
        }
      }
      return { content: '' }
    } else {
      message.error('生成图片失败')
      return { content: '' }
    }
  } catch (error) {
    console.error('生成图片失败:', error)
    message.error('生成失败，请重试')
    return { content: '' }
  } finally {
    loading.value = false
  }
}
const sumbitData = async () => {

  const result = await generateImgContent()
  if (!result.content) return

  emit('confirm', {
    content: result.content,
  })
  chapterStore.referceText = ''
  emit('update:modelValue', false)

  await UserService.loadUserInfoAndAssistantMemberInfo()

}
const handleOk = async () => {
  // if (!defaultPrompt.value) {
  //   message.warning('请输入图片生成描述')
  //   return
  // }
  chapterStore.payTriggerType = BOOK_PAY_TRIGGER_TYPE.GENERATE_IMG
  chapterStore.showPayModal = true
}

const handleCancel = () => {
  if (loading.value) {
    message.warning('正在创作中，请勿关闭弹窗')
    return
  }
  chapterStore.referceText = ''
  emit('update:modelValue', false)
  defaultPrompt.value = ''
}

const handleEventConfirmCreate = () => {
  if (chapterStore.payTriggerType != BOOK_PAY_TRIGGER_TYPE.GENERATE_IMG) {
    return
  }
  sumbitData()
}

onMounted(() => {


  $eventBus.on(StarloveConstants.keyOfEventBus.bookEditorCreateAfterConfirmPayModal, handleEventConfirmCreate)
})
onBeforeUnmount(() => {
  defaultPrompt.value = ''
  $eventBus.off(StarloveConstants.keyOfEventBus.bookEditorCreateAfterConfirmPayModal, handleEventConfirmCreate);
});
</script>

<style scoped>
.preview-section {
  margin-top: 16px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.preview-title {
  font-weight: bold;
  margin-bottom: 8px;
}

.preview-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60px;
  padding: 16px;
  background-color: white;
  border-radius: 4px;
}

.text-ellipsis-wrapper {
  position: relative;
  overflow: hidden;
  height: 3em;
}

.text-ellipsis {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  word-break: break-all;
  padding: 0;
  line-height: 1.5;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}
</style>