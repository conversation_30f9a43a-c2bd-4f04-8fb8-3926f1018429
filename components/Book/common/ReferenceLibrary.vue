<!-- 
  参考资料库组件
  功能：
  1. 显示参考资料库
  2. 提供添加、删除参考资料功能
  3. 监听editor:citationClick事件，自动显示参考资料库抽屉
-->
<template>
    <div>
        <!-- 头部添加文件区域 -->
        <div class="overflow-hidden p-[20px] bg-gradient-to-b from-[#E1E6FF] to-[#DBEAFB] rounded-[10px] flex-shrink-0 cursor-pointer"
            @click="openReferenceDrawer">
            <div class="flex items-center justify-between">
                <div class="flex items-center justify-center text-[16px] text-[500]">
                    <Iconfont name="folder" class="mr-2"></Iconfont> 参考资料库
                </div>

                <div class="flex items-center justify-center text-sm text-blue-700 px-3 py-1 cursor-pointer"
                    @click.stop.prevent="handlePressAddFile(BookUploadFileType.bookDataBase)">
                    <plus theme="outline" size="16" /> 添加
                </div>
            </div>

            <div class="mt-[15px] ml-[25px] text-[15px] text-[#333333] h-[20px] cursor-pointer">
                <template v-if="isLoading">
                    <a-spin size="small" />
                </template>
                <template v-else-if="learningCount > 0">
                    正在学习{{ learningCount }}个文件...
                </template>
                <template v-else>
                    已学习{{ referenceData?.completed || 0 }}个文件
                </template>
            </div>

            <!-- 参考资料抽屉 -->
            <a-drawer v-model:open="drawerVisible" title="参考资料库" placement="right" :width="400">
                <template #extra>
                    <a-button class="btn-with-icon" type="primary"
                        @click="handlePressAddFile(BookUploadFileType.bookDataBase)">
                        <plus theme="outline" size="16" /> 添加文件
                    </a-button>
                </template>
                <a-spin :spinning="uploadFileLoading || uploadedFileToBeConfirmedLoading"
                    :tip="`${uploadedFileToBeConfirmedLoading ? '文件确认中...' : '文件读取中...'}`">
                    <div class="reference-list space-y-3 max-h-full overflow-y-auto pr-1">
                        <a-empty
                            v-if="(!referenceData || referenceData?.files.length === 0) && !uploadFileLoading && !uploadedFileToBeConfirmedLoading"
                            description="暂无参考资料" />
                        <template v-else-if="isLoading">
                            <div v-for="i in 3" :key="i" class="mb-3">
                                <a-skeleton active :paragraph="{ rows: 2 }" />
                            </div>
                        </template>

                        <!-- 文件项 -->
                        <div v-else v-for="(item, index) in referenceData?.files" :key="index"
                            class="p-4 bg-white rounded-lg hover:bg-gray-50 mb-3 shadow-sm border border-gray-100">
                            <div class="flex items-start">
                                <!-- 编号标签 -->
                                <a-tag color="blue" class="mr-3 flex-shrink-0 mt-1 min-w-[30px] text-center">
                                    {{ item.reference_no }}
                                </a-tag>

                                <div class="flex-1 overflow-hidden">
                                    <!-- 标题和删除按钮 -->
                                    <div class="flex justify-between items-start w-full mb-2">
                                        <div class="flex-1">
                                            <div class="flex items-start">
                                                <!-- 文件类型图标 -->
                                                <span class="mr-2 text-gray-500 mt-1 flex-shrink-0">
                                                    <img :src="getFileIcon(getFileTypeFromName(item.file_name || ''))"
                                                        :alt="getFileTypeFromName(item.file_name || '')"
                                                        class="w-[18px] h-[18px]">

                                                    <!-- <Iconfont
                                                        :name="getFileIcon(getFileTypeFromName(item.file_name || ''))"
                                                        :size="18" /> -->
                                                </span>
                                                <!-- 文件标题名 -->
                                                <a :href="item.cloud_url" target="_blank"
                                                    class="text-blue-600 hover:text-blue-800 font-medium text-[15px] line-clamp-2 whitespace-normal leading-normal break-all">
                                                    {{ item.file_name || item.citation_line }}
                                                </a>
                                            </div>
                                        </div>

                                        <!-- 删除按钮 -->
                                        <a-popconfirm title="确定要删除这个参考资料吗？" ok-text="确定" cancel-text="取消"
                                            @confirm="handleDeleteReference(item.key)">
                                            <a-button type="primary" danger size="small"
                                                :loading="deletingIds.includes(item.key)"
                                                class="ml-2 flex-shrink-0 btn-with-icon">
                                                <template #icon>
                                                    <DeleteOutlined v-if="!deletingIds.includes(item.key)" />
                                                </template>
                                                <span>删除</span>
                                            </a-button>
                                        </a-popconfirm>
                                    </div>

                                    <!-- 状态和文件类型标签 -->
                                    <div class="flex space-x-2 mt-2 pl-6">
                                        <a-tag v-if="item.status === 'pending'" color="processing">
                                            <template #icon>
                                                <LoadingOutlined />
                                            </template> 学习中
                                        </a-tag>
                                        <a-tag v-else-if="item.status === 'success'" color="success">
                                            <template #icon>
                                                <CheckOutlined />
                                            </template> 已完成
                                        </a-tag>
                                        <a-tag v-else-if="item.status === 'error'" color="error">
                                            <template #icon>
                                                <CloseOutlined />
                                            </template> 失败
                                        </a-tag>

                                        <a-tag v-if="getFileTypeFromName(item.file_name || '')" color="cyan">
                                            {{ getFileTypeFromName(item.file_name || '') }}
                                        </a-tag>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </a-spin>
            </a-drawer>
        </div>

        <file-add-modal
            v-if="chapterStore.showFileAddModal && chapterStore.chapterUploadFileModal == BookUploadFileType.bookDataBase"
            v-model:model-value="chapterStore.showFileAddModal" :uploadFileCount="canUploadFileCount"
            @confirm="onConfirmFileAdd" />
    </div>
</template>

<script setup lang="ts">
import { useNuxtApp } from '#app';
import Iconfont from '@/components/Iconfont.vue';
import { useChapterStore } from '@/stores/chapter';
import { BOOK_PAY_TRIGGER_TYPE, BookUploadFileType } from '@/utils/constants';
import {
    CheckOutlined,
    CloseOutlined,
    DeleteOutlined,
    LoadingOutlined
} from '@ant-design/icons-vue';
import { Plus } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { storeToRefs } from 'pinia';
import { computed, defineExpose, onMounted, onUnmounted, ref } from 'vue';
import { UserService } from '~/services/user';
import { useRechargeStore } from '~/stores/recharge';
import { useTaskStore } from '~/stores/task';
import { useUserStore } from '~/stores/user';
import type { ReferenceResponseData } from '~/types/book';
import { BookService } from '~/utils/api/book';
import { editorBus } from '~/utils/book/editorBus';
import { MAX_REFERENCE_FILE_COUNT } from '~/utils/constants';
import { StarloveConstants } from '~/utils/starloveConstants';
import FileAddModal from '../common/FileAddModal.vue';

const { $eventBus } = useNuxtApp();

const user = useUserStore()
const chapterStore = useChapterStore()
const taskStore = useTaskStore()
const { isGenerating, isTraversing } = storeToRefs(taskStore)
const rechargeStore = useRechargeStore()

const spaceId = computed(() => {

    return user.currentLoginInfo?.id || ''

})


// const teamId = computed(() => {

//     return user.currentLoginInfo?.id || ''

// })


const referenceData = ref<ReferenceResponseData | undefined>(undefined)
const fileList = ref<any[]>([])
const drawerVisible = ref(false)
const isLoading = ref(false)
const canUploadFileCount = ref<number>(0)
// const currentUploadFileType = ref(BookUploadFileType.bookDataBase)
const uploadFileLoading = ref(false)
const uploadedFileToBeConfirmedLoading = ref(false) //上传文件待确认loading
const deletingIds = ref<string[]>([]) // 存储正在删除的参考资料ID
const refreshTimer = ref<number | null>(null) // 存储定时器ID

const knowledgeAssistantMemberInfo = computed(() => {
    return UserService.getKnowledgeAssistantMemberInfo()
})


// 计算"学习中"的文件数量
const learningCount = computed(() => {
    if (!referenceData.value) return 0

    // 使用API返回的in_progress字段作为学习中的文件数量
    return referenceData.value.in_progress || 0
})

// 监听editor:citationClick事件
editorBus.on('editor:citationClick', () => {
    drawerVisible.value = true
})

// 打开参考资料抽屉
const openReferenceDrawer = () => {
    drawerVisible.value = true
}

// 添加文件按钮点击处理
const handlePressAddFile = (type: any) => {
    if (isGenerating.value) {
        message.warning('正在生成章节内容，请等待完成后再上传文件')
        return
    }
    if (isTraversing.value) {
        message.warning('正在批量生成章节内容，请等待完成后再上传文件')
        return
    }
    // currentUploadFileType.value = type

    if (MAX_REFERENCE_FILE_COUNT - (referenceData.value?.files.length || 0) == 0) {
        message.warning('最多只能上传50个文件')
        return
    }
    chapterStore.chapterUploadFileModal = BookUploadFileType.bookDataBase
    canUploadFileCount.value = MAX_REFERENCE_FILE_COUNT - (referenceData.value?.files.length || 0)
    console.log("canUploadFileCount ==>", canUploadFileCount.value)
    chapterStore.showFileAddModal = true
}

// 确认添加文件
const onConfirmFileAdd = async (_list: any[], type: BookUploadFileType) => {
    // console.log("onConfirmFileAdd ==>", type)
    // console.log("_list ==>", _list)
    let fileIds: any[] = []
    let repositoryFileIds: any[] = []

    if (_list.length == 0) {
        return
    }
    if (type != BookUploadFileType.bookDataBase) {
        return
    }
    _list.forEach(d => {
        // 本地上传
        if (d?.fileUrl) {
            fileIds.push(d.id)
        } else {
            // 知识库选择
            repositoryFileIds.push(d.id)
        }
    })
    if (fileIds.length == 0 && repositoryFileIds.length == 0) {
        return
    }

    try {
        if (!drawerVisible.value) {
            drawerVisible.value = true
        }

        uploadFileLoading.value = true
        const res = await BookService.getFilesByIds({
            spaceId: spaceId.value,
            fileIds: fileIds,
            repositoryFileIds: repositoryFileIds,
        })

        if (!res.success || !Array.isArray(res.result)) {
            message.error(res.message || '文件上传失败')
            return
        }
        checkUserCoinAndUpload(res.result || [])
    } catch (error) {
        console.error(error)
    } finally {
        uploadFileLoading.value = false
    }
}

// 检查用户硬币并上传文件
const checkUserCoinAndUpload = async (list: any) => {
    if (list.length == 0) {
        return
    }
    if (!chapterStore.currentBook || !chapterStore.currentBook.key) {
        return
    }

    fileList.value = list
    chapterStore.bookUploadDataBaseFileList = list
    // 普通用户计算上传文件学习费用
    const needCoin = list.reduce((sum: number, d: any) => sum + d.needCoin, 0)
    uploadedFileToBeConfirmedLoading.value = true
    chapterStore.showPayModal = true
    chapterStore.payTriggerType = BOOK_PAY_TRIGGER_TYPE.GENERATE_CHAPTER_ATTACHMENT
    chapterStore.uploadFilesToStudyFees = needCoin
}

// 提交参考资料上传
const submitReferenceUpload = async (list: any) => {
    let attachments: any = []
    list.forEach((d: any) => {
        attachments.push({
            url: d.fileUrl,
            fileId: d.id,
            fileName: d.fileName,
        })
    })
    await BookService.referenceUpload({
        book_key: chapterStore.currentBook?.key || '',
        attachments: attachments
    })
    await fetchReferenceList()
}

// 处理确认上传文件事件
const handleEventConfirmUploadFile = (type: string) => {
    if (type != BOOK_PAY_TRIGGER_TYPE.GENERATE_CHAPTER_ATTACHMENT) {
        return
    }
    uploadedFileToBeConfirmedLoading.value = false
    submitReferenceUpload(fileList.value)
}

// 处理取消上传文件事件
const handleEventConfirmUploadFileCancel = () => {
    fileList.value = []
    uploadedFileToBeConfirmedLoading.value = false
}

// 删除参考资料
const handleDeleteReference = async (key: string) => {
    try {
        const fileIndex = referenceData.value?.files.findIndex(file => file.key === key) ?? -1
        if (fileIndex === -1) return

        // 添加到删除中的ID列表
        deletingIds.value.push(key)

        // 调用删除API
        const success = await BookService.deleteReference(key)

        if (success) {
            message.success('参考资料已删除')
            // 删除成功后重新获取参考资料列表
            await fetchReferenceList()
        } else {
            message.error('删除参考资料失败')
        }
    } catch (error) {
        message.error('删除参考资料时出错')
        console.error('删除参考资料时出错:', error)
    } finally {
        // 从删除中的ID列表移除
        const index = deletingIds.value.indexOf(key)
        if (index !== -1) {
            deletingIds.value.splice(index, 1)
        }
    }
}

// 获取参考资料列表
const fetchReferenceList = async () => {
    const bookKey = chapterStore.currentBook?.key
    if (!bookKey) {
        return
    }

    try {
        const response = await BookService.getReferenceInfo(bookKey)

        if (response.success) {
            referenceData.value = response.data

            // 清除之前的定时器
            if (refreshTimer.value !== null) {
                clearTimeout(refreshTimer.value)
                refreshTimer.value = null
            }

            // 如果有学习中的文件，设置10秒后再次获取
            if (referenceData.value?.in_progress && referenceData.value.in_progress > 0) {
                refreshTimer.value = window.setTimeout(() => {
                    // 静默刷新，不显示loading状态
                    fetchReferenceListSilently()
                }, 10000)
            }
        } else {
            throw new Error(response.message || '获取参考资料失败')
        }
    } catch (error) {
        message.error('获取参考资料列表失败')
        console.error(error)
    }
}

// 静默获取参考资料列表（不显示loading状态）
const fetchReferenceListSilently = async () => {
    if (!chapterStore.currentChapter) {
        return
    }

    // 获取章节所属书的key
    const bookKey = chapterStore.currentBook?.key
    if (!bookKey) {
        return
    }

    try {
        const response = await BookService.getReferenceInfo(bookKey)

        if (response.success) {
            referenceData.value = response.data

            // 清除之前的定时器
            if (refreshTimer.value !== null) {
                clearTimeout(refreshTimer.value)
                refreshTimer.value = null
            }

            // 如果有学习中的文件，设置10秒后再次获取
            if (referenceData.value?.in_progress && referenceData.value.in_progress > 0) {
                refreshTimer.value = window.setTimeout(() => {
                    fetchReferenceListSilently()
                }, 10000)
            }
        }
    } catch (error) {
        console.error('静默获取参考资料列表失败:', error)
    }
}

// 根据文件类型获取对应的图标名
const getFileIcon = (fileType: string): string => {
    // const fileTypeMap: Record<string, string> = {
    //     'pdf': 'pdf',
    //     'doc': 'word',
    //     'docx': 'word',
    //     'xls': 'excel',
    //     'xlsx': 'excel',
    //     'ppt': 'ppt',
    //     'pptx': 'ppt',
    //     'txt': 'text',
    //     'image': 'image',
    //     'png': 'image',
    //     'jpg': 'image',
    //     'jpeg': 'image',
    //     'html': 'html',
    //     'zip': 'zip',
    //     'md': 'markdown',
    // }
    // return fileTypeMap[fileType.toLowerCase()] || 'file'
    switch (fileType.toLowerCase()) {
        case 'pdf':
            return KnowledgeFileIcon.pdf;
        case 'doc':
        case 'docx':
            return KnowledgeFileIcon.doc;
        case 'ppt':
        case 'pptx':
            return KnowledgeFileIcon.ppt;
        case 'img':
        case 'jpg':
        case 'jpeg':
        case 'png':
            return KnowledgeFileIcon.img;
        case 'txt':
        case 'md':
        case 'text':
            return KnowledgeFileIcon.text;
        case 'xlsx':
        case 'csv':
            return KnowledgeFileIcon.xlsx;
        default:
            return KnowledgeFileIcon.encode;
    }
}

// 根据文件名获取文件类型
const getFileTypeFromName = (fileName: string): string => {
    if (!fileName) return ''

    const extension = fileName.split('.').pop()?.toLowerCase() || ''

    // 返回文件扩展名作为类型
    return extension
}

// 初始化
onMounted(async () => {
    // console.log('ReferenceLibrary初始化 ==>', chapterStore.bookValue?.key)
    isLoading.value = true
    await fetchReferenceList()
    isLoading.value = false

    $eventBus.on(StarloveConstants.keyOfEventBus.bookEditorCreateAfterConfirmPayModal, handleEventConfirmUploadFile)
    $eventBus.on(StarloveConstants.keyOfEventBus.bookEditorCreateAfterConfirmPayCancelModal, handleEventConfirmUploadFileCancel)
})

// 卸载
onUnmounted(() => {
    // console.log('ReferenceLibrary卸载')
    if (refreshTimer.value !== null) {
        clearTimeout(refreshTimer.value)
        refreshTimer.value = null
    }

    // 移除事件监听
    editorBus.off('editor:citationClick')
    $eventBus.off(StarloveConstants.keyOfEventBus.bookEditorCreateAfterConfirmPayCancelModal, handleEventConfirmUploadFileCancel)
    $eventBus.off(StarloveConstants.keyOfEventBus.bookEditorCreateAfterConfirmPayModal, handleEventConfirmUploadFile)
})

// 暴露接口
defineExpose({

    learningCount
});
</script>

<style scoped>
.btn-with-icon {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.btn-with-icon :deep(.anticon) {
    display: flex !important;
    align-items: center !important;
    margin-right: 4px;
}
</style>