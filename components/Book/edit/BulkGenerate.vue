<template>
    <div class="flex gap-2 flex-1 ">
        <a-button class="bg-[#2551B5] flex-1 btn-with-icon" :loading="taskStore.isTraversing" block
            @click="handlePressTranversing" type="primary" style="background: #2551B5;">
            生成剩余全部节点
        </a-button>
    </div>
</template>

<script setup lang="ts">
import { StarloveConstants } from '@/utils/starloveConstants'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { message } from 'ant-design-vue'
import { onMounted, onUnmounted, ref } from 'vue'
import { useRouter } from 'vue-router'

import { Modal } from 'ant-design-vue'
import { UserService } from '~/services/user'
import { useChapterStore } from '~/stores/chapter'
import { useTaskStore } from '~/stores/task'
import { API_CONFIG } from '~/utils/book/config'
import { TaskAcitonEnum } from '~/utils/enums/enums'

const { $eventBus } = useNuxtApp();

const props = defineProps({
    learningCount: {
        type: Number,
        required: true
    }
})


// 添加AbortController引用
let controller: AbortController | null = null

// 初始化路由
const router = useRouter()

const taskStore = useTaskStore()
const chapterStore = useChapterStore()

console.log('BulkGenerate组件初始化')

// 用户输入的提示内容
const userPrompt = ref("")

let retryCount = 0;
const maxRetries = 5; // 最大重试次数
const retryInterval = 10 * 1000; // 重连间隔时间（毫秒）


const startFetchEventSource = async (params: any, action: TaskAcitonEnum) => {
    // console.log('开始生成章节，用户提示:', userPrompt.value)

    let url;
    if (action == TaskAcitonEnum.START) {
        url = `${API_CONFIG.getBaseUrl()}/api/sse/auto_generate_book/`
    } else if (action == TaskAcitonEnum.CONTINUE) {
        url = `${API_CONFIG.getBaseUrl()}/api/sse/continue_generate_book/`
    }
    console.log('开始生成章节，用户提示:', userPrompt.value)

    // 添加批量生成开始消息
    // if (chapterStore.currentBook?.key) {
    //     taskStore.addBookStatusMessage('开始一键创作', chapterStore.currentBook.key);
    // }

    taskStore.setIsTraversing(true)
    taskStore.setTraverseProgress(0)
    taskStore.setIsGenerating(true)

    console.log('请求参数:', params)

    // 如果有正在进行的请求，取消它
    if (controller) {
        console.log('取消之前的请求')
        controller.abort()
    }

    // 创建新的 AbortController
    controller = new AbortController()
    console.log('创建新的请求控制器')

    setTimeout(() => {
        UserService.loadUserInfoAndAssistantMemberInfo()
    }, 2000)

    try {
        console.log('开始调用SSE接口')
        // 使用 fetchEventSource 订阅 SSE 事件
        await fetchEventSource(`${url}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'token': `Bearer ${UserService.getToken()}`
            },
            body: JSON.stringify(params),
            signal: controller.signal,
            credentials: 'include',
            openWhenHidden: true,

            onmessage: (event) => {
                taskStore.processSSEMessage(event)


            },

            onclose: () => {
                if (taskStore?.isGenerating) {
                    throw new Error('SSE连接关闭，准备重试')
                } else {
                    taskStore.setIsGenerating(false)
                    taskStore.setIsTraversing(false)
                    // 连接关闭时的处理
                    console.log('SSE连接已关闭')
                    controller = null
                }

                // 添加批量生成完成消息
                // if (chapterStore.currentBook?.key) {
                //     taskStore.addBookStatusMessage('一键创作已完成', chapterStore.currentBook.key);
                // }
            },

            onerror: (err) => {
                // taskStore.setIsGenerating(false)
                // taskStore.setIsTraversing(false)

                // 错误处理
                console.error('SSE 连接错误:', err)
                // message.error('生成过程中发生错误，请重试')



                // 添加错误消息
                // if (chapterStore.currentBook?.key) {
                //     taskStore.addBookStatusMessage('生成过程中发生错误，请重试', chapterStore.currentBook.key);
                // }

                controller = null

                // 抛出错误来终止连接，防止重复重试
                throw err
            }
        })
        console.log('SSE请求完成')
    } catch (e) {
        if (retryCount < maxRetries) {
            retryCount++;
            console.log(`${retryInterval}s后，重试第 ${retryCount} 次...`);
            setTimeout(() => {
                // 重新开始请求前，加载后台数据
                chapterStore.loadChapter(chapterStore.currentChapter?.key || '')
                taskStore.setIsGenerating(false)
                startFetchEventSource(params, action);
            }, retryInterval);
        } else {
            retryCount = 0
            taskStore.setIsGenerating(false)
            taskStore.setIsTraversing(false)

            console.error('SSE请求异常:', e)
            // if (e instanceof Error && e.name !== 'AbortError') {
            //     console.error('SSE请求失败:', e.message)
            //     message.error('生成请求失败，请重试')
            // } else {
            //     console.log('请求被中止或其他异常')
            // }
            Modal.confirm({
                title: '生成过程中发生错误，请重试',
                content: '请刷新页面',
                okText: '刷新页面',
                okButtonProps: {
                    type: 'primary',
                    danger: true
                },
                onOk: () => {
                    //刷新页面
                    window.location.reload()
                }
            })
        }

    }
}

const handlePressTranversing = async () => {
    if (props.learningCount > 0) {
        message.warning('资料库中所有文件学习完成后才可以开始生成')
        return
    }
    if (taskStore.isGenerating) {
        message.warning('正在生成章节内容，请等待完成后再切换章节')
        return
    }
    if (taskStore.isTraversing) {
        message.warning('正在批量生成章节内容，请等待完成后再切换章节')
        return
    }

    try {
        message.loading('正在计算章节生成费用', 0)
        chapterStore.payTriggerType = BOOK_PAY_TRIGGER_TYPE.GENERAT_TRAVERSE_CHAPTERS
        const { needCoin, is_free } = await chapterStore.loadChapterCoinData(FILE_COIN_FREE_TYPE.TEMPLATE)
        message.destroy()
        if (is_free) {
            chapterStore.showPayModalByType(BOOK_PAY_TRIGGER_TYPE.GENERAT_TRAVERSE_CHAPTERS)
            return
        }
        if (needCoin == 0) {
            message.warning('已生成全部节点')
            chapterStore.payTriggerType = undefined
            return
        }
        chapterStore.showPayModalByType(BOOK_PAY_TRIGGER_TYPE.GENERAT_TRAVERSE_CHAPTERS)
    } catch (error) {
        message.destroy()
        message.error('计算章节生成费用失败')
    }
}

const startWritingBooks = () => {
    if (chapterStore.payTriggerType != BOOK_PAY_TRIGGER_TYPE.GENERAT_TRAVERSE_CHAPTERS) {
        return
    }
    // 构造请求参数
    const params = {
        book_key: chapterStore.currentBook?.key,
        user_prompt: chapterStore.userEditCustomPrompt,
        spaceId: chapterStore.spaceId,
        // teamId: chapterStore.teamId,
        file_urls: (chapterStore.bookUploadTemplateFileList || []).map(item => item?.fileUrl),
        fileIds: (chapterStore.bookUploadTemplateFileList || []).map(item => item?.id)
    }
    startFetchEventSource(params, TaskAcitonEnum.START)

    chapterStore.userEditCustomPrompt = ''
}

watch(() => chapterStore.bookValue, (newValue, oldValue) => {
    console.log('chapterStore.bookValue newValue', newValue)

    if (newValue && newValue.is_generating) {
        const params = {
            book_key: chapterStore.bookValue?.key,
            chapter_key: chapterStore.bookValue?.generating_chapter_key,
        }
        startFetchEventSource(params, TaskAcitonEnum.CONTINUE)
    }
})

onMounted(() => {
    console.log('BulkGenerate组件挂载')
    $eventBus.on(StarloveConstants.keyOfEventBus.bookEditorCreateAfterConfirmPayModal, startWritingBooks)

})

// 组件销毁时清除资源
onUnmounted(() => {
    console.log('BulkGenerate组件卸载')
    // 关闭可能存在的消息通知
    message.destroy()

    // 取消进行中的请求
    if (controller) {
        console.log('取消未完成的SSE请求')
        controller.abort()
        controller = null
    }
    taskStore.setIsGenerating(false)
    taskStore.setIsTraversing(false)
    taskStore.setTraverseProgress(0)

    $eventBus.off(StarloveConstants.keyOfEventBus.bookEditorCreateAfterConfirmPayModal, startWritingBooks)
})
</script>