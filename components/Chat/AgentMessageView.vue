<template>
  <div class="message-view-new">
    <div :class="{
      'message-item': true,
      'message-item-mine': message.senderId != 'AI',
      'message-item-ai': message.senderId == 'AI'
    }">
      <div class="message-content">
        <div class="relative" v-if="message.senderId === 'AI'">
          <div class="relative" v-if="isShowCompleteAnswered">
            <div class="absolute top-0 right-3" v-if="isShowReferenceSource">
              <button @click="handleReferenceSource"
                class="flex items-center justify-center px-3 py-1.5 text-gray-500 bg-gray-50  rounded-lg cursor-pointer hover:text-blue-500 ">
                <Iconfont v-for="item in referenceIconList" :name="item" :size="15" class="mr-2"></Iconfont>
                <span class="pr-1 text-sm ">参考来源</span>
                <right :size="14" />
              </button>
            </div>
            <a-collapse :bordered="false" :defaultActiveKey="[message.id]" :style="{ background: '#fff' }"
              expandIconPosition="end">
              <template #expandIcon="props">
                <caret-right-outlined :rotate="props?.isActive ? 90 : 0" />
              </template>
              <a-collapse-panel :key="message.id" :style="customStyle">
                <template #header>
                  <div style="padding: 0 !important">

                    <template v-if="message.status != LastMessageStatus.error">
                      <span v-if="message.status == LastMessageStatus.done">已完成回答</span>
                      <span v-else-if="message.status == LastMessageStatus.answeing">正在回答中...</span>
                      <span v-else-if="message.status == LastMessageStatus.ing">{{ timeLineList[0]?.content ||
                        '正在回答中...' }}</span>
                    </template>
                  </div>
                </template>
                <div class="flex px-1 pt-[11px]">
                  <div class="text-gray-500 ">
                    <a-timeline :pending="message.status == LastMessageStatus.answeing ? '生成回答中...' : ''">
                      <a-timeline-item color="gray" v-for="(item, timeLineIndex) in timeLineList"
                        :key="item?.item_id ? `${item?.item_id}_cc` : `item-line-${timeLineIndex}`">
                        <template #dot>
                          <dot color="text-gray-400" />
                        </template>
                        <div v-if="item?.type == LastAgentMessageStatus.text_delta" class="text-gray-500 ">
                          <RenderMessageContent :content="item.delta" :is-my-self="false"></RenderMessageContent>
                          <template v-if="item.children">
                            <RenderMessageItem v-for="child in item.children" :key="child.item_id" :item="child">
                            </RenderMessageItem>
                          </template>
                        </div>
                        <div class="text-gray-500 " v-else-if="item?.type == LastAgentMessageStatus.text_done">
                          <!--判断item_id 是final_answer 开头的不要渲染 item.content 显示生成回答-->
                          <div class="text-gray-800 " v-if="item.item_id.startsWith('final_answer')">生成回答{{
                            message.status ==
                              LastMessageStatus.answeing ?
                              '...' : '' }}</div>
                          <RenderMessageContent v-else :content="item.content" :is-my-self="false">
                          </RenderMessageContent>

                          <template v-if="item.children">
                            <RenderMessageItem v-for="child in item.children" :key="child.item_id" :item="child">
                            </RenderMessageItem>
                          </template>
                        </div>
                        <div
                          v-else-if="[LastAgentMessageStatus.think_delta, LastAgentMessageStatus.think_done].includes(item?.type || '')">
                          <p class="text-gray-500 ">深度思考</p>
                          <div class="text-gray-500 " v-if="item?.type == LastAgentMessageStatus.think_delta">
                            <RenderMessageContent :content="item.delta" :is-my-self="false"></RenderMessageContent>
                          </div>
                          <div class="text-gray-500 " v-else-if="item?.type == LastAgentMessageStatus.think_done">
                            <RenderMessageContent :content="item.content" :is-my-self="false"></RenderMessageContent>
                          </div>
                        </div>
                        <div v-else>
                          {{ item }}
                        </div>
                      </a-timeline-item>
                    </a-timeline>
                  </div>

                </div>
              </a-collapse-panel>
            </a-collapse>
          </div>
          <!-- <div class="message-item-content1"
            v-if="message.status == LastMessageStatus.error && timeLineList.length > 0">
            {{ timeLineList[0].content }}
          </div> -->
          <!---如果是对文件提问，则需要显示指定来源 -->
          <div v-if="isShowKnowledge" class="absolute top-[-5px] flex justify-end right-3"><span
              @click="handleReferenceSource"
              class="flex items-center px-2 py-1 mt-2 mr-3 text-sm rounded-lg cursor-pointer hover:text-blue-500 bg-gray-50 ">指定来源
              <right :size="14" />
            </span>

          </div>
          <RenderMessageContent :content="finalAnswer" :is-my-self="false" :class="{
            'message-item-content': true,
            'message-item-contentf': true,
          }">
          </RenderMessageContent>
        </div>

        <template v-if="message.senderId !== 'AI'">
          <RenderMyMessageContent :answer-content="answer" class="message-item-content">
          </RenderMyMessageContent>
        </template>
        <div v-if="message.senderId == 'AI' &&
          props.isLastMessageRecord &&
          props.isAsking &&
          message.status != LastMessageStatus.answeing && message.status != LastMessageStatus.thinking
        " style="margin-left: 12px; padding: 0 15px 15px 15px">
          <LoadingOutlined style="color: #42e5b5; filter: drop-shadow(0 0 5px #249cff); font-size: 15px" />
        </div>

        <MessageTools :is-share="isShare" :message="message" v-if="isShowMessageTools"></MessageTools>

      </div>

      <MessageRecommendApply v-if="isShowRecommendApply" :recommendApplyList="recommendApplyList"
        :isKnowledge="isKnowledge">
      </MessageRecommendApply>
    </div>

    <!-- <div class="clear-hint-text" v-if="isShowClearContextText">——已清除上下文，请开启新话题——</div> -->
  </div>
</template>

<script setup lang="ts">
import type { AppCategory, AppMessage, AppMessageRecord } from '@/services/types/appMessage'
import { useChatStore } from '@/stores/chat'
import { LastAgentMessageStatus, LastMessageStatus } from '@/utils/constants'
import asyncLoadJs from '@/utils/loadResources'
import { isJSON, sleep } from '@/utils/utils'
import { Dot, Right } from '@icon-park/vue-next'

import { CaretRightOutlined, LoadingOutlined } from '@ant-design/icons-vue'
import markdownItKatex from '@vscode/markdown-it-katex'
import copy from 'copy-text-to-clipboard'
import hljs from 'highlight.js/lib/core'
import 'highlight.js/styles/ir-black.css'
import 'katex/dist/katex.min.css'
import markdownit from 'markdown-it'
import markdownItContainer from 'markdown-it-container'
import markdownItHighlightjs from 'markdown-it-highlightjs'
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import { ToastService } from '~/services/toast'
import type { AgentStreamData } from '~/services/types/agent'
import MessageRecommendApply from './MessageRecommendApply.vue'
import MessageTools from './MessageTools.vue'
import RenderMessageContent from './RenderMessageContent.vue'
import RenderMessageItem from './RenderMessageItem.vue'
import RenderMyMessageContent from './RenderMyMessageContent.vue'

const { $eventBus } = useNuxtApp();

const { knowdedgeList } = storeToRefs(useChatStore());
// 是否显示指定来源
const isShowKnowledge = computed(() => {
  if (!props.currentSessionInfo?.id) {
    return false;
  }
  if (!props.currentSessionInfo.fileIds) {
    return false;
  }
  if (knowdedgeList.value.length < 1) {
    return false;
  }
  return true;
});
declare global {
  interface Window {
    exportToExcelMD: (tableId: string) => Promise<void>;
    copyMD: (codeId: string) => Promise<void>;
    XLSX: any;
  }
}

interface Props {
  message: AppMessage
  isAsking: boolean
  isKnowledge?: boolean
  // isShowClearContextHint?: boolean
  isLastMessageRecord: boolean
  currentIsClickedClearButton: boolean
  messageRecommendApplyData?: AppCategory[]
  isFirstAnswer?: boolean,
  isShare?: boolean
  currentReferenceSourceMessageId?: string
  sessionFrom?: string
  currentSessionInfo?: AppMessageRecord | null
}

const props = withDefaults(defineProps<Props>(), {
  isShare: false,
  currentReferenceSourceMessageId: '',
  sessionFrom: SessionFrom.Normal,
  currentSessionInfo: null
})
const customStyle = 'background: #fff;border-radius: 10px;margin: 10px 10px 0 10px;'
const chat = useChatStore()
const { openReferenceSource } = storeToRefs(useChatStore())
let lastHeading = 'table-data'

const isShowMessageTools = computed(() => {
  if (props.isLastMessageRecord && props.isAsking) {
    return false
  }
  return true
})


const isShowRecommendApply = computed(() => {
  // if (props.isAsking) {
  //   return false
  // }
  if (props.isAsking && props.isLastMessageRecord) {
    return false
  }
  if (recommendApplyList.value.length == 0) {
    return false
  }
  return true
})

const understands = computed(() => {
  if (!props.isFirstAnswer) {
    return []
  }
  if (!props.message.understands) {
    return []
  }
  return props.message.understands.filter((item) => item)
})
const answerRefs = computed(() => {
  if (!answer.value) {
    return []
  }
  if (!answer.value.refs) {
    return []
  }
  return answer.value.refs
})

const refs = computed(() => {
  let data
  if (props.message.refs) {
    data = props.message.refs || []
  } else {
    data = answerRefs.value || []
  }
  const list = data.filter((item: any) => item.snippet || (item.meta && item.meta.page))
  return list || []
})

const thinkCollapseActiveKey = ref<string[]>(['2'])

// 监听消息状态变化
// watch(() => props.message.status, (newStatus) => {
//   if (newStatus === LastMessageStatus.thinking) {
//     thinkCollapseActiveKey.value = ['2']  // 思考中状态，设置面板的 key，使其展开
//   }
// }, { immediate: true })

// 监听思考内容变化
watch(() => props.message.think, (newVal) => {
  if (newVal) {
    nextTick(() => {
      $eventBus.emit(StarloveConstants.keyOfEventBus.chatScrollToBottom)
    })
  }
})

const refsCount = computed(() => {
  if (!props.message) {
    return 0
  }
  return refs.value.length
})

const recommendApplyList = computed(() => {
  if (!props.message) {
    return []
  }
  return props.message.recommendApplyList || []
})
const replyMessage = computed(() => {
  // props.message.replyMessage 是的类型是对象 直接返回
  if (props.message?.replyMessage && typeof props.message.replyMessage === 'object') {
    return props.message.replyMessage
  }
  if (props.message.mediaType === 'json') {
    const obj = JSON.parse(props.message?.content || '{}')
    if (obj?.events) {

      const list = obj.events
      const keyMap = new Map();
      list.forEach((item: any) => {
        const key = `${item.item_id}`; // 拼接多个字段为唯一键
        if (keyMap.has(key)) {
          // 如果已存在该key，则合并新旧值
          const existingItem = keyMap.get(key);
          // 策略1: 浅合并对象属性（适用于大多数情况）
          const mergedItem = { ...existingItem, ...item };
          keyMap.set(key, mergedItem);
        } else {
          // 如果不存在，则直接设置
          keyMap.set(key, item);
        }
      });
      const uniqueData = Array.from(keyMap.values());
      console.log('uniqueData ==>', uniqueData)
      return uniqueData
    }
  } else {
    if (props.message.senderId == 'AI') {
      return [{
        id: '1',
        type: LastAgentMessageStatus.text_done,
        content: props.message.content,
        item_id: `final_answer.chat${props.message.id}.chat`,
      }]
    }
  }
  return []
})
const timeLineList = computed(() => {
  const uniqueData = replyMessage.value.filter((item: any) => item?.type != LastAgentMessageStatus.conversation_started)
    .filter((item: any) => item?.type != LastAgentMessageStatus.conversation_completed)
    .filter((item: any) => item?.type != LastAgentMessageStatus.message)
    .filter((item: any) => !item?.item_id.startsWith('final_answer.chat'))
  let result: any = [];
  let idx = -1
  uniqueData.forEach((item: any) => {
    if (item?.type && item.type.startsWith('tool')) {
      // 如果是工具，写入到上一个 item 的 children 中
      if (result[idx]) {
        if (!result[idx]?.children) {

          result[idx].children = [item]
        } else {
          // 判断item.item_id 是否已经存在于 children 中
          if (!result[idx].children.some((child: any) => child.item_id === item.item_id)) {
            result[idx].children.push(item)
          }
        }
      }
    } else {
      result.push(item)
      idx++
    }
  });
  return result
})
// 最终答案
const finalAnswer = computed(() => {
  // if (props.message.status != LastAgentMessageStatus.done) {
  //   return ''
  // }
  const list = replyMessage.value.filter((item: any) => item?.item_id && item?.item_id.startsWith('final_answer.chat'))
  if (!list.length) {
    return ''
  }
  const item = list[list.length - 1]
  return item?.content || item?.delta || ''
})
// 是否显示已完成回答
const isShowCompleteAnswered = computed(() => {
  return props.message.status != LastMessageStatus.error && (props.message.status == LastMessageStatus.done || props.message.status == LastMessageStatus.answeing || props.message.status == LastMessageStatus.ing) && timeLineList.value.length > 0
})
// 是否显示参考来源
const isShowReferenceSource = computed(() => {
  if (props.isKnowledge) {
    return false
  }
  if (props.sessionFrom == SessionFrom.Normal && props.currentSessionInfo?.id && !props.currentSessionInfo.fileIds) {
    const list = replyMessage.value.filter((item: any) => [AgentToolName.repo_file_block, AgentToolName.repo_search_block, AgentToolName.repo_search_fileinfo, AgentToolName.web_search, AgentToolName.scholar_search].includes(item?.tool || ''))
    if (list.length > 0) {
      let count = 0;
      list.forEach((item: AgentStreamData) => {
        count += item?.result?.count || 0;
      });
      if (count > 0) {
        return true
      }
    }
  }
  return false
})
// 显示参考来源 icon 列表
const referenceIconList = computed(() => {
  const keyMap = new Map();
  const list = replyMessage.value.filter((item: any) => item?.type.startsWith('tool'))
  list.forEach((item: any) => {
    if (item.result?.type == MessageResultType.WEB) {
      keyMap.set('lianwang1', 'lianwang1');
    } else if (item.result?.type == MessageResultType.REPO_FILE) {
      keyMap.set('wodezhishiku1', 'wodezhishiku1');
    } else if (item.result?.type == MessageResultType.SCHOLAR) {
      keyMap.set('xueshuwenzhang', 'xueshuwenzhang');
    }
  })
  return Array.from(keyMap.values());
})
// 获取 type 是text_delta 最后一个节点的序号
const getLastTextDeltaIndexByMessage = computed(() => {
  let index = -1;
  replyMessage.value.forEach((item: any, _index: number) => {
    if (item?.type == LastAgentMessageStatus.text_done) {
      index = _index;
    }
  });
  return index;
})
watch(replyMessage, (newVal) => {
  if (props.currentSessionInfo?.id && !props.currentSessionInfo.fileIds) {
    if (props.isLastMessageRecord && props.sessionFrom == SessionFrom.Normal) {
      // 判断不是指定文件提问，才写入
      const list = (newVal || []).filter((item: any) => [AgentToolName.repo_file_block, AgentToolName.repo_search_block, AgentToolName.repo_search_fileinfo, AgentToolName.web_search, AgentToolName.scholar_search].includes(item?.tool || ''))
      chat.setToolReplyMessage(list)
      if (list.length > 0) {
        let count = 0;
        list.forEach((item: AgentStreamData) => {
          count += item?.result?.count || 0;
        });
        if (count > 0) {
          chat.openReferenceSource = true
        }
      }
    }
  }

}, { immediate: true })
const answer = computed(() => {
  if (!props.message || !props.message.content) {
    return
  }
  // console.log('props.message.content ==>', props.message.content)
  if (isJSON(props.message.content)) {
    const data = JSON.parse(props.message.content)
    // const regex = /\[\[(.*?)\]\]\((.*?)\)/g
    const contentText = data.content
    return {
      ...data,
      content: contentText
    }
  }
  let data = props.message.content //.replace(/\*\*/g, '').replace(/\\n/g, '<br/>')
  // console.log('darta 1234232 2343 ==>', data)
  return { content: data }
})

const hasStartThinkTag = computed(() => {
  const startTag = '<think>'
  if (props.message.think) {
    return true
  }
  if (answer.value?.content.includes(startTag)) {
    return true
  }
  return false
})

const hasEndThinkTag = computed(() => {
  const endTag = '</think>'
  if (props.message.think?.includes(endTag)) {
    return true
  }
  if (answer.value?.content.includes(endTag)) {
    return true
  }
  return false
})



const mdSelf = markdownit({
  html: true, // 启用 HTML 标签
  linkify: true, // 自动将 URL 转换为链接
  breaks: true
})
const md = markdownit({
  html: true, // 启用 HTML 标签
  linkify: true, // 自动将 URL 转换为链接
  breaks: true,
  xhtmlOut: true,
  langPrefix: 'language-',
  typographer: true,
  highlight: function (str: string, lang: string): string {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return `<pre class="hljs" data-lang="${lang}"><code>${hljs.highlight(lang, str, true).value
          }</code></pre>`
      } catch (__) { }
    }

    return `<pre class="hljs" data-lang="${lang}"><code>${md.utils.escapeHtml(str)}</code></pre>`
  }
})
  .use(markdownItKatex)
  .use(markdownItContainer, 'spoiler', {
    validate: function (params: string) {
      return params.trim().match(/^spoiler\s+(.*)$/)
    },
    render: function (tokens: any, idx: number) {
      var m = tokens[idx].info.trim().match(/^spoiler\s+(.*)$/)
      if (tokens[idx].nesting === 1) {
        return '<details><summary>' + md.utils.escapeHtml(m[1]) + '</summary>\n'
      } else {
        return '</details>\n'
      }
    }
  })
  .use(markdownItHighlightjs)
  .use((md) => {
    // 添加一个计数器来跟踪表格数量
    let tableCounter = 0;

    // 拦截标题的解析
    const defaultHeadingRender =
      md.renderer.rules.heading_open ||
      function (tokens, idx, options, env, self) {
        return self.renderToken(tokens, idx, options)
      }

    md.renderer.rules.heading_open = function (tokens, idx, options, env, self) {
      // 获取标题内容
      const nextToken = tokens[idx + 1]
      if (nextToken && nextToken.type === 'inline') {
        lastHeading = nextToken.content // 保存标题内容
      }
      return defaultHeadingRender(tokens, idx, options, env, self)
    }

    // 重置表格计数器的函数
    const resetTableCounter = () => {
      tableCounter = 0;
    };

    // 在每次渲染开始时重置计数器
    const defaultCoreRender = md.core.process;
    md.core.process = function (state) {
      resetTableCounter();
      return defaultCoreRender.call(this, state);
    };

    const defaultRender =
      md.renderer.rules.table_open ||
      function (tokens, idx, options, env, self) {
        return self.renderToken(tokens, idx, options)
      }

    md.renderer.rules.table_open = function (tokens, idx, options, env, self) {
      // 增加计数器并生成唯一的表格ID
      tableCounter++;
      const uniqueTableId = `table-${Date.now()}-${tableCounter}`;

      // 在渲染表格时，为表格前添加一个小图标
      return `
        <div class="table-wrapper1">
          <span class="table-icon" onclick="exportToExcelMD('${uniqueTableId}')">
          <img class="table-icon-img" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAASVJREFUWEftllESgyAMRMGLVQ8j4y1ab+HgYerJpI0jnUCDBqR12oEff5B9rskSKU5e8mR98dsASqkbOKi1Xp4pK9mBVfwKosaYaRzH5msAWNyKpkJEO0CJH4GIAvDE+6f7r18gpaxTfgcbwBeHwlNKGRCtqqqZ5xkAomuCBUCJgzAGGIZhSilMFkDbtvfV4h63nA+wQkFLLk5orXfP391gC6zruhq+ErcaBbC1n2pTNgD18hYANxMKQHGgOOA4AL0Okcq932PbkMoSB8BGKWS7HzpHc8Ce7V/bDkAockOhAocaYy6cYeQjANy0w3dEVgf+FyB1vttyhFUDxMgV43JwLxSqHdmEEM5M8ZaEqBOyiONDKGfJKLbtlZuAatdDd0EOwALwAPKYDzBALxiRAAAAAElFTkSuQmCC"> 导出为表格</span>
          <div class="table-wrapper" data-table-id="${uniqueTableId}" data-table-name="${lastHeading}">
          ${defaultRender(tokens, idx, options, env, self)}
      `
    }

    const defaultClose =
      md.renderer.rules.table_close ||
      function (tokens, idx, options, env, self) {
        return self.renderToken(tokens, idx, options)
      }

    md.renderer.rules.table_close = function (tokens, idx, options, env, self) {
      return `${defaultClose(tokens, idx, options, env, self)}</div>`
    }
  })
// 自定义渲染器显示语言名称
md.renderer.rules.fence = (tokens, idx, options, env, self) => {
  const token = tokens[idx]
  const language = token.info.trim() || 'plaintext' // 获取语言名称
  const code = token.content
  let highlighted = ''
  try {
    highlighted = language
      ? hljs.highlight(code, { language: language }).value
      : md.utils.escapeHtml(code)
  } catch (error) {
    highlighted = md.utils.escapeHtml(code)
  }
  const codeId = `code-${idx}`
  return `
        <div class="code-block">
          <div class="code-language">
            <div>${language}</div>
            <div class="copy-code" onclick="copyMD('${codeId}')">复制</div>
          </div>
          <pre><code data-code-id="${codeId}" class="hljs ${md.utils.escapeHtml(
    language
  )}">${highlighted}</code></pre>
        </div>
      `
}
const defaultRender =
  md.renderer.rules.link_open ||
  function (tokens, idx, options, env, self) {
    return self.renderToken(tokens, idx, options)
  }

md.renderer.rules.link_open = function (tokens: any, idx: number, options: any, env: any, self: any) {
  // 如果链接没有 target 属性，则添加 target="_blank"
  const aIndex = tokens[idx].attrIndex('target')
  if (aIndex < 0) {
    tokens[idx].attrPush(['target', '_blank']) // 添加新的属性
  } else {
    tokens[idx].attrs[aIndex][1] = '_blank' // 替换现有属性的值
  }
  // 传递 token 给默认渲染器
  return defaultRender(tokens, idx, options, env, self)
}

const thinkContent = computed(() => {
  // console.log("thinkContent  ==>", props.message.think)
  if (props.message.think) {
    return md.render(props.message.think)
  }
  try {
    if (!answer.value) {
      return ''
    }
    const content = answer.value?.content;

    const match = content.match(/<think>([\s\S]*?)(?:<\/think>|$)/);
    // console.log("match  ==>", match)
    const newData = match ? match[1] : ""
    return md.render(newData)
  } catch (error) {
    return ''
    // console.error("JSON 解析错误:", error);
  }
})

const questionContent = computed(() => {
  return mdSelf.render(answer.value.content)
})

const answerContent = computed(() => {
  if (!answer.value) {
    return
  }
  if (answer.value.files) {
    return answer.value
  }
  // 如果是自己发的消息，不做走别的markdown解析
  if (props.message.senderId !== 'AI') {
    return mdSelf.render(answer.value.content)
  }

  const newData = answer.value.content
    .replace(/<think>[\s\S]*?<\/think>/g, '')
    .replace(/\\\{/g, '${')
    .replace(/\\\}/g, '}$')
    .replace(/\\\[/g, '$[')
    .replace(/\\\]/g, ']$')
    .replace(/\\\(/g, '$(')
    .replace(/\\\)/g, ')$')
    .replace(/#{2,}/g, (match: any) => match + ' ');
  // .replace(/\\text\{([^}]*)\}/g, '$1')
  // console.log('newData ==>', newData.length)
  if ((!newData || newData.trim().length == 0) && props.message.status != LastMessageStatus.thinking) {
    return thinkContent.value
  }
  return md.render(newData)
})
const renderMdToHtml = (content: string) => {
  const newData = content
    .replace(/<think>[\s\S]*?<\/think>/g, '')
    .replace(/\\\{/g, '${')
    .replace(/\\\}/g, '}$')
    .replace(/\\\[/g, '$[')
    .replace(/\\\]/g, ']$')
    .replace(/\\\(/g, '$(')
    .replace(/\\\)/g, ')$')
    .replace(/#{2,}/g, (match: any) => match + ' ');
  return md.render(newData)
}
const renderUnder = (dd: string) => {
  return md.render(dd)
}



const handleReferenceSource = () => {
  const list = replyMessage.value.filter((item: any) => [AgentToolName.repo_file_block, AgentToolName.repo_search_block, AgentToolName.repo_search_fileinfo, AgentToolName.web_search, AgentToolName.scholar_search].includes(item?.tool || ''))
  chat.setToolReplyMessage(list)
  openReferenceSource.value = true
}

onMounted(() => {
  // console.log('message 123 ==>', props.message)
  // console.log('props.isAsking ==>', props.isAsking && props.isLastMessageRecord)
  window['exportToExcelMD'] = async (tableId) => {
    let XLSX = window['XLSX']
    // Taro.showLoading({ title: '导出下载表格中...' })
    if (!XLSX) {
      await asyncLoadJs('https://static-1256600262.file.myqcloud.com/lib/excel/xlsx.full.min.js')
      await sleep(1000)
      XLSX = window['XLSX']
    }
    try {
      const tableWrapper = document.querySelector(`[data-table-id="${tableId}"]`)
      if (!tableWrapper) {
        // Taro.hideLoading()
        return
      }
      const table = tableWrapper.querySelector('table')
      if (!table) {
        // Taro.hideLoading()
        return
      }
      // 获取表格数据
      const rows = Array.from(table.rows).map((row) =>
        Array.from(row.cells).map((cell) => cell.textContent)
      )

      // 创建 Excel 工作表
      const worksheet = XLSX.utils.aoa_to_sheet(rows)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
      const _tableName = tableWrapper.getAttribute('data-table-name') || 'table-data'
      // 导出 Excel 文件
      XLSX.writeFile(workbook, `${_tableName}.xlsx`)
      // Taro.hideLoading()
    } catch (error) {
      console.error('导出Excel失败:', error)
      // Taro.hideLoading()
    }
  }

  window['copyMD'] = async (codeId) => {
    const codeWrapper = document.querySelector(`[data-code-id="${codeId}"]`)
    if (!codeWrapper) {
      return
    }
    copy((codeWrapper as HTMLElement).innerText)
    ToastService.success('复制成功')
  }
})
</script>

<style lang="scss">
@import 'katex/dist/katex.min.css';

.katex {
  line-height: 2.5em;
}

.katex .vlist {
  // vertical-align: super;
  line-height: 1.8em;
}

.message-view-new {
  // background-color: aqua;
  // max-width: 850px;
  // overflow-x: hidden;

  .ant-collapse-header {
    border-radius: 10px !important;
    padding: 6px 10px 6px 0 !important;
    // background: #E7EDFE !important;
    width: max-content;
    margin-left: 15px;
  }


  .message-item {
    width: 100%;
    display: flex;
    align-items: center;

    padding-left: 10px;
    padding-right: 10px;
    margin-top: 10px;
    margin-bottom: 10px;

    .message-content {
      border-radius: 5px;

      .message-text {
        // margin: 15px;
        padding: 7px 11px;
        font-size: 15px;
        line-height: 180%;
      }
    }

    .action-button {
      border: none !important;
      background: white !important;
      color: #999 !important; // 可以根据需要更改文本颜色
      box-shadow: none !important;

      &:hover {
        cursor: pointer;
        opacity: 0.8;
        box-shadow: none !important;
      }
    }
  }

  .message-item-mine {
    max-width: 100%;
    justify-content: flex-end;


    .message-content {
      color: #333333;
      background-color: #dae7ff;
      border-radius: 15px 15px 0px 15px;


      .message-item-content {
        padding: 10px 15px;
        line-height: 1.8;
        font-size: 15px;

        .ai-result {
          white-space: pre-wrap;
        }

        // p {
        //     margin: 0;
        // }

        // h2 {
        //     font-size: 16px;
        //     font-weight: bold;
        //     background-color: #f2f6ff;
        //     padding: 5px 10px;
        //     border-radius: 5px;
        //     margin: 10px 0;
        // }
      }
    }
  }

  .message-item-ai {
    // background: forestgreen;
    justify-content: flex-start;
    align-items: flex-start;
    flex-direction: column;


    .message-content {
      color: #333333;

      background-color: #fff;
      border-radius: 15px 15px 15px 0px;

      .message-item-content {
        padding: 10px 15px;
        line-height: 1.8;
        font-size: 15px;
        margin-left: 15px;

        // p {
        //     padding-bottom: 0px;
        // }


      }

      .message-item-content1 {
        padding: 10px 15px;
        line-height: 1.8;
        font-size: 15px;
        margin-left: 8px;
      }

      .message-item-contentf {
        margin-top: -10px;
      }
    }

    .message-content-knowledge {
      color: #333333;
      max-width: 700px;
      // background-color: #f7f7f7;
      // border-radius: 15px 15px 15px 0px;
    }
  }

  .clear-hint-text {
    font-weight: 400;
    font-size: 14px;
    color: #bbbbbb;
    line-height: 21px;
    text-align: center;
    margin: 20px 0;
  }

  .overflow {
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    // -webkit-line-clamp: 1;
    overflow: hidden;
    word-break: break-all;
  }

  .flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .flex-start {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .understand-question p {
    margin: 0;
  }

  .understand-question-title {
    padding-bottom: 6px;
    font-weight: bolder;
  }

  .tink-content {
    // font-weight: 500;
    font-size: 15px;
    color: #777777;
    line-height: 27px;
  }

  .resource-link {
    display: block;
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 5px;
    line-height: 1.5;
  }

  .refs-container {
    width: 100%;
    max-width: 100%;


    .ref-item {
      width: 100%;
      margin-bottom: 5px;

    }

    .ref-link {
      display: flex;
      width: 100%;

      align-items: center;

      .ref-type {
        color: #777777;
        flex-shrink: 0;
      }

      .ref-content {
        max-width: 700px; //待优化
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .ant-timeline-item-last {
    padding-bottom: 0 !important;
  }
}

.message-item-content {
  .ai-result {
    white-space: pre-wrap;
  }

  table {
    width: 100%;
    margin: 10px 0;
    border-collapse: collapse;
  }

  table,
  th,
  td {
    border: 1px solid black;
    /* 设置表格、表头和单元格的边框 */
  }

  th,
  td {
    padding: 8px;
    /* 设置单元格内边距 */
    text-align: center;
    /* 文本左对齐 */
  }

  ul,
  ol {
    li {
      margin-left: 15px;
    }
  }

  .table-wrapper1 {
    position: relative;
  }

  .table-wrapper {
    overflow: auto;

    table {
      overflow-x: auto;
    }
  }

  .table-icon {
    position: absolute;
    font-size: 20px;
    top: -30px;
    right: 0px;
    padding: 0 6px;
    cursor: pointer;
    font-size: 12px;
    color: #666;

    &:hover {
      color: #1e99ff;
    }
  }

  .table-icon-img {
    width: 15px;
    height: 15px;
  }

  .code-block {
    margin: 16px 0;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    overflow: hidden;

    .code-language {
      height: 40px;
      background: #f5f5f5;
      color: #333;
      font-size: 12px;
      font-family: monospace;
      padding: 4px 8px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    pre {
      margin: 0;
      padding: 0;
      background: #282c34;
      color: #f8f8f2;
    }

    .copy-code {
      padding: 4px 8px;
      cursor: pointer;

      &:hover {
        color: #1e99ff;
      }
    }
  }


  ol li {
    // background-color: #ff0000;
    list-style-type: decimal;
    line-height: 180%;
  }

  p {
    // line-height: 180%;
    line-height: 1.8;
    // background-color: #df2d00;
    margin-bottom: 0.5em;
    margin-top: 0.5em;
  }

  h1 {
    // line-height: 1.8;
    font-size: 2em;
    font-weight: bold;
    margin-top: 0.5em;
    margin-bottom: 0.5em;
  }

  h2 {
    // line-height: 1.8;
    font-size: 1.5em;
    font-weight: bold;
    line-height: 1.8;
    margin-top: 0.5em;
    margin-bottom: 0.5em;
  }

  h3 {
    // line-height: 1.8;
    font-size: 1.2em;
    font-weight: bold;
    line-height: 1.8;
    margin-top: 0.5em;
    margin-bottom: 0.5em;
  }

  .table-icon {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 10px;
  }

}
</style>
