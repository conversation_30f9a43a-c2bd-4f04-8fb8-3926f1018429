<template>
    <div class="absolute w-full bottom-1 left-1">
        <!-- 使用自定义水平列表组件 -->
        <HorizontalList :items="selectFileList" :scrollStep="250">
            <!-- 定义列表项内容 -->
            <template #item="{ item }">
                <div :class="{
                    'flex-shrink-0 overflow-hidden transition-all duration-200 bg-gray-100 border backdrop-blur-sm rounded-xl border-gray-200/70 group hover:border-blue-700/30': true,
                    'border-red-600 hover:border-red-400': item.status == RepositoryFileStatus.ERROR
                }">
                    <div class="relative flex items-center justify-between w-full px-2 py-1">
                        <div class="flex-shrink-0">
                            <Iconfont :name="getFileIcon(item)" :size="22"></Iconfont>
                        </div>
                        <!-- 左侧：文件信息 -->
                        <div class="flex flex-col justify-center flex-1 min-w-0 pr-4 pl-1.5">
                            <!-- 左上：文件名 -->
                            <h3
                                class="overflow-hidden pr-1 text-ellipsis text-sm font-bold text-gray-700 break-words max-w-[160px] whitespace-nowrap">
                                {{ item.title || '--' }}
                            </h3>
                            <!-- 左下：文件信息 -->
                            <div v-if="item.status != RepositoryFileStatus.ERROR"
                                class="flex items-center gap-3 my-1 text-xs text-gray-500 truncate transition-colors whitespace-nowrap">
                                <span v-if="item.fileSize" class="flex items-center">
                                    {{
                                        formatStorageSize(item.fileSize, 2) }}
                                </span>
                                <span v-if="item.wordCount" class="flex items-center">
                                    {{ (item.wordCount / 10000).toFixed(2) }}万字
                                </span>
                                <span v-if="item.createTime" class="flex items-center">
                                    {{ formatDate(item.createTime) }}
                                </span>
                            </div>
                            <div v-else><span class="inline-block px-2 py-1 text-xs text-red-500 rounded-md bg-red-50">
                                    学习失败
                                </span>
                            </div>
                        </div>
                        <!-- 关闭 -->
                        <span class="absolute px-1 py-1 text-blue-600 cursor-pointer top-1 right-1"
                            @click.stop="deleteFile(item)">
                            <close-one :size="14"></close-one>
                        </span>

                        <div class="absolute bottom-0 left-0 w-full">
                            <AiQuestionFileItemProcess :repository-file="item"></AiQuestionFileItemProcess>
                        </div>
                    </div>
                </div>
            </template>
        </HorizontalList>
    </div>

    <!-- 加上默认高度，以便不影响输入框的大小 -->
    <div v-if="selectFileList.length" class="h-20"></div>
</template>
<script setup lang="ts">
import { CloseOne } from '@icon-park/vue-next';
import type { NewRepositoryFile } from '~/services/types/repositoryFile';
import AiQuestionFileItemProcess from './AiQuestionFileItemProcess.vue';
const props = defineProps({
    selectFileList: {
        type: Array,
        default: [],
    },
})
const emit = defineEmits(['deleteFile']);
const deleteFile = (item: any) => {
    emit('deleteFile', item);
}
// const items = [
//     'https://picsum.photos/468/468?random=1',
//     'https://picsum.photos/468/468?random=2',
//     'https://picsum.photos/468/468?random=3',
//     'https://picsum.photos/468/468?random=4',
//     'https://picsum.photos/468/468?random=5',
//     'https://picsum.photos/468/468?random=6'
// ]
const formatDate = (dateString: string) => {
    if (!dateString) return '';

    // 使用工具函数获取日期部分
    const formattedDateTime = formatDateTimeString(dateString);
    // 只返回日期部分，去掉时间
    return formattedDateTime.split(' ')[0];

    // const date = new Date(dateString);
    // return date.toISOString().split('T')[0];
}

const getFileNameAndExtension = (fullName: string) => {
    const match = fullName.match(/^(.+)(\.[^.]+)$/)
    return {
        name: match ? match[1].toLowerCase() : fullName.toLowerCase(),
        extension: (match ? match[2] : '').toLowerCase().replace('.', '')
    }
}

const getFileIcon = (item: NewRepositoryFile) => {
    if (!item.title) return KnowledgeFileIcon.encode;

    const { extension } = getFileNameAndExtension(item.title);


    // 根据扩展名返回对应图标
    switch (extension) {
        case 'pdf':
            return KnowledgeFileIconSvg.pdf;
        case 'doc':
        case 'docx':
            return KnowledgeFileIconSvg.doc;
        case 'ppt':
        case 'pptx':
            return KnowledgeFileIconSvg.ppt;
        case 'img':
        case 'jpg':
        case 'jpeg':
        case 'png':
            return KnowledgeFileIconSvg.img;
        case 'txt':
        case 'md':
        case 'text':
            return KnowledgeFileIconSvg.text;
        case 'xlsx':
        case 'csv':
            return KnowledgeFileIconSvg.xlsx;
        default:
            return KnowledgeFileIconSvg.encode;
    }
}
</script>