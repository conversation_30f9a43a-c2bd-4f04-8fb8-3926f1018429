<template>
    <div class="border-gray-200" :class="[isHome ? 'p-0' : ' p-4']">
        <div :class="[isHome ? '' : 'max-w-4xl mx-auto']">
            <div v-if="!isHome && isLogined" class="flex justify-end gap-3 pb-3 ">
                <button @click="handleOpenNewQueation"
                    class="flex items-center justify-center px-6 py-2 text-gray-800 bg-white border rounded-lg cursor-pointer border-blue-50 hover:border-blue-700 hover:text-blue-700 hover:bg-blue-50 ">
                    <plus :size="14" /><span class="pl-2 text-sm">新提问</span>
                </button>
                <QuestionRecordOperate :session-from="sessionFrom" :session-list="sessionList"
                    @ok="handleOpenQueationRecord" @change-session-id="handleChangeSessionId">
                </QuestionRecordOperate>
            </div>
            <div class="bg-white rounded-xl p-1 sm:p-2 pt-0 border transition-all  mb-1 focus-within:shadow-[0_0_15px_rgba(59,130,246,0.3)] focus-within:border-blue-400 relative before:absolute before:inset-0 before:rounded-xl before:transition-all before:opacity-0 focus-within:before:opacity-100 before:bg-gradient-to-r before:from-blue-500/5 before:to-blue-400/5 before:-z-10"
                :class="[isHome ? 'border-blue-300/50' : 'border-gray-200']">
                <div class="relative">
                    <textarea v-model="questionValue" :auto-size="{ minRows: 3, maxRows: 2 }"
                        :placeholder="textAreaPlaceholder"
                        class="w-full p-2 text-sm bg-transparent rounded-lg outline-none resize-none sm:p-3 focus:ring-0 focus:border-transparent sm:text-base"
                        :class="[isHome ? 'min-h-[95px]' : 'min-h-[80px]']" :maxlength="maxLength"
                        @keydown="handleKeyDown" @paste="handlePaste" />
                    <div class="bg-blue-50 w-[176px] h-[99px] flex justify-center items-center relative"
                        v-for="item in files">
                        <img class="max-w-[176px] max-h-[99px] object-contain transition-opacity duration-300"
                            :src="item?.url || ''" />
                        <div class="absolute top-[7px] right-[7px] px-[4px] cursor-pointer" @click="handleDelImg"><span
                                data-v-d5d524ce="" role="img" aria-label="close-circle"
                                class="anticon anticon-close-circle"
                                style="font-size: 16px; color: rgb(56, 139, 239);"><svg focusable="false" class=""
                                    data-icon="close-circle" width="1em" height="1em" fill="currentColor"
                                    aria-hidden="true" fill-rule="evenodd" viewBox="64 64 896 896">
                                    <path
                                        d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z">
                                    </path>
                                </svg></span>
                        </div>
                        <UProgress v-if="item.percent < 100" :value="item.percent" color="blue"
                            class="absolute top-1/2 -translate-y-1/2 px-[10px]" />
                    </div>
                    <!--文件列表-->

                    <AiQuestionFileList :select-file-list="selectFileList" @delete-file="deleteFile" />

                </div>

                <div class="flex items-start justify-between space-y-3 sm:flex-row sm:items-center sm:space-y-0 ">
                    <div class="flex flex-wrap gap-2 mt-3 sm:space-x-3 sm:mt-0">
                        <AiSelect :questionFileInfo="questionFileInfo" v-model:question-mode-value="questionModeValue"
                            :isKnowledge="isKnowledge" :is-home="isHome"
                            :repository-file-id-list="repositoryFileIdList" />
                    </div>

                    <!-- 右侧按钮组：新增上传图片按钮 -->
                    <div
                        class="flex items-center justify-end w-full mr-0.5 space-x-1 text-xs md:mr-2 md:space-x-2 sm:w-auto md:text-sm">
                        <ClientOnly>
                            <div class="mt-1 text-red-500 text-nowrap" v-if="remainingCharacters < 1">
                                已超出字数
                            </div>
                            <div class="px-1 py-1 border rounded-lg md:px-3 bg-amber-50 border-amber-100 text-amber-500 whitespace-nowrap"
                                v-if="isShowResidue">
                                剩余提问:
                                {{ isSSSVip ? '无限' : canAskQuestionCount }}次
                                <span
                                    class="ml-0.5 md:ml-6 font-normal  text-blue-500 leading-5 md:leading-6 cursor-pointer"
                                    v-if="!isSSSVip && canAskQuestionCount <= 0" @click="handleUpgrade">
                                    升级
                                </span>
                            </div>
                        </ClientOnly>
                        <!-- 显示学术搜索剩余字数 -->
                        <!-- <ClientOnly>
                            <div class="flex items-center gap-1 px-1 py-2 text-gray-800 rounded-lg md:px-3 md:py-2 bg-gray-50 whitespace-nowrap"
                                v-if="scholarSearchRemainingNum < 11">
                                <BookIconfont name="xueshubiaoti" :size="17" />
                                <span class="hidden md:inline-block">剩余学术搜索:</span>
                                {{ scholarSearchRemainingNum }}次
                            </div>
                        </ClientOnly> -->
                        <!-- 上传图片 -->
                        <XUploadImg v-if="!isKnowledge" v-model:files="files" :paste-file-info="pasteFileInfo"
                            :repository-file-id-list="repositoryFileIdList" />
                        <!-- 上传知识库文件 -->
                        <XUploadKnowledge v-if="!isKnowledge" v-model:files="files" @select="onKnowledgeSelect">
                            <template #content>
                                <button
                                    class="p-1.5 sm:p-2 text-gray-500 hover:text-gray-700 transition-colors relative"
                                    :disabled="!!files.length">
                                    <span
                                        :class="[!!files.length ? 'text-gray-400 group-hover/check:text-gray-400 cursor-not-allowed' : 'text-gray-500 cursor-pointer']">
                                        <BookIconfont name="fileupload" :size="24">
                                        </BookIconfont>
                                    </span>
                                </button>

                            </template>
                        </XUploadKnowledge>
                        <!-- 发送按钮 -->
                        <button class="relative bg-transparent group" @click="handlePressSubmit">
                            <div
                                class="absolute -inset-0.5 bg-transparent blur-sm group-hover:blur-md transition-all duration-300">
                            </div>
                            <div class="relative flex items-center justify-center overflow-hidden leading-none transition-all duration-300 bg-transparent rounded-lg hover:from-blue-600 hover:to-indigo-700"
                                :class="isSubmitEnabled ? 'text-white' : 'disabled-chat-input text-gray-500 cursor-not-allowed'">

                                <template v-if="isSubmitEnabled">
                                    <svg t="1754883495618"
                                        class="transition-transform duration-300 icon group-hover:scale-110"
                                        viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
                                        p-id="4045" width="35" height="35">
                                        <path
                                            d="M0 0.058511m234.043769 0l555.853951 0q234.043769 0 234.043769 234.043769l0 555.853951q0 234.043769-234.043769 234.043769l-555.853951 0q-234.043769 0-234.043769-234.043769l0-555.853951q0-234.043769 234.043769-234.043769Z"
                                            fill="#2551B5" p-id="4046"></path>
                                        <path
                                            d="M777.259357 246.82841c11.702188 11.672933 15.680933 28.670362 10.444203 44.292783l-166.200332 498.513228a43.210331 43.210331 0 0 1-39.056054 29.548026 43.093309 43.093309 0 0 1-41.630535-25.715559l-95.519113-214.822925-214.764414-95.460602a43.093309 43.093309 0 0 1-25.686303-41.659791c0.877664-17.962859 12.521342-33.351237 29.548026-38.997543l498.513227-166.171076a43.181075 43.181075 0 0 1 44.351295 10.473459zM746.365579 277.400377L248.261928 443.571453l214.735158 95.402091c8.39632 3.7447 15.359122 10.005371 19.89372 17.904349l2.077139 4.095766 95.402091 214.58888L746.365579 277.400377z m-119.362322 122.990001a21.649049 21.649049 0 0 1 0 30.659733L545.965602 512.029255a21.649049 21.649049 0 1 1-30.659734-30.659733l81.0084-80.979144a21.649049 21.649049 0 0 1 30.630478 0z"
                                            fill="#FFFFFF" p-id="4047"></path>
                                    </svg>
                                </template>
                                <template v-else>
                                    <svg t="1754882980017"
                                        class="transition-transform duration-300 icon group-hover:scale-110"
                                        viewBox="0 0 1053 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
                                        p-id="3887" width="35" height="35">
                                        <path
                                            d="M1.55054 0.058511m234.043769 0l555.853951 0q234.043769 0 234.043769 234.043769l0 555.853951q0 234.043769-234.043769 234.043769l-555.853951 0q-234.043769 0-234.043769-234.043769l0-555.853951q0-234.043769 234.043769-234.043769Z"
                                            fill="#C5C9D2" p-id="3888"></path>
                                        <path
                                            d="M778.809897 246.82841c11.702188 11.672933 15.680933 28.670362 10.444203 44.292783l-166.200332 498.513228a43.210331 43.210331 0 0 1-39.056054 29.548026 43.093309 43.093309 0 0 1-41.630535-25.715559l-95.519113-214.822925-214.764414-95.460602a43.093309 43.093309 0 0 1-25.686303-41.659791c0.877664-17.962859 12.521342-33.351237 29.548026-38.997543l498.513227-166.171076a43.181075 43.181075 0 0 1 44.351295 10.473459zM747.916119 277.400377L249.812468 443.571453l214.735158 95.402091c8.39632 3.7447 15.359122 10.005371 19.89372 17.904349l2.077139 4.095766 95.402091 214.58888L747.916119 277.400377z m-119.362322 122.990001a21.649049 21.649049 0 0 1 0 30.659733L547.516142 512.029255a21.649049 21.649049 0 1 1-30.659734-30.659733l81.0084-80.979144a21.649049 21.649049 0 0 1 30.630478 0z"
                                            fill="#FFFFFF" p-id="3889"></path>
                                    </svg>
                                </template>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
            <!-- 部功能提示 -->
            <div v-if="!isHome" class="flex items-center justify-center mt-2 text-xs text-center text-gray-500">
                回复的内容由AI生成，非人工编辑；其内容准确性和完整性无法保证，不代表我们的态度和观点。
            </div>

        </div>
    </div>
</template>
<script lang="ts" setup>
import { useChatStore } from '@/stores/chat';
import { getAiQuestionModeList } from '@/utils/utils';
import { Plus } from '@icon-park/vue-next';
import { useRouter } from 'vue-router';
import { getFilesByIds } from '~/api/repositoryFile';
import XUploadImg from '~/components/XUploadImg.vue';
import type { UploadDocument } from '~/services/types/knowledgeUpload';
import type { QuestionFile, RepositoryFile } from '~/services/types/repositoryFile';
import { UserService } from '~/services/user';
import { useRechargeStore } from '~/stores/recharge';
import { useUserStore } from '~/stores/user';

import type { AppMessageRecord } from '~/services/types/appMessage';
import { HomeQuestionInputModal, SessionFrom } from '~/utils/constants';
import AiQuestionFileList from './AiQuestionFileList.vue';
import AiSelect from './AiSelect.vue';
import QuestionRecordOperate from './QuestionRecordOperate.vue';
import XUploadKnowledge from './XUploadKnowledge.vue';
const { $eventBus } = useNuxtApp();
const props = defineProps({
    question: {
        type: String,
        default: '',
    },
    isKnowledge: {
        type: Boolean,
        default: false,
    },
    isAsking: {
        type: Boolean,
        default: false,
    },
    isWaitingAnswer: {
        type: Boolean,
        default: false,
    },
    isHome: {
        type: Boolean,
        default: false,
    },
    questionMode: {
        type: Array as PropType<string[]>,
        default: () => [],
    },
    repositoryFileIdList: {
        type: Array as PropType<string[]>,
        default: () => [],
    },
    // 知识库文件夹id列表
    repositoryFolderIdList: {
        type: Array as PropType<string[]>,
        default: () => [],
    },
    // 会话来源
    sessionFrom: {
        type: String,
        default: SessionFrom.Normal,
    },
    sessionList: {
        type: Array as PropType<AppMessageRecord[]>,
        default: () => [],
    },
})


const currentMode = ref(HomeQuestionInputModal.AI_CREATE)

const emit = defineEmits(['update:question', 'update:questionMode', 'update:repositoryFileIdList', 'submit', 'openNewQuestion', 'changeSessionId'])
const questionValue = computed({
    get: () => props.question,
    set: (val) => {
        // console.log(val, 'val')
        emit('update:question', val)
    }
})
const questionModeValue = computed({
    get: () => props.questionMode,
    set: (val) => {
        emit('update:questionMode', val)
    }
})
const userStore = useUserStore()
const { isLogined } = storeToRefs(userStore)

const textAreaPlaceholder = computed(() => {
    if (props.sessionFrom == SessionFrom.KnowledgeFolderRoot) {
        return '对本知识库进行提问'
    } else if (props.sessionFrom == SessionFrom.KnowledgeFolder) {
        return '对本文件夹进行提问'
    } else if (props.sessionFrom == SessionFrom.KnowledgeSingleFile) {
        return '对本文件进行提问'
    }
    return '想了解什么知识，快来问问我！Shfit+Enter/Ctrl+Enter换行'
})
const questionFileInfo = ref<QuestionFile>()
const selectFileList = ref<UploadDocument[]>([])
const handleKeyDown = (event: KeyboardEvent) => {
    if ((event.ctrlKey || event.shiftKey) && event.key === 'Enter') {
        event.preventDefault()

        const textarea = event.target as HTMLTextAreaElement
        const start = textarea.selectionStart
        const end = textarea.selectionEnd

        if (!questionValue.value) {
            return
        }
        questionValue.value = `${questionValue.value.substring(
            0,
            start
        )}\n${questionValue.value.substring(end)}`

        textarea.setSelectionRange(start + 1, start + 1)
        return
    }
    if (event.key == 'Enter') {
        event.preventDefault()
        handlePressSubmit()
    }
}
const pasteFileInfo = ref()
const handlePaste = (event: ClipboardEvent) => {
    if (props.isKnowledge) {
        return
    }
    const clipboardData = event.clipboardData
    if (clipboardData) {
        // 获取粘贴的内容
        const items = clipboardData.items
        // 遍历粘贴板中的内容
        for (const item of items) {
            // 检查是否是图片类型
            if (item.type.startsWith('image/')) {
                // 获取图片文件
                const file = item.getAsFile()
                if (file) {
                    // 这里可以处理图片文件，比如上传或显示
                    // message.success('图片粘贴成功！')
                    console.log('粘贴的图片文件:', file)
                    pasteFileInfo.value = {
                        uid: 1,
                        name: file.name,
                        status: 'ready',
                        originFileObj: file
                    }
                }
                // 阻止默认粘贴行为（防止图片以 URL 粘贴）
                event.preventDefault()
                break
            }
        }
    }
}
const handleChangeSessionId = (sessionId: string) => {
    emit('changeSessionId', sessionId)
}
const files = ref<any>([])
watch(
    () => {
        return files.value
    },
    (_newValue) => {
        if (_newValue.length) {
            const file = _newValue[0];
            if (file && file.response) {
                const { fileId, fileUrl } = file.response;
                questionFileInfo.value = {
                    id: fileId,
                    fileUrl: fileUrl,
                    fileName: '',
                    status: 'done',
                    userId: '',
                };
                questionModeValue.value = [];
            }
        } else {
            // questionFileInfo.value = undefined
            // questionModeValue.value = [QuestionTypeEnum.useR1, QuestionTypeEnum.useSearch]
        }
    }
)
// 按钮是否可用
const isSubmitEnabled = computed(() => {
    if (props.isAsking) {
        return false
    }
    if (!questionFileInfo.value && !questionFileInfo.value?.id) {
        if (!props.question) {
            return false
        }
    }
    if (remainingCharacters.value < 1) {
        return false
    }
    // 检查有学习中的...不让发送 selectFileList 
    const unlearnedFile = selectFileList.value.filter((d) => d.status !== RepositoryFileStatus.ERROR && d.status !== RepositoryFileStatus.DONE)
    if (unlearnedFile.length > 0) {
        return false
    }
    return true
})
const handlePressSubmit = () => {
    if (!UserService.isLogined()) {
        const userStore = useUserStore()
        userStore.openLoginModal()
        return
    }
    // if (files.value.length) {
    //     const file = files.value[0];
    //     if (file && file.response) {
    //         const { fileId, fileUrl } = file.response;
    //         questionFileInfo.value = {
    //             id: fileId,
    //             fileUrl: fileUrl,
    //             fileName: '',
    //             status: 'done',
    //             userId: '',
    //         };
    //         questionModeValue.value = []
    //     }
    // }
    // console.log('questionFileInfo', questionFileInfo.value, files.value)
    if (!isSubmitEnabled.value) {
        return
    }
    emit('submit', questionFileInfo.value)
}

const handleDelImg = () => {
    files.value = []
    questionFileInfo.value = undefined
    questionModeValue.value = [QuestionTypeEnum.useR1, QuestionTypeEnum.useSearch]
}
const handleDeleteImage = () => {
    files.value = []
    questionFileInfo.value = undefined
    questionModeValue.value = getAiQuestionModeList()
}
const clearQuestionFileInfo = () => {
    handleDeleteImage()
}
const isShowResidue = computed(() => {
    if (!UserService.isLogined()) {
        return false
    }
    if (UserService.canAskQuestionCount() > 10) {
        return false
    }

    return true

})
const canAskQuestionCount = computed(() => {
    return UserService.canAskQuestionCount()
})
const isSSSVip = computed(() => {
    return UserService.isSSSVip()
})

// 学术搜索次数
const scholarSearchRemainingNum = computed(() => {
    return UserService.getKnowledgeAssistantMemberInfo()?.maxScholarSearch || 0
})

const handleChangeMode = (mode: HomeQuestionInputModal) => {
    currentMode.value = mode
}

const handleUpgrade = () => {
    const rechargeStore = useRechargeStore()
    rechargeStore.$state.rechargeModalVisible = true
}
const maxLength = 2000;

const remainingCharacters = computed(() => {
    return maxLength - questionValue.value.length;
});
let timerId: NodeJS.Timeout | undefined = undefined

const refreshSelectFileList = async () => {
    const list = unref(selectFileList)
    if (list.length) {
        const unlearnedFile = list.find(item => item.status !== RepositoryFileStatus.DONE)
        if (unlearnedFile) {
            const ids = list.map(item => `${item.id}`).filter(item => item)
            const res = await getFilesByIds({
                fileIds: ids,
                spaceId: UserService.getSelfUserId()
            })
            if (!res.success) {
                return
            }
            const teamFiles = res.data
            teamFiles.forEach((d: RepositoryFile) => {
                const dd = selectFileList.value.find(item => `${item.id}` == d.id)
                if (dd) {
                    dd.status = d.status
                    dd.wordCount = d.wordCount
                    dd.status = d.status
                    dd.processData = d.processData
                }
            });
            // 重新对processingList 进行排序，status：RepositoryFileStatus.ERROR排前面
            const processingList = teamFiles.filter((d: RepositoryFile) => d.status !== RepositoryFileStatus.ERROR && d.status !== RepositoryFileStatus.DONE).sort((a: RepositoryFile, b: RepositoryFile) => {
                // 将错误状态的文件排在前面
                if (a.status === RepositoryFileStatus.ERROR && b.status !== RepositoryFileStatus.ERROR) {
                    return -1;
                }
                if (b.status === RepositoryFileStatus.ERROR && a.status !== RepositoryFileStatus.ERROR) {
                    return 1;
                }
                return 0;
            }) || []
            if (timerId) {
                clearTimeout(timerId)
            }
            // 检查是否有处理中的文件
            if (processingList.length > 0) {
                timerId = setTimeout(() => {
                    refreshSelectFileList()
                }, 3000)
            }

        }
    }
}
const onKnowledgeSelect = (list: UploadDocument[]) => {
    let repositoryFileIdList: string[] = []
    list.forEach(item => {
        if (item.id) {
            repositoryFileIdList.push(`${item.id}`)
        }
    })
    console.log('onKnowledgeSelect', repositoryFileIdList, list)
    selectFileList.value = list
    emit('update:repositoryFileIdList', repositoryFileIdList)
    //  已添加文件后，联网搜索、学术搜索、知识库搜索关闭，不可点击打开，图片上传关闭
    questionModeValue.value = [QuestionTypeEnum.useR1];
    // 如果 selectFileList 文件有为完成学习的，需要调用刷新接口，更新状态
    refreshSelectFileList()
}
const deleteFile = (item: UploadDocument) => {
    const index = selectFileList.value.findIndex(file => file.id === item.id)
    if (index !== -1) {
        selectFileList.value.splice(index, 1)
    }
    let repositoryFileIdList: string[] = []
    selectFileList.value.forEach((d: UploadDocument) => {
        if (item.id) {
            repositoryFileIdList.push(`${d.id}`)
        }
    })
    emit('update:repositoryFileIdList', repositoryFileIdList)
    $eventBus.emit(StarloveConstants.keyOfEventBus.myQuestionSelectKnowledgeFileDeleteAction, {
        id: item.id
    })
}
const clearKnowledgeFiles = () => {
    selectFileList.value.forEach((d: UploadDocument) => {
        $eventBus.emit(StarloveConstants.keyOfEventBus.myQuestionSelectKnowledgeFileDeleteAction, {
            id: d.id
        })
    })
    selectFileList.value = []
    emit('update:repositoryFileIdList', [])
}
const router = useRouter()

const handleOpenNewQueation = () => {
    const chat = useChatStore()
    chat.setIsNewSessionId(true)
    // 如果是知识库提问，就不要跳转
    if ([SessionFrom.KnowledgeFolder, SessionFrom.KnowledgeFolderRoot, SessionFrom.KnowledgeSingleFile].includes(props.sessionFrom as any)) {
        emit('openNewQuestion')
        return
    }
    setTimeout(() => {
        router.replace(`/chat`)
    }, 300)
}
const handleOpenQueationRecord = () => {
    router.replace(`/profile/questions`)
}
defineExpose({
    clearQuestionFileInfo,
    clearKnowledgeFiles
})
</script>
<style lang="scss" scoped>
.disabled-chat-input {
    background: #f1f1f1;
}
</style>