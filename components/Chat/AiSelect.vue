<template>
    <div class="knowledge-textarea-action-view">
        <div class="knowledge-action-line">
            <div class="chat-footer-checkbox">
                <div class="flex flex-wrap gap-1 sm:space-x-2">
                    <a-tooltip v-for="item in questionOptionsGroupB" :key="item.value">
                        <template #title>{{ questionModeValue.includes(item.value) ? item.tooltip : item.tooltipDisabled
                        }}</template>
                        <template v-if="questionModeValue.includes(item.value)">
                            <label
                                class="relative group/check flex items-center px-3 sm:px-3 py-1.5 sm:py-1 bg-gradient-to-r from-blue-50/50 to-blue-100/50 rounded-lg cursor-pointer hover:from-blue-100/50 hover:to-blue-200/50 transition-colors justify-center"
                                :class="[item.mClass]">
                                <input type="checkbox" :value="item.value" v-model="questionModeValue"
                                    :disabled="item.disabled"
                                    class="w-0 h-0 hidden mr-1.5 sm:mr-2 appearance-none bg-transparent border-0 checked:bg-transparent checked:border-transparent">
                                <span
                                    class="text-xs text-gray-600 transition-colors sm:text-sm group-hover/check:text-gray-800"
                                    :class="[item.disabled ? 'text-gray-400 group-hover/check:text-gray-400' : '']">{{
                                        item.label }}</span><span class="w-1.5 h-1.5 ml-1.5 rounded-full bg-green-400"
                                    :class="[item.ignoreChecked ? 'bg-green-400' : 'bg-gray-400']"></span>
                            </label>
                        </template>
                        <template v-else>
                            <label
                                class="relative group/check flex items-center px-3 sm:px-3 py-1.5 sm:py-1 bg-gradient-to-r from-blue-50/50 to-blue-100/50 rounded-lg  hover:from-blue-100/50 hover:to-blue-200/50 transition-colors justify-center"
                                :class="[item.disabled ? 'bg-gradient-to-r from-gray-50/50 to-blue-50/50  hover:from-gray-50/50 hover:to-gray-50/50 cursor-not-allowed ' : 'text-gray-600 cursor-pointer', item.mClass]">
                                <input type="checkbox" :value="item.value" v-model="questionModeValue"
                                    :disabled="item.disabled"
                                    class="w-0 h-0 hidden mr-1.5 sm:mr-2 appearance-none bg-transparent border-0 checked:bg-transparent checked:border-transparent">
                                <span class="text-xs transition-colors sm:text-sm"
                                    :class="[item.disabled ? 'text-gray-400 group-hover/check:text-gray-400 ' : 'group-hover/check:text-gray-800 text-gray-600']">{{
                                        item.labelDisabled }}</span><span
                                    class="w-1.5 h-1.5 ml-1.5 rounded-full bg-gray-400"
                                    :class="[item.ignoreChecked ? 'bg-green-400' : 'bg-gray-400']"></span>
                            </label>
                        </template>
                    </a-tooltip>

                    <div class="hidden md:block" v-if="!isKnowledge">
                        <div class="text-gray-500 relative group/check flex flex-wrap gap-4 items-center px-2 sm:px-3 py-1.5 sm:py-2 bg-gradient-to-r from-blue-50/50 to-blue-100/50 rounded-lg hover:from-blue-100/50 hover:to-blue-200/50 transition-colors justify-center"
                            :class="{
                                'bg-gray-200': isDisabledQuestionMode
                            }">
                            <span v-for="item in questionOptionsGroupA" :key="item.icon" :class="{
                                'cursor-pointer': !item.disabled,
                                'text-blue-600': item.checked,
                                'text-gray-300 cursor-not-allowed': item.disabled && !item.checked,
                            }" @click="handleQuestionOptionsChange(item)">
                                <BookIconfont :name="item.icon" :size="20">
                                </BookIconfont>
                            </span>
                            <client-only>

                                <UPopover v-model:open="openPopover" mode="click" :disabled="isDisabledQuestionMode"
                                    :ui="config" class="flex items-center h-full">
                                    <span :class="{
                                        'cursor-not-allowed': isDisabledQuestionMode,
                                    }">
                                        <down :size="20" :fill="`${isDisabledQuestionMode ? '#d1d5db' : '#333333'}`" />
                                    </span>
                                    <template #panel>
                                        <div class="w-40 p-2 text-sm text-gray-500 bg-white shadow-sm">
                                            <div v-for="item in questionOptionsGroupA" :key="item.icon" :class="{
                                                'text-blue-600': item.checked,
                                                'px-4 py-3 cursor-pointer flex items-center hover:text-blue-800 relative': true
                                            }" @click="handleQuestionOptionsChange(item)">
                                                <a-tooltip>
                                                    <template
                                                        v-if="item.label == QuestionLabelEnum.scholar && scholarSearchRemainingNum < 11"
                                                        #title>
                                                        剩余{{ scholarSearchRemainingNum }}次 <span
                                                            class="pl-2 cursor-pointer"
                                                            @click="handleOpenExchangeModal">兑换</span>
                                                    </template>
                                                    <BookIconfont :name="item.icon" :size="17">
                                                    </BookIconfont>
                                                    <span class="pl-2 text-sm">{{ item.label }}</span>
                                                    <span class="absolute -translate-y-1/2 right-2 top-1/2">
                                                        <Check v-if="item.checked" :size="16"></Check>
                                                    </span>
                                                </a-tooltip>
                                            </div>
                                        </div>
                                    </template>
                                </UPopover>

                                <template #fallback>
                                    <span>
                                        <down :size="20" fill="#333333" />
                                    </span>
                                </template>
                            </client-only>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import BookIconfont from '@/components/Book/BookIconfont.vue';
import type { QuestionFile } from '@/services/types/repositoryFile';
import { QuestionLabelUnCheckedEnum, QuestionTypeEnum } from '@/utils/constants';
import { Check, Down } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import { UserService } from '~/services/user';
import { useExchangeSearchStore } from '~/stores/exchangeSearch';

interface Props {
    isAskQuestionInTheDocument?: boolean
    isKnowledgeHomePage?: boolean
    questionModeValue: string[]
    questionFileInfo?: QuestionFile
    isKnowledge?: boolean
    isHome?: boolean
    repositoryFileIdList: string[]
}
const props = withDefaults(defineProps<Props>(), {
    isKnowledge: false,
    isHome: false,
    repositoryFileIdList: () => []
})

const emit = defineEmits(['update:questionModeValue', 'ok', 'uploadImage'])

const questionModeValue = computed({
    get: () => props.questionModeValue,
    set: (val) => {
        emit('update:questionModeValue', val)
    }
})
const openPopover = ref(false)
const isVisibleRecharge = ref(false)
const questionOptions = ref([
    { label: QuestionLabelEnum.useR1, value: QuestionTypeEnum.useR1, originalValue: QuestionTypeEnum.useR1, disabled: false, tooltip: '采用Deepseek R1满血版推理模型，解决复杂推理问题', labelDisabled: QuestionLabelUnCheckedEnum.normal, tooltipDisabled: `采用小in通用大模型，适用于普通问题回答`, ignoreChecked: true, mClass: 'min-w-28', icon: '', checked: false, sort: 1 },
    { label: QuestionLabelEnum.useSearch, value: QuestionTypeEnum.useSearch, originalValue: QuestionTypeEnum.useSearch, disabled: false, tooltip: '接入互联网，按需搜索网页资料', labelDisabled: QuestionLabelUnCheckedEnum.noUseSearch, tooltipDisabled: `未接入互联网`, ignoreChecked: false, mClass: 'min-w-24', icon: 'lianwang', checked: false, sort: 2 },
    { label: QuestionLabelEnum.useRepository, value: QuestionTypeEnum.useRepository, originalValue: QuestionTypeEnum.useRepository, disabled: false, tooltip: '接入知识库，按需搜索知识库内资料', labelDisabled: QuestionLabelUnCheckedEnum.noUseRepository, tooltipDisabled: `未接入知识库`, ignoreChecked: false, mClass: 'min-w-28', icon: 'zhishiku', checked: false, sort: 4 },
    { label: QuestionLabelEnum.scholar, value: QuestionTypeEnum.useScholar, originalValue: QuestionTypeEnum.useScholar, disabled: false, tooltip: '接入学术搜索，按需搜索学术资料', labelDisabled: QuestionLabelUnCheckedEnum.noScholar, tooltipDisabled: `未接入学术搜索`, ignoreChecked: false, mClass: 'min-w-28', icon: 'xueshubiaoti', checked: false, sort: 3 }
])
const questionOptionsGroupA = computed(() => {
    const list = questionOptions.value.filter(item => item.label !== QuestionLabelEnum.useR1)
    list.forEach(item => {
        item.checked = questionModeValue.value.includes(item.value)
        // if (item.label === QuestionLabelEnum.useSearch) {
        //     item.checked = questionModeValue.value[1] == item.value
        // } else if (item.label === QuestionLabelEnum.useRepository) {
        //     item.checked = questionModeValue.value[2] == item.value
        // } else if (item.label === QuestionLabelEnum.scholar) {
        //     item.checked = questionModeValue.value[3] == item.value
        // }
    })
    return list.sort((a, b) => a.sort - b.sort)
})
const questionOptionsGroupB = computed(() => {
    return questionOptions.value.filter(item => item.label === QuestionLabelEnum.useR1)
})
const handleQuestionOptionsChange = (itemA: any) => {
    if (itemA.disabled) {
        return
    }
    questionOptions.value.forEach(item => {
        if (item.label == itemA.label) {
            item.checked = !item.checked
            if (item.checked) {
                questionModeValue.value.push(item.originalValue)
            } else {
                questionModeValue.value = questionModeValue.value.filter((d: any) => d != item.originalValue)
            }
        }
    })
    // setTimeout(() => {
    //     openPopover.value = false
    // }, 100)
}
const handleCheckboxChange = (checkedValue: any) => {
    // console.log('handleCheckboxChange checkedValue ==>', checkedValue)
    // emit('updateQuestionModeValue', checkedValue)
    storage.set(
        StarloveConstants.keyOflocalStorage.aiQuestionModeList,
        JSON.stringify(checkedValue)
    )
}
// 学术搜索次数
const scholarSearchRemainingNum = computed(() => {
    return UserService.getKnowledgeAssistantMemberInfo()?.maxScholarSearch || 0
})
const handleQuestionUploadImage = () => {
    if (props.questionFileInfo) {
        message.warning('只能上传一张图片')
        return
    }
    emit('uploadImage')
}
// 联网搜索、学术搜索、知识库搜索 是否disabled
const isDisabledQuestionMode = computed(() => {
    const list = questionOptions.value.filter(item => item.label !== QuestionLabelEnum.useR1).filter(item => item.disabled)
    return list.length == 3
})
const handleOpenExchangeModal = () => {
    // 通过store打开兑换模态弹窗
    const exchangeSearchStore = useExchangeSearchStore()
    exchangeSearchStore.openExchangeModal()
}
// 选图片
watch(
    () => {
        return props.questionFileInfo
    },
    (_newValue, _oldValue) => {
        // console.log('props.questionFileInfo ===>', _newValue)
        if (!_newValue) {
            questionOptions.value[0].disabled = false
            questionOptions.value[1].disabled = false
            questionOptions.value[2].disabled = false
            questionOptions.value[3].disabled = false
            return
        }
        questionOptions.value[0].disabled = true
        questionOptions.value[0].value = QuestionTypeEnum.useR1
        questionOptions.value[1].disabled = true
        questionOptions.value[2].disabled = true
        questionOptions.value[3].disabled = true
        // questionModeValue.value = []
        // emit('updateQuestionModeValue', [])
    }
)

// 选文件是
watch(
    () => {
        return props.repositoryFileIdList
    },
    (_newValue, _oldValue) => {
        // console.log('props.questionFileInfo ===>', _newValue)
        if (!_newValue || !_newValue.length) {
            questionOptions.value[0].disabled = false
            questionOptions.value[1].disabled = false
            questionOptions.value[2].disabled = false
            questionOptions.value[3].disabled = false
            return
        }
        questionOptions.value[0].disabled = false
        questionOptions.value[0].value = QuestionTypeEnum.useR1
        questionOptions.value[1].disabled = true
        questionOptions.value[2].disabled = true
        questionOptions.value[3].disabled = true
        // questionModeValue.value = []
        // emit('updateQuestionModeValue', [])
    }
)
const config = {
    wrapper: 'relative',
    container: 'z-50 group',
    trigger: 'inline-flex w-full',
    width: '',
    background: 'bg-white dark:bg-gray-900',
    shadow: 'shadow-lg',
    // shadow: 'shadow-[0px_1px_20px_0px_#C7D6FE]',
    rounded: 'rounded-md',
    ring: 'ring-0 ',
    base: 'overflow-hidden focus:outline-none relative',
    transition: {
        enterActiveClass: 'transition ease-out duration-200',
        enterFromClass: 'opacity-0 translate-y-1',
        enterToClass: 'opacity-100 translate-y-0',
        leaveActiveClass: 'transition ease-in duration-150',
        leaveFromClass: 'opacity-100 translate-y-0',
        leaveToClass: 'opacity-0 translate-y-1'
    },
    overlay: {
        base: 'fixed inset-0 transition-opacity z-50',
        background: 'bg-gray-200/75 dark:bg-gray-800/75',
        transition: {
            enterActiveClass: 'ease-out duration-200',
            enterFromClass: 'opacity-0',
            enterToClass: 'opacity-100',
            leaveActiveClass: 'ease-in duration-150',
            leaveFromClass: 'opacity-100',
            leaveToClass: 'opacity-0'
        }
    },
    popper: {
        strategy: 'fixed'
    },
    default: {
        openDelay: 0,
        closeDelay: 0
    },
    arrow: {
        base: 'invisible before:visible before:block before:rotate-45 before:z-[-1] before:w-2 before:h-2',
        ring: 'before:ring-0 ',
        rounded: 'before:rounded-sm',
        background: 'before:bg-gray-200 dark:before:bg-gray-800',
        shadow: 'before:shadow',
        placement: "group-data-[popper-placement*='right']:-left-1 group-data-[popper-placement*='left']:-right-1 group-data-[popper-placement*='top']:-bottom-1 group-data-[popper-placement*='bottom']:-top-1"
    }
}


onMounted(() => {
    // questionModeVue = props.questionModeValue

})
onUnmounted(() => { })

</script>

<style lang="scss" scoped>
.knowledge-textarea-action-view {

    .knowledge-action-line {
        display: flex;
        justify-content: space-between;
        align-items: center;


        .chat-footer-checkbox {
            display: flex;
            justify-content: flex-start;
            padding: 10px;
        }

        /* 当容器宽度小于768px时 */
        @media (max-width: 768px) {
            .chat-footer-checkbox {
                display: flex;
                justify-content: flex-start;
                padding: 10px 3px;
            }
        }

        .question-image {
            display: flex;
            align-items: center;
            padding: 0 6px;
            margin-left: 20px;
            cursor: pointer;
        }

        .residue {
            padding: 4px 15px;
            background: #fffaec;
            border-radius: 10px;
            border: 1px solid #ffe6ba;
            color: #f09209;
            font-size: 14px;
            white-space: nowrap;
        }

        @media screen and (max-width: 768px) {
            .residue {
                display: none;
            }
        }

        .vip-upgrade {
            margin-left: 25px;
            font-weight: 400;
            font-size: 15px;
            color: #388bef;
            line-height: 23px;
            cursor: pointer;
        }
    }

    .flex-end {
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }

    .ant-checkbox {

        // .ant-checkbox-disabled .ant-checkbox-inner
        ::v-deep .custom-checkbox-style .ant-checkbox-disabled .ant-checkbox-inner {
            background-color: #1677ff !important;
            border-color: #1677ff !important;
        }

        ::v-deep .ant-checkbox-disabled .ant-checkbox-inner:after {
            border-color: #ffffff !important;
        }

        ::v-deep .ant-checkbox-disabled+span {
            color: rgba(0, 0, 0, 0.88);
        }
    }

}
</style>