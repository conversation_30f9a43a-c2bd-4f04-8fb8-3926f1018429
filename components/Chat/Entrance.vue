<template>
    <IndexMainPart v-if="!isAgent" :is-visible-top="isVisibleTop" :session-id="sessionId"
        :is-new-session-id="isNewSessionId" :is-knowledge="isKnowledge" :repository-file-id="repositoryFileId"
        :example-list="exampleList" :session-from="sessionFrom" />
    <Agent v-else :editor-data="editorData" :is-visible-top="isVisibleTop" :session-id="sessionId" :key="sessionId"
        :is-new-session-id="isNewSessionId" :repository-file-id="repositoryFileId" :is-knowledge="isKnowledge"
        :example-list="exampleList" :repository-folder-id-list="repositoryFolderIdList"
        :is-empty-document="isEmptyDocument" :session-from="sessionFrom" :session-list="sessionList"
        :question-mode="questionMode" @change-session-id="handleChangeSessionId" />
</template>
<script lang="ts" setup>
import { type EditorData } from '@/services/types/repositoryFile';
import type { AppMessageRecord } from '~/services/types/appMessage';
import Agent from './Agent.vue';
import IndexMainPart from './AiQuestionTextarea.vue';

const props = defineProps({
    isVisibleTop: {
        type: Boolean,
        default: false,
    },
    sessionId: {
        type: String,
        default: '',
    },
    isKnowledge: {
        type: Boolean,
        default: false,
    },
    pageNo: {
        type: Number,
        default: 1,
    },
    repositoryFileId: {
        type: String,
        default: '',
    },
    exampleList: {
        type: Array as PropType<string[]>,
        default: () => [],
    },
    isNewSessionId: {
        type: Boolean,
        default: false,
    },
    isAgent: {
        type: Boolean,
        default: true,
    },
    // 知识库文件夹id列表
    repositoryFolderIdList: {
        type: Array as PropType<string[]>,
        default: () => [],
    },
    // 会话来源
    sessionFrom: {
        type: String,
        default: SessionFrom.Normal,
    },
    isEmptyDocument: {
        type: Boolean,
        default: false,
    },
    editorData: {
        type: Array as PropType<EditorData[]>,
        default: () => [],
    },
    sessionList: {
        type: Array as PropType<AppMessageRecord[]>,
        default: () => [],
    },
    questionMode: {
        type: Array as PropType<string[]>,
        default: () => [],
    },
})
const emit = defineEmits(['changeSessionId'])
// 切换sessionId事件
const handleChangeSessionId = (_sessionId: string) => {
    emit('changeSessionId', _sessionId)
}
</script>