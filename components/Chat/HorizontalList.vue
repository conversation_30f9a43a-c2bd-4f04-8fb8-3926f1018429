<template>
    <div class="relative w-full mx-auto">
        <!-- 列表容器 -->
        <div class="relative overflow-hidden horizontal-list-container">
            <!-- 列表内容 -->
            <div ref="listRef" class="flex gap-4 py-2 transition-transform duration-300 ease-out horizontal-list"
                :style="{ paddingLeft: buttonWidth + 'px', paddingRight: '40px' }">
                <!-- 列表项插槽 -->
                <slot name="item" v-for="(item, index) in items" :key="index" :item="item" />
            </div>
        </div>

        <!-- 左滚动按钮 -->
        <div class="absolute left-0 z-10 flex items-center justify-center -translate-y-1/2 bg-white h-14 top-1/2"
            v-if="showButtons" :style="{ backgroundColor: 'rgba(255, 255, 255, 0.8)' }">
            <button ref="leftBtn" @click="scroll('left')"
                class="flex items-center justify-center invisible w-10 h-10 text-gray-700 transition-all duration-200 bg-white rounded-full shadow-md opacity-0 hover:bg-gray-50"
                :style="{ opacity: showButtons ? '1' : '0', visibility: showButtons ? 'visible' : 'hidden' }"
                aria-label="滚动到左侧">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                        clip-rule="evenodd" />
                </svg>
            </button>
        </div>


        <!-- 右滚动按钮 -->
        <div class="absolute right-0 z-10 flex items-center justify-center -translate-y-1/2 bg-white h-14 top-1/2"
            :style="{ backgroundColor: 'rgba(255, 255, 255, 0.8)' }" v-if="showButtons">
            <button ref="rightBtn" @click="scroll('right')"
                class="flex items-center justify-center invisible w-10 h-10 text-gray-700 transition-all duration-200 bg-white rounded-full shadow-md opacity-0 hover:bg-gray-50"
                :style="{ opacity: showButtons ? '1' : '0', visibility: showButtons ? 'visible' : 'hidden' }"
                aria-label="滚动到右侧">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clip-rule="evenodd" />
                </svg>
            </button>
        </div>

    </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';

// 接收的 props
const props = defineProps({
    // 列表数据
    items: {
        type: Array,
        required: true
    },
    // 容器固定宽度
    containerWidth: {
        type: String,
        default: '100%'
    },
    // 每次滚动的距离
    scrollStep: {
        type: Number,
        default: 300
    }
});

// 引用
const listRef = ref<HTMLDivElement>(null);
const leftBtn = ref<HTMLButtonElement>(null);
const rightBtn = ref<HTMLButtonElement>(null);

// 状态
const showButtons = ref(false);
const buttonWidth = 0; // 按钮宽度，用于计算内边距

// 计算是否需要显示滚动按钮
const checkScrollable = () => {
    if (!listRef.value) return;

    const container = listRef.value.parentElement;
    if (!container) return;

    // 内容宽度大于容器宽度时显示按钮
    showButtons.value = listRef.value.scrollWidth > container.clientWidth;
};

// 滚动函数
const scroll = (direction: 'left' | 'right') => {
    if (!listRef.value) return;

    const scrollAmount = direction === 'left'
        ? -props.scrollStep
        : props.scrollStep;

    // 平滑滚动
    listRef.value.scrollBy({
        left: scrollAmount,
        behavior: 'smooth'
    });
};

// 监听窗口大小变化，重新检查是否需要显示按钮
const handleResize = () => {
    checkScrollable();
};

// 初始化
onMounted(() => {
    // 设置容器宽度
    const container = listRef.value?.parentElement;
    if (container) {
        container.style.width = props.containerWidth;
    }

    // 初始检查
    checkScrollable();

    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);

    // 清理函数
    return () => {
        window.removeEventListener('resize', handleResize);
    };
});

// 监听数据变化，重新检查
watch(() => props.items, () => {
    // 延迟检查，确保DOM已更新
    setTimeout(checkScrollable, 0);
});
</script>

<style scoped>
.horizontal-list-container {
    width: 100%;
}

.horizontal-list {
    width: 100%;
    overflow-x: auto;
    scrollbar-width: none;
    /* 隐藏滚动条 Firefox */
}

/* 隐藏滚动条 Chrome, Safari and Opera */
.horizontal-list::-webkit-scrollbar {
    display: none;
}

/* 触摸设备上的滚动优化 */
@media (hover: none) and (pointer: coarse) {
    .horizontal-list {
        scroll-snap-type: x mandatory;
    }

    .horizontal-list>* {
        scroll-snap-align: start;
    }
}
</style>
