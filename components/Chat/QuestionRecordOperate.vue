<template>
    <UPopover mode="hover" v-model:open="open" :popper="{ placement: 'top-end' }" :ui="config">
        <button @click="handleOpenQueationRecord"
            class="flex items-center justify-center px-6 py-2 text-gray-800 bg-white border rounded-lg cursor-pointer border-blue-50 hover:border-blue-700 hover:text-blue-700 hover:bg-blue-50">
            <history :size="14" fill="#333333" /> <span class="pl-2 text-sm ">提问记录</span>
        </button>
        <template #panel>
            <div class="w-64 p-1 text-sm bg-white " v-if="messageRecordListMy.length > 0">
                <div class="relative flex items-center justify-between px-4 py-3 font-bold">
                    <div class="flex items-center">
                        <history :size="14" />
                        <span class="pl-1">{{ title }}</span>
                    </div>
                    <div @click="handleClose" class="absolute -translate-y-1/2 cursor-pointer right-2 top-1/2">
                        <close class="px-1.5 py-1.5 lne-he cursor-pointer" :size="14" fill="#AAAAAA" />
                    </div>
                </div>
                <div>
                    <div v-for="item in messageRecordListMy.slice(0, 5)" :key="item.id" @click="handleChat(item)"
                        class="px-3 py-2 mx-1 overflow-hidden text-gray-500 rounded-md cursor-pointer text-nowrap text-ellipsis hover:text-blue-500 hover:bg-indigo-50 ">
                        {{ item.title }}
                    </div>
                    <div class="flex items-center justify-center px-3 py-2 mx-1 overflow-hidden text-gray-500 rounded-md cursor-pointer text-nowrap text-ellipsis hover:text-blue-500 hover:bg-indigo-50 "
                        @click="handleMore">
                        查看更多
                        <Right :size="20" />
                    </div>
                </div>
            </div>
        </template>
    </UPopover>
</template>
<script setup lang="ts">
import { useChatStore } from '@/stores/chat';
import { Close, History, Right } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { getMessageSessionList } from '~/api/appMessage';
import { getSubFolders, repositoryFileGetDetail } from '~/api/repositoryFile';
import type { AppMessageRecord } from '~/services/types/appMessage';
import { UserService } from '~/services/user';

const props = defineProps({
    sessionFrom: {
        type: String,
        default: SessionFrom.Normal,
    },
    sessionList: {
        type: Array as PropType<AppMessageRecord[]>,
        default: () => [],
    },
})
const open = ref(false)
const config = {
    wrapper: 'relative',
    container: 'z-50 group',
    trigger: 'inline-flex w-full',
    width: '',
    background: 'bg-white dark:bg-gray-900',
    shadow: 'shadow-[0px_1px_20px_0px_#C7D6FE]',
    rounded: 'rounded-md',
    ring: 'ring-0 ',
    base: 'overflow-hidden focus:outline-none relative',
    transition: {
        enterActiveClass: 'transition ease-out duration-200',
        enterFromClass: 'opacity-0 translate-y-1',
        enterToClass: 'opacity-100 translate-y-0',
        leaveActiveClass: 'transition ease-in duration-150',
        leaveFromClass: 'opacity-100 translate-y-0',
        leaveToClass: 'opacity-0 translate-y-1'
    },
    overlay: {
        base: 'fixed inset-0 transition-opacity z-50',
        background: 'bg-gray-200/75 dark:bg-gray-800/75',
        transition: {
            enterActiveClass: 'ease-out duration-200',
            enterFromClass: 'opacity-0',
            enterToClass: 'opacity-100',
            leaveActiveClass: 'ease-in duration-150',
            leaveFromClass: 'opacity-100',
            leaveToClass: 'opacity-0'
        }
    },
    popper: {
        strategy: 'fixed'
    },
    default: {
        openDelay: 0,
        closeDelay: 0
    },
    arrow: {
        base: 'invisible before:visible before:block before:rotate-45 before:z-[-1] before:w-2 before:h-2',
        ring: 'before:ring-0 ',
        rounded: 'before:rounded-sm',
        background: 'before:bg-gray-200 dark:before:bg-gray-800',
        shadow: 'before:shadow',
        placement: "group-data-[popper-placement*='right']:-left-1 group-data-[popper-placement*='left']:-right-1 group-data-[popper-placement*='top']:-bottom-1 group-data-[popper-placement*='bottom']:-top-1"
    }
}


defineShortcuts({
    o: () => open.value = !open.value
})
const emit = defineEmits(['ok', 'changeSessionId'])
const handleOpenQueationRecord = () => {
    console.log('打开提问记录')
    emit('ok')
}
const title = computed(() => {
    if (props.sessionFrom === SessionFrom.KnowledgeSingleFile) {
        return '文件提问记录'
    } else if (props.sessionFrom === SessionFrom.KnowledgeFolderRoot) {
        return '知识库提问记录'
    } else if (props.sessionFrom === SessionFrom.KnowledgeFolder) {
        return '知识库提问记录'
    }
    return 'AI提问记录'
})
const reqParams = reactive({
    pageNo: 1,
    pageSize: 10,
    pages: 0,
    total: 0,
    title: '',
    sessionFrom: props.sessionFrom,
})
const isLoading = ref(true)

const messageRecordList = ref<AppMessageRecord[]>([])
const messageRecordListMy = computed(() => {
    if (props.sessionFrom == SessionFrom.Normal) {
        return messageRecordList.value
    }
    return props.sessionList || []
})
const loadMessageRecordData = async () => {

    const params: any = {
        pageNo: reqParams.pageNo,
        pageSize: reqParams.pageSize,
        title: reqParams?.title || '',
        sessionFrom: props.sessionFrom,
    }

    const res = await getMessageSessionList(params)
    isLoading.value = false
    if (!res.ok || !res.data) {
        message.error(res.message || '获取消息失败')
        return
    }
    messageRecordList.value = res.data?.records || []
    if (params?.title) {
        reqParams.title = params.title || ''
    }
    reqParams.pageNo = params.pageNo || 1
    reqParams.pages = res.data.pages || 0
    reqParams.total = res.data.total || 0
}
watch(() => open.value, (val) => {
    if (val) {
        loadMessageRecordData()
    }
})
const router = useRouter()
const chat = useChatStore()
const handleClose = () => {
    open.value = false
}
const handleChat = async (item: AppMessageRecord) => {
    console.log('handleChat', item)
    chat.setIsNewSessionId(false)
    // 根据sesessionFrom判断跳转
    if (props.sessionFrom == SessionFrom.KnowledgeSingleFile) {
        emit('changeSessionId', item?.sessionId || '')
        handleClose()
        return
    } else if (props.sessionFrom == SessionFrom.KnowledgeFolder || props.sessionFrom == SessionFrom.KnowledgeFolderRoot) {
        emit('changeSessionId', item?.sessionId || '')
        handleClose()
        return
    }
    if (item?.sessionFrom == SessionFrom.KnowledgeSingleFile) {
        // 跳转前先判断文件是存在
        if (item.fileIds) {
            const res = await repositoryFileGetDetail({ spaceId: UserService.getSelfUserId(), id: item.fileIds, silent: true })
            if (!res.success) {
                message.error('该提问的原文件已删除，无法打开提问记录')
                return
            }
        }
        router.push(`/library/${item.fileIds}?type=questions`)
        return
    } else if (item?.sessionFrom == SessionFrom.KnowledgeFolder) {
        if (item?.folderIds && !isNaN(Number(item?.folderIds))) {
            const res = await getSubFolders(UserService.getSelfUserId(), { folderId: item?.folderIds, silent: true })
            if (!res.success) {
                message.error('该提问的原文件夹已删除，无法打开提问记录')
                return
            }
        }
        router.push(`/library?folderId=${item.folderIds}`)
        return
    } else if (item?.sessionFrom == SessionFrom.KnowledgeFolderRoot) {
        router.push(`/library`)
        return
    }
    router.push(`/chat/${item.sessionId}`)
}
const handleMore = () => {
    router.replace(`/profile/questions`)
}
onBeforeUnmount(() => {
    handleClose()
})
</script>