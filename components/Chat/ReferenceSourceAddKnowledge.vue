<template>
    <div v-if="isShowAddToKnowledge" class="flex justify-end pt-3">
        <button @click.stop="handleAddToKnowledge(resultItem, item)"
            class="flex items-center justify-center px-3 py-1.5 text-gray-800 bg-white border border-blue-100 rounded-lg cursor-pointer hover:border-blue-150 hover:text-blue-600 ">
            <lightning :size="13" /> <span class="pl-1 text-sm ">加入知识库</span>
        </button>
    </div>
</template>
<script setup lang="ts">
import { BindPhoneModal, HTTP_STATUS } from '@/utils/constants';
import { Lightning } from '@icon-park/vue-next';
import { cloneDeep } from 'lodash';
import { addFiles, addHtml } from '~/api/repositoryFile';
import { uploadByUrl } from '~/api/upload';
import { ToastService } from '~/services/toast';
import type { AgentStreamData, AgentStreamDataResult } from '~/services/types/agent';
import { UserService } from '~/services/user';
import { removeQuestionMarkText } from '~/utils/utils';


const props = defineProps({
    resultItem: {
        type: Object as PropType<AgentStreamDataResult>,
        default: {},
    },
    item: {
        type: Object as PropType<AgentStreamData>,
        default: {},
    },
})
// 是否展示添加到知识库按钮
const isShowAddToKnowledge = computed(() => {
    if (props.item?.result?.type == MessageResultType.WEB) {
        if (props.resultItem?.meta?.url) {
            return true
        }
    } else if (props.item?.result?.type == MessageResultType.REPO_FILE) {
        return false
    } else if (props.item?.result?.type == MessageResultType.SCHOLAR) {
        if (props.resultItem?.meta?.pdf_url) {
            return true
        }
    }
    return false
})

const postData = async (params: any) => {
    const res = await addHtml(params)
    if (!res.success) {
        return
    }
    ToastService.success('添加成功')
}
const addToLibraryByPdf = async (pdf_url: string, title: string) => {
    try {

        // 从PDF URL获取文件名
        const fileName = `${title.substring(0, 50)}.pdf` // 取标题前50个字符作为文件名

        const uploadByUrlParams = {
            fileName: fileName,
            fileUrl: removeQuestionMarkText(pdf_url),
        }

        // 保存文件信息到系统
        const uploadByUrlResult = await uploadByUrl(uploadByUrlParams)

        if (!uploadByUrlResult.ok) {
            throw new Error('文件保存失败')
        }

        // 添加到知识库
        const res = await addFiles({
            "spaceId": UserService.getSelfUserId(),
            "fileCategory": AddToDefaultFolder.MESSAGE,
            fileIds: [uploadByUrlResult.data.id]
        })

        if (res?.code == HTTP_STATUS.MOBILE_NOT_BOUND) {
            const user = useUserStore()
            user.setShowPhoneBoundModal({
                status: BindPhoneModal.SHOW_BINDING,
            })
            return
        }

        if (!res || !res.ok) {
            throw new Error('添加到知识库失败')
        }

        ToastService.success('PDF已成功添加到知识库')

    } catch (error: any) {
        console.error('添加到知识库失败:', error)
        ToastService.error(error?.message || 'PDF添加失败，请重试')
    }
}
const handleAddToKnowledge = async (_resultItem: AgentStreamDataResult, replyMessageItem: AgentStreamData) => {
    const itema = cloneDeep(_resultItem)
    let url: string = ''
    if (replyMessageItem?.result?.type === MessageResultType.WEB) {
        url = itema?.meta?.url || ''
        if (!url) {
            ToastService.error('链接不存在')
            return
        }
        postData({
            "spaceId": UserService.getSelfUserId(),
            "url": url,
            "fileCategory": AddToDefaultFolder.MESSAGE,
        })

    } else if (replyMessageItem?.result?.type === MessageResultType.SCHOLAR) {
        const pdf_url = _resultItem?.meta?.pdf_url || ''
        if (!pdf_url) {
            ToastService.error('该文献没有可用的PDF链接')
            return
        }
        addToLibraryByPdf(pdf_url, _resultItem?.title || 'pdf1')
    } else if (replyMessageItem?.result?.type === MessageResultType.SCHOLAR) {
        const file_id = _resultItem.file_id || ''
        if (!file_id) {
            ToastService.error('文件id不存在')
            return
        }
        postData({
            "spaceId": UserService.getSelfUserId(),
            "fileCategory": AddToDefaultFolder.MESSAGE,
            fileIds: [file_id]
        })
    }

}
</script>