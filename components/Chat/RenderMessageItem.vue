<template>
    <template
        v-if="[LastAgentMessageStatus.tool_call_started, LastAgentMessageStatus.tool_call_completed].includes(item?.type || '')">
        <div class="pt-3 pb-1 font-medium text-blue-700">{{ item?.tool_name }}
            <span v-if="item?.description"
                class="inline-block bg-gray-50 text-gray-700 px-3 py-1 ml-2.5 rounded-md font-normal text-xs">{{
                    item?.description
                }}</span>
        </div>
        <template v-if="item.result?.type == MessageResultType.REPO_Block">
            <p class="pt-1 text-xs text-gray-400">已读取到<span class="px-0.5">{{ item?.result?.count || 0
            }}</span>个知识点 </p>
        </template>
        <template v-else>
            <p class="pt-1 text-xs text-gray-400">已搜索到<span class="px-0.5">{{ item?.result?.count || 0
            }}</span>篇{{
                        MessageResultTypeName[item.result?.type as keyof typeof MessageResultTypeName] }} </p>
        </template>

    </template>
</template>
<script lang="ts" setup>
import type { AgentStreamData } from '~/services/types/agent';

const props = defineProps({
    item: {
        type: Object as PropType<AgentStreamData>,
        default: '',
    },
})
</script>