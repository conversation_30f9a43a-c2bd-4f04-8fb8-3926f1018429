<template>
    <div v-if="answerContent && answerContent.files" class="message-item-content">
        <div v-html="answerContent.content"></div>
        <a-image class="object-contain w-auto" v-if="answerContent.files.length > 0" :height="99" style="width: auto;"
            :src="answerContent.files[0].fileUrl" />
    </div>
    <div v-else v-html="answerContent.content"></div>
</template>
<script setup lang="ts">
import type { PropType } from 'vue';
const props = defineProps({
    answerContent: {
        type: Object as PropType<any>,
        default: '',
    },
})
</script>