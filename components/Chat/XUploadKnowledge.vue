<template>
    <div @click="showSelectModal = true">
        <slot name="content">
        </slot>
    </div>
    <!-- 在 template 底部添加弹窗组件 -->
    <SelectKnowledgeDocumentModal code="chat" v-if="showSelectModal" v-model:modelValue="showSelectModal"
        :appCode="appCode" :selected-ids="selectedKnowledgeFileIds" :options="options" :fileType="fileType"
        :maxLength="maxLength" :is-chat="true" @select="onKnowledgeSelect" />
</template>
<script setup lang="ts">
import { ref } from 'vue';
import type { UploadDocument, UploadFileRepositoryInfo } from '~/services/types/knowledgeUpload';
// 显式声明 files prop
const props = defineProps<{
    files?: any[] // 根据实际类型调整
}>()
const emit = defineEmits(['select'])

const showSelectModal = ref(false)
const appCode = ref('ppt')
const options = ref('')
const fileType = ref('')
const maxLength = ref(50)
const selectedKnowledgeFileIds = ref<(string | number | undefined)[]>([])
const selectedKnowledgeList = ref<UploadDocument[]>([])
const fileRepositoryInfoList = ref<UploadFileRepositoryInfo[]>([])
const { $eventBus } = useNuxtApp();


const setFileRepositoryInfoList = () => {
    const newList = selectedKnowledgeList.value
        .filter((item): item is (typeof item & { fileId: string }) => {
            return selectedKnowledgeFileIds.value.includes(item.id) && !!item.id
        })
    if (newList.length == 0) {
        fileRepositoryInfoList.value = []
        return
    }
    fileRepositoryInfoList.value = newList.map(item => ({
        fileId: item.fileId,
        repositoryFileId: item.id as string
    }))
}
const onKnowledgeSelect = (list: UploadDocument[]) => {
    selectedKnowledgeList.value = list
    selectedKnowledgeFileIds.value = list.map(item => item.id)
    setFileRepositoryInfoList()
    emit('select', list)
}
const removeFile = (item: { id: string }) => {
    selectedKnowledgeList.value = selectedKnowledgeList.value.filter(i => i.id !== item.id)
    selectedKnowledgeFileIds.value = selectedKnowledgeFileIds.value.filter(id => id !== item.id)

    setFileRepositoryInfoList()
    emit('select', selectedKnowledgeList.value)
}

onMounted(() => {
    $eventBus.on(StarloveConstants.keyOfEventBus.myQuestionSelectKnowledgeFileDeleteAction, removeFile)
})
onBeforeUnmount(() => {
    $eventBus.off(StarloveConstants.keyOfEventBus.myQuestionSelectKnowledgeFileDeleteAction, removeFile);
});

</script>