<template>
  <div class="relative flex px-3 py-1 overflow-hidden text-base text-indigo-500 underline min-w-36 h-7">
    <transition-group name="marquee" tag="div" class="relative w-full text-center">
      <div v-for="(text, index) in texts" :key="text.id" v-show="currentIndex === index"
        class="min-w-36 whitespace-nowrap">
        <a :href="text.sourceUrl" target="_blank">{{ text.title }}</a>
      </div>
    </transition-group>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import type { NewsInfo } from '~/services/types/repositoryFile'
const props = defineProps({
  texts: {
    type: Array as PropType<NewsInfo[]>,
    required: true,
    default: () => []
  },
  interval: {
    type: Number,
    default: 3000
  }
})

const currentIndex = ref(0)
const timer = ref<any>(null)

const startInterval = () => {
  if (timer.value) clearInterval(timer.value)
  timer.value = setInterval(() => {
    currentIndex.value = (currentIndex.value + 1) % props.texts.length
  }, props.interval)
  // console.log('🎠 Marquee: 动画已启动')
}

const handleVisibilityChange = () => {
  if (document.hidden) {
    clearInterval(timer.value)
    // console.log('🎠 Marquee: 页面隐藏，动画已暂停')
  } else {
    startInterval()
    // console.log('🎠 Marquee: 页面可见，动画已重启')
  }
}

onMounted(() => {
  startInterval()
  // 监听页面可见性变化
  document.addEventListener('visibilitychange', handleVisibilityChange)
  // console.log('🎠 Marquee: 组件已挂载')
})

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
    // console.log('🎠 Marquee: 定时器已清理')
  }
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  // console.log('🎠 Marquee: 组件已卸载')
})
</script>

<style scoped>
/* 调整过渡样式，确保元素在同一位置过渡 */
.marquee-enter-active,
.marquee-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: absolute;
  /* 仅让过渡中的元素绝对定位，确保叠加在同一位置 */
  left: 50%;
  /* 固定水平居中 */
  transform: translateX(-50%);
  width: 100%;
  /* 继承父容器宽度，避免文字换行导致的宽度变化 */
}

/* 进入动画：从下方淡入 */
.marquee-enter-from {
  opacity: 0;
  transform: translate(-50%, 20px);
  /* 仅垂直位移，保持水平居中 */
}

/* 离开动画：向上淡出 */
.marquee-leave-to {
  opacity: 0;
  transform: translate(-50%, -20px);
  /* 仅垂直位移，保持水平居中 */
}

/* 确保静态状态下元素居中 */
.marquee-enter-to,
.marquee-leave-from {
  opacity: 1;
  transform: translate(-50%, 0);
  /* 水平垂直都居中 */
}
</style>