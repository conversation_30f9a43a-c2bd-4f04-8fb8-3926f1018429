<template>
    <div class="flex items-start gap-4 p-4 bg-gray-50 rounded-lg">
        <div class="w-10 h-10 rounded-lg bg-blue-50 flex items-center justify-center shrink-0">
            <file-editing theme="outline" size="24" fill="#3B82F6" />
        </div>
        <div class="flex-1">
            <div class="mt-1 space-y-2.5">
                <div class="flex text-sm">
                    <span class="text-gray-500 w-20 shrink-0">标题：</span>
                    <span class="text-gray-600">{{ currentSubmission.formData.topic }}</span>
                </div>

                <template v-if="creatorRequireFieldInfoList.length > 0">
                    <div class="flex text-sm" v-for="(item, index) in creatorRequireFieldInfoList" :key="index">
                        <div class="text-gray-500 w-20 shrink-0">{{ polishFieldName(item.fieldName) }}：</div>

                        <div class="text-gray-600">
                            <template v-if="item.fieldType == 'checkbox' && currentSubmission?.formData?.params">
                                {{ getFieldTextForInformation(item.options,
                                    currentSubmission.formData.params[item.fieldCode]) }}
                            </template>

                            <template v-else-if="item.fieldType != 'input-text'">
                                <span v-if="item.fieldCode == 'references' && currentSubmission &&
                                    ((currentSubmission.attachments && currentSubmission.attachments.length > 0 &&
                                        currentSubmission.attachments[0].repositoryFileId) ||
                                        (currentSubmission.uploadAttachments && currentSubmission.uploadAttachments.length > 0 &&
                                            currentSubmission.uploadAttachments[0].repositoryFileId))">
                                    知识库引用
                                </span>
                                <!-- 兼容智能图表 -->
                                <span v-else-if="item?.code == 'insight_chart' && item.fieldCode == 'chartType'">{{
                                    ChartType[currentSubmission?.formData?.params?.[item.fieldCode] as keyof typeof
                                    ChartType] || ''
                                }}</span>
                                <span v-else>{{ currentSubmission?.formData?.params?.[item.fieldCode] }}</span>
                            </template>

                            <template v-else>
                                <!-- 要素条目的内容区域-->
                                <CollapsibleText :text="currentSubmission?.formData?.params?.[item.fieldCode]" />
                            </template>
                        </div>
                    </div>
                </template>

                <div class="flex text-sm" v-if="isShowPPTTemplate">
                    <span class="text-gray-500 w-20 shrink-0">PPT模板：</span>
                    <span class="text-gray-600">{{ currentSubmission.formData?.params?.templateName || '' }}</span>
                </div>

                <div class="flex text-sm" v-if="currentSubmission?.creatorCode == 'ppt'">
                    <span class="text-gray-500 w-20 shrink-0">PPT页数：</span>
                    <span class="text-gray-600">约{{ currentSubmission.formData.customSizeLength }}页</span>
                </div>

                <div class="flex text-sm" v-if="isShowUserOutline">
                    <span class="text-gray-500 w-20 shrink-0">{{ fieldOutline.length > 0 ?
                        fieldOutline[0]?.fieldName ?? '' :
                        '写作大纲' }}：</span>
                    <div class="text-gray-600 w-full">
                        <template v-if="outline">
                            <div class="flex items-center justify-between w-full ">
                                <div class="flex items-center justify-between">
                                    指定大纲
                                    <span
                                        v-if="wordsCount > -1 && currentSubmission?.creatorCode != CreateContentType.information"
                                        class="text-gray-500 mx-1">
                                        约{{ wordsCount || 0 }}字（英文减半）
                                    </span>
                                </div>
                                <div class="text-blue-500 cursor-pointer hover:text-blue-600 transition-colors"
                                    @click="handleCopyOutline">复制大纲</div>
                            </div>
                        </template>
                        <template v-else>AI智能</template>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { FileEditing } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import type { PropType } from 'vue';
import { computed } from 'vue';
import CollapsibleText from '~/components/Common/CollapsibleText.vue';
import { CreateContentType } from '~/utils/constants';
const props = defineProps({
    currentSubmission: {
        type: Object,
        required: true
    },
    currentCreator: {
        type: Object as PropType<CreatorsInfo>,
        required: true
    }
})

interface CreatorsInfo {
    creator: {
        code: string;
    };
    details: Array<{
        fieldCode: string;
        fieldName: string;
        fieldType: string;
        options?: string;
        maxLength?: number;
        nodeWordCount?: string;
    }>;
}

const outline = computed(() => {
    if (
        !props.currentSubmission?.formData.userOutline ||
        props.currentSubmission?.formData.userOutline == undefined ||
        props.currentSubmission?.formData.userOutline == null ||
        props.currentSubmission?.formData.userOutline == ''
    ) {
        return false
    }
    return true
})

const isShowPPTTemplate = computed(() => {
    if (props.currentCreator?.creator.code != 'ppt') {
        return false
    }
    if (!props.currentSubmission?.formData.params) {
        return false
    }
    return true
})

const isShowUserOutline = computed(() => {
    if (props.currentCreator?.creator.code == 'ppt') {
        return false
    }
    if (outline.value) {
        return true
    }
    if (!props.currentSubmission) {
        return false
    }
    if ('userOutline' in props.currentSubmission?.formData) {
        return true
    }
    return false
})

const fieldOutline = computed(() => {
    if (!props.currentCreator) {
        return []
    }
    return props.currentCreator?.details.filter((item: { fieldCode: string }) => item.fieldCode == 'outline')
})

const predictWordsNumber = computed(() => {
    const list = fieldOutline.value
    if (list.length == 0) {
        return 0
    }
    return list[0].maxLength || 0
})

const wordsCount = computed(() => {
    const list = fieldOutline.value
    if (list.length == 0) {
        return 500
    }
    const count = parseInt(list[0].nodeWordCount || '500')
    const customSizeLength = Number(props.currentSubmission?.formData?.customSizeLength || 0)
    return customSizeLength * count + predictWordsNumber.value
})

const creatorRequireFieldInfoList = computed(() => {
    const result = props.currentCreator?.details || []
    const list = result.filter((item: { fieldCode: string }) => {
        if (outline.value && item.fieldCode == 'size') {
            return
        }
        if (item.fieldCode == 'outline') {
            return
        }
        if (item.fieldCode == 'example') {
            return
        }
        if (item.fieldCode == 'size' && props.currentSubmission?.formData?.params?.size == 'custom') {
            return
        }
        if (item.fieldCode == 'upload') {
            return
        }
        if (item.fieldCode == 'references') {
            return
        }
        return hasParamValue(item.fieldCode)
    })
    return list
})

const hasParamValue = (code: string) => {
    if (!props.currentSubmission || !props.currentSubmission.formData || !props.currentSubmission.formData.params) {
        return false
    }
    if (!props.currentSubmission?.formData?.params?.[code]) {
        return false
    }
    const paramValue = `${props.currentSubmission.formData.params[code]}`
    if (paramValue.length == 0) {
        return false
    }
    return true
}

const getFieldTextForInformation = (options: string | undefined, value: string) => {
    if (!options || !value) return ''

    const optionList = options.split(',')
    const valueList = value.split(',')
    // console.log('optionList ==>', optionList)
    // console.log('valueList ==>', valueList)
    return valueList.map(val => {
        const option = optionList.find(opt => opt.split(':')[0] === val)
        return option ? option.split(':')[1] : val
    }).join('、')
}

const polishFieldName = (name: string) => {
    if (name.indexOf('（英文减半）') > -1) {
        return name.replace('（英文减半）', '')
    }
    // console.log('polishFieldName name ==>', name)
    return name
}

const handleCopyOutline = async () => {
    // console.log('复制大纲被点击')

    // console.log(props.currentSubmission?.formData?.userOutline)
    // 打印类型
    // console.log(typeof props.currentSubmission?.formData?.userOutline)

    const outlineList = JSON.parse(props.currentSubmission?.formData?.userOutline!)
    // console.log(outlineList)


    let result = ''
    outlineList.forEach((chapter: any, chapterIndex: number) => {
        result += `第${chapterIndex + 1}章：${chapter.chapter_title}\n`
        chapter.sections.forEach((section: any, sectionIndex: number) => {
            result += `  ${chapterIndex + 1}.${sectionIndex + 1}：${section.section_title}\n`
            section.nodes.forEach((node: any, nodeIndex: number) => {
                result += `    ${chapterIndex + 1}.${sectionIndex + 1}.${nodeIndex + 1}：${node.node_title
                    }\n`
            })
        })
    })

    try {
        await navigator.clipboard.writeText(result)


        message.success('复制成功')
    } catch (err) {
        console.error('复制失败:', err)
        message.error('复制失败')
    }
}
</script>