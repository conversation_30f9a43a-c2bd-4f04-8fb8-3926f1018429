<template>
  <div class="flex flex-col flex-1 overflow-hidden">
    <!-- 表单内容区域 -->
    <div class="flex-1 p-6 overflow-y-auto bg-white">

      <div v-if="creatorsInfo">
        <!-- 标题输入 -->
        <div class="mb-6" v-if="creatorsInfo.contentFieldName">
          <label class="flex items-center mb-2 text-sm font-medium text-gray-700">
            <span class="mr-1 text-red-500">*</span>
            {{ creatorsInfo.contentFieldName }}
            <PopoverHelp :content="creatorsInfo.contentFieldDesc || ''" />
          </label>
          <div class="relative">
            <textarea v-model="topicValue" rows="2" type="text" :placeholder="creatorsInfo.contentFieldPlaceholder"
              :maxlength="creatorsInfo.needSelectLength || 100"
              class="w-full pl-4 pr-16 py-2.5 text-sm bg-white border border-gray-200 rounded-xl focus:outline-none focus:border-blue-300/50 focus:ring-1 focus:ring-blue-300/50"></textarea>
            <button @click="clearField"
              class="absolute right-3.5 bottom-3.5 px-2 py-1 text-xs text-gray-400 hover:text-gray-600">
              清空
            </button>
          </div>
          <div class="flex justify-end mt-1">
            <span class="text-xs text-gray-400">{{ topicValue?.length || 0 }} / {{
              creatorsInfo.needSelectLength ||
              100
            }}</span>
          </div>
        </div>

        <div v-for="(item, index) in creatorRequireFieldInfoList || []" :key="item.fieldCode">
          <!--  :ref="setFieldOutlineTextareaRefs" -->
          <FieldOutline v-if="item.fieldCode == 'outline' && creatorsInfo.code != 'ppt' && creatorsInfo.code != 'book'"
            :ref="(el) => {
              if (el) fieldOutlineTextareaRefs = el
            }
              " :field-item="{ ...item, againEditOutlineList }" :creator-data="creatorData" :isHighlight="isHighlight"
            v-model:outline-type="formValues[item.fieldCode]!" @onOutlineBlur="onOutlineBlur">
          </FieldOutline>

          <!-- radios单选 -->
          <FieldRadio v-if="item.fieldType == 'radios' && item.fieldCode != 'references'" :code="creatorsInfo.code"
            :field-item="item" :outline-type="formValues.outline || CreateSubmissionAssistType.ai"
            v-model:radio-value="formValues[item.fieldCode]"
            :isUploadOrSelectedKnowledgeFile="isUploadOrSelectedKnowledgeFile">
          </FieldRadio>

          <FieldImage v-if="item.fieldType == 'radios_image'" :field-item="item">
          </FieldImage>

          <FieldRadioButton v-if="item.fieldType == 'radios_button' && shouldShowRadioButton(item)" :field-item="item"
            v-model:radio-value="formValues[item.fieldCode]">
          </FieldRadioButton>

          <!--  :ref="setFieldUploadFilesRefs" -->

          <FieldUpload :ref="(el) => {
            if (el) fieldUploadFilesRefs = el
          }
            " v-if="isShowUploadFile(item)" v-model:referenceType="formValues[item.fieldCode]" :is-show-label="true"
            :field-item-info="item" :creatorRequireFieldInfoList="creatorRequireFieldInfoList"
            :references-list="referencesList" :appCode="creatorData?.creator.code"
            :knowledge-upload-list="knowledgeUploadList" :attachments="uploadedOfSuccessAttachments"></FieldUpload>

          <!-- input输入框 -->
          <FieldInput v-if="showShouldInput(item)" :field-item="item" v-model:input-value="formValues[item.fieldCode]">
          </FieldInput>

          <!-- select下拉看框 -->
          <FieldSelect v-if="item.fieldType == 'select'" :field-item="item" v-model="formValues[item.fieldCode]">
          </FieldSelect>

          <!-- Slider 滑动输入条 -->
          <FieldSlider v-if="showShouldSlider(item)" :field-item="item" v-model="formValues[item.fieldCode]">
          </FieldSlider>

        </div>
      </div>

    </div>

    <!-- 底部按钮 -->
    <div class="flex justify-center p-4 bg-white border-t border-gray-100 shrink-0">
      <button @click="handlePressCreate"
        class="relative px-16 py-3 overflow-hidden text-base font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 rounded-xl group"
        :disabled="isSubmitting">
        <span class="relative z-10 inline-flex items-center">
          <!-- <loading v-if="isSubmitting" theme="outline" size="20" fill="#fff" class="mr-2 animate-spin" /> -->
          <a-spin :spinning="isSubmitting" class="flex items-center white-spin mr-2"></a-spin>
          {{ okText }}
        </span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { checkFileHash } from '@/api/repositoryFile.js'
import { generatePutUrl, uploadByUrl } from '@/api/upload'
import { UserService } from '@/services/user'
import { getFileExtension, getFileSha256, removeQuestionMarkText } from '@/utils/utils'
import { message } from 'ant-design-vue'
import axios from 'axios'
import { computed, onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import PopoverHelp from '~/components/Common/PopoverHelp.vue'
import { useTracking } from '~/composables/useTracking'
import type { CreatorRequireFieldInfo, CreatorsInfo, FormValues, SelectRequireInfo } from '~/services/types/appMessage'
import { useOutlineStore } from '~/stores/outline'
import { useUserStore } from '~/stores/user'
import { CreateContentType, CreateSubmissionAssistChineseType, CreateSubmissionAssistType, CreateSubmissionInformationType, EditorContent } from '~/utils/constants'
import { readSubmissionDraft } from '~/utils/pc_utils'
import FieldInput from '../component/FieldInput.vue'
import FieldOutline from '../component/FieldOutline.vue'
import FieldRadio from '../component/FieldRadio.vue'
import FieldSelect from '../component/FieldSelect.vue'
import FieldUpload from '../component/FieldUpload.vue'

const outlineStore = useOutlineStore()


const props = withDefaults(defineProps<{
  creatorData: CreatorsInfo,
  currentCode: string,
  okText?: string,
  isSubmitLoading?: boolean
}>(), {
  okText: '写作'
})

const { track } = useTracking();

const router = useRouter()
const emit = defineEmits(['createOutline', 'submitForm'])

const topicValue = ref('')

const fieldUploadFilesRefs = ref()
const fieldOutlineTextareaRefs = ref()
const isHighlight = ref(false)

// const fieldUploadFilesRefs = ref<ComponentInstance<FileUploadRef>>(null)
// const fieldOutlineTextareaRefs = ref<ComponentInstance<OutlineTextareaRef>>(null)
const formValues = ref<FormValues>({})
let outlineValue: OutlineElement[] = []
let customSizeLength = 0
const againEditOutlineList = ref([])
const user = useUserStore()

// const setFieldOutlineTextareaRefs: RefCallback<OutlineTextareaRef> = (el, refs) => {
//   if (el && 'value' in el) {
//     fieldOutlineTextareaRefs.value = el as ComponentInstance<OutlineTextareaRef>
//   }
// }

// const setFieldUploadFilesRefs: RefCallback<FileUploadRef> = (el, refs) => {
//   if (el && '$props' in el && 'fileList' in (el.$props || {})) {
//     fieldUploadFilesRefs.value = el as ComponentInstance<FileUploadRef>
//   }
// }

const creatorsInfo = computed(() => props.creatorData?.creator)

const uploadedOfSuccessAttachments = ref<any[]>([])

const creatorRequireFieldInfoList = computed(() =>
  props.creatorData?.details.filter((item) => item.fieldCode != 'example')
)

const referencesList = computed(() =>
  creatorRequireFieldInfoList.value?.filter((item) => item.fieldCode == 'references').map(item => ({
    fieldCode: item.fieldCode,
    fieldName: item.fieldName,
    fieldType: item.fieldType,
    isRequired: item.isRequired,
    fieldValue: item.fieldValue || '',
    defaultValue: item.defaultValue || '',
    options: item.options || '',
    description: item.description || '',
    placeholder: item.placeholder || '',
    maxLength: item.maxLength || 0,
    id: item.id || '',
    isShowField: true
  })) as SelectRequireInfo[] || []
)

const uploadList = computed(() =>
  creatorRequireFieldInfoList.value?.filter((item) => item.fieldCode == 'upload').map(item => ({
    fieldCode: item.fieldCode,
    fieldName: item.fieldName,
    fieldType: item.fieldType,
    isRequired: item.isRequired,
    fieldValue: item.fieldValue || '',
    defaultValue: item.defaultValue || '',
    options: item.options || '',
    description: item.description || '',
    placeholder: item.placeholder || '',
    maxLength: item.maxLength || 0,
    id: item.id || '',
    isShowField: true
  })) as SelectRequireInfo[] || []
)

const fieldOutlineList = computed(() =>
  creatorRequireFieldInfoList.value?.filter((item) => item.fieldCode == 'outline') || []
)

const fieldSizeList = computed(() =>
  creatorRequireFieldInfoList.value?.filter((item) => item.fieldCode == 'size') || []
)

const fieldOutlineModelList = computed(() =>
  creatorRequireFieldInfoList.value?.filter((item) => item.fieldCode == 'outline_model') || []
)


const knowledgeUploadList = computed(() =>
  creatorRequireFieldInfoList.value?.filter((item) => item.fieldCode == 'knowledge-upload').map(item => ({
    fieldCode: item.fieldCode,
    fieldName: item.fieldName,
    fieldType: item.fieldType,
    isRequired: item.isRequired,
    fieldValue: item.fieldValue || '',
    defaultValue: item.defaultValue || '',
    options: item.options || '',
    description: item.description || '',
    placeholder: item.placeholder || '',
    maxLength: item.maxLength || 0,
    id: item.id || '',
    isShowField: true
  })) as SelectRequireInfo[] || []
)

const isUseLargeWorker = computed(() =>
  creatorsInfo.value?.config?.isUseLargeWorker || false
)

// 当前选中的类型值
const currentTypeValue = ref('')

// 判断是否显示 FieldRadioButton 组件
const shouldShowRadioButton = computed(() => {
  return (item: any) => {
    if (currentTypeValue.value === EditorContent.editorChart && item.fieldCode == 'chartType') {
      return true
    }
    if (currentTypeValue.value === EditorContent.editorImage && item.fieldCode == 'imageRatio') {
      return true
    }
    return false
  }
})

// 处理类型变化
// const handleTypeChange = (value: string) => {
//   currentTypeValue.value = value
//   topicValue.value = EditorContentType[value as keyof typeof EditorContentType] || ''

//   // 当类型变化时，清空相关字段的值
//   if (value != EditorContent.Chart) {
//     // 如果不是图表类型，清空图表类型字段
//     formValues.value['chartType'] = ''
//   }

//   if (value != EditorContent.Image) {
//     // 如果不是图片类型，清空图片比例字段
//     formValues.value['imageRatio'] = ''
//   }

//   // 如果切换到图表类型，设置默认值
//   if (value == EditorContent.Chart && !formValues.value['chartType']) {
//     const chartTypeField = creatorRequireFieldInfoList.value?.find(item => item.fieldCode === 'chartType')
//     if (chartTypeField) {
//       formValues.value['chartType'] = chartTypeField.defaultValue || 'bar'
//     }
//   }

//   // 如果切换到图片类型，设置默认值
//   if (value == EditorContent.Image && !formValues.value['imageRatio']) {
//     const imageRatioField = creatorRequireFieldInfoList.value?.find(item => item.fieldCode === 'imageRatio')
//     if (imageRatioField) {
//       formValues.value['imageRatio'] = imageRatioField.defaultValue || '4:3'
//     }
//   }
// }

// 监听 title 字段变化，同步 currentTypeValue
watch(() => formValues.value.title, (newValue) => {
  if (newValue !== undefined) {
    currentTypeValue.value = newValue
    // 只有在用户主动选择时才调用 handleTypeChange
    // 初始化时不调用，避免设置不必要的默认值
  }
}, { immediate: true })

// 监听字段值变化
watch(creatorRequireFieldInfoList, (newList) => {
  if (newList) {
    // 初始化表单值
    newList.forEach(item => {
      if (item.fieldType == 'slider') {
        formValues.value[item.fieldCode] = parseInt(item.defaultValue || item.fieldValue || '4')
      } else {
        formValues.value[item.fieldCode] = item.defaultValue || item.fieldValue || ''
      }

      // 如果是 title 字段，同时初始化 currentTypeValue
      if (item.fieldCode === 'title' && item.code === 'insight_chart') {
        currentTypeValue.value = item.defaultValue || item.fieldValue || ''
      }
    })
  }
}, { immediate: true })

// 监听表单值变化，同步回creatorRequireFieldInfoList
watch(formValues, (newValues) => {
  if (!props.creatorData?.details) return;

  // 更新原始数据
  Object.entries(newValues).forEach(([fieldCode, value]) => {
    const field = props.creatorData.details.find(item => item.fieldCode === fieldCode);
    if (field) {
      // 更新字段值
      field.fieldValue = String(value);
    }
  });
}, { deep: true });

// 监听topicValue变化，同步到原始数据
watch(topicValue, (newValue) => {
  if (props.creatorData?.details) {
    const field = props.creatorData.details.find(item => item.fieldCode === 'topic');
    if (field) {
      field.fieldValue = newValue;
    }
  }
});

const updateFormOutline = (value: string) => {
  formValues.value.outline = value;
}

const shouldShowCustomPaperOutline = computed(() => {
  const list = fieldOutlineList.value
  if (list.length > 0) {
    updateFormOutline(list[0].defaultValue || CreateSubmissionAssistType.ai);
    return true
  }
  if (creatorsInfo.value?.code == CreateContentType.information) {
    const list = creatorRequireFieldInfoList.value?.filter((item) => item.fieldCode == 'references')
    if (list?.length > 0 && list[0].fieldValue?.includes(CreateSubmissionAssistChineseType.custom)) {
      updateFormOutline(CreateSubmissionAssistType.custom);
      return true
    }
  }
  return false
})

const isShowOutline = ref(shouldShowCustomPaperOutline.value)

const clearField = () => {
  topicValue.value = ''

  // 更新topic值到原始数据
  if (props.creatorData?.details) {
    const field = props.creatorData.details.find(item => item.fieldCode === 'topic');
    if (field) {
      field.fieldValue = '';
    }
  }
}

const onOutlineBlur = (list: OutlineElement[], sizeLength: number) => {
  outlineValue = list
  customSizeLength = sizeLength
}


// const isExistFileList = computed(() => {
//   const files = fieldUploadFilesRefs.value?.$props.fileList
//   return files ? files.length > 0 : false
// })

// const isExistKnowledgeFileList = computed(() => {
//   const ids = fieldUploadFilesRefs.value?.$props.selectedIds
//   return ids ? ids.length > 0 : false
// })

const isShowUploadFile = (item: CreatorRequireFieldInfo) => {
  // 先判断是否为 references 字段且满足条件
  // if (item.fieldCode === 'references') {
  //   const list = referencesList.value
  //   if (list.length > 0) {
  //     const fieldValue = list[0].fieldValue
  //     if (fieldValue) {
  //       return (
  //         fieldValue.includes(CreateSubmissionAssistType.custom) ||
  //         fieldValue.includes(CreateSubmissionAssistChineseType.custom) ||
  //         fieldValue.includes(CreateSubmissionInformationType.upload)
  //       )
  //     }
  //   }
  //   return false
  // }

  // 如果是 upload 字段，则检查是否已经有满足条件的 references 字段
  if (item.fieldCode === 'upload') {
    // 检查是否已经有满足条件的 references 字段
    const hasValidReferences = referencesList.value.some(ref => {
      const fieldValue = ref.fieldValue
      return fieldValue && (
        fieldValue.includes(CreateSubmissionAssistType.custom) ||
        fieldValue.includes(CreateSubmissionAssistChineseType.custom) ||
        fieldValue.includes(CreateSubmissionInformationType.upload)
      )
    })

    // 如果已经有满足条件的 references 字段，则不显示 upload 字段
    // if (hasValidReferences) {
    //   return false
    // }

    // 如果没有 references 字段或没有满足条件的 references 字段，则显示 upload 字段
    return uploadList.value.length > 0
  }

  return false
}

const showShouldInput = (item: CreatorRequireFieldInfo) => {
  if (props.currentCode == 'book') {
    if (item.fieldCode == 'outline_ask') {
      if (fieldOutlineModelList.value.length > 0 && fieldOutlineModelList.value[0].fieldValue == CreateSubmissionOutlineModalType.yes) {
        return true
      }
      return false
    }
  }
  if (item.fieldCode == 'outline') {
    return false
  }
  if (item.fieldType == 'input-text') {
    return true
  }
  return false
}

const showShouldSlider = (item: CreatorRequireFieldInfo) => {
  if (props.currentCode == 'book' && item.fieldCode == 'size') {
    if (fieldOutlineModelList.value.length == 0) {
      return false
    }
    const value = fieldOutlineModelList.value[0].fieldValue || fieldOutlineModelList.value[0].defaultValue
    if (value != CreateSubmissionOutlineModalType.yes) {
      return true
    }
    return false
  }
  if (item.fieldCode == 'slider') {
    return true
  }
  return false
}

// 添加提交状态ref
const isSubmitting = ref(props.isSubmitLoading || false)

watch(() => props.isSubmitLoading, (_newValue) => {
  isSubmitting.value = _newValue
})

// 修改写作按钮处理函数
const handlePressCreate = async () => {

  if (!UserService.isLogined()) {
    user.openLoginModal()
    track('create_submission', props.creatorData.creator.id, '写作按钮点击')
    return
  }
  isSubmitting.value = true

  const outlineList = outlineValue.length == 0 ? outlineStore.getOutlineContent : outlineValue
  // const { outlineList, customSizeLength }
  // const outlineData = fieldOutlineTextareaRefs.value?.getOutlineAndCustomSizeLength()
  // console.log("formValues.value ==>", formValues.value)
  if (!validateForm(
    formValues.value,
    topicValue.value,
    creatorsInfo.value,
    creatorRequireFieldInfoList.value || [], //?.filter(item => item.isRequired === 'Y')
    isShowOutline.value,
    outlineList,
    isUploadOrSelectedKnowledgeFile.value
  )) {
    isSubmitting.value = false
    // toast.error('请填写完整信息')
    // message.error('请填写完整信息')
    scrollOutlineTextarea()
    return
  }
  try {
    const attachments = await handleUploadFile()
    // console.log("submit attachments ==>", attachments)
    const formData = getSubmitParams(
      formValues.value,
      topicValue.value,
      creatorsInfo.value,
      outlineList,
      customSizeLength || outlineStore.getOutlineLength,
      // outlineData?.outlineList || [],
      // (outlineData?.outlineList || []).length || outlineStore.getOutlineLength,
      fieldSizeList.value,
      attachments
    )
    // console.log("formData  ==> ", formData)
    if (['ppt', 'book'].includes(creatorsInfo.value?.code || '')) {
      emit("createOutline", formData)
      return
    }

    emit("submitForm", formData)

    track('create_submission', props.creatorData.creator.id, '写作按钮点击')
  } catch (error) {
    console.error(error)
    message.error('提交失败')
  } finally {
    isSubmitting.value = false
  }
}

const isUploadOrSelectedKnowledgeFile = computed(() => {
  const fileList = fieldUploadFilesRefs.value?.fileList

  const selectedKnowledgeFileIdList = fieldUploadFilesRefs.value?.selectedKnowledgeFileIds
  if (fileList?.length > 0 || (selectedKnowledgeFileIdList && selectedKnowledgeFileIdList.value.length > 0)) {
    return true
  }
  return false
})

const scrollOutlineTextarea = () => {
  const targetElement = document.querySelector('.outline-selector')
  if (targetElement) {
    // console.log('targetElement ==>', targetElement)
    targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' })

    isHighlight.value = true

    // 3 秒后移除闪烁效果
    setTimeout(() => {
      isHighlight.value = false
    }, 3000)
  }
}

const handleUploadFile = async () => {
  const fileIdList: Array<{ fileId: string; repositoryFileId?: string }> = []

  const fileList = fieldUploadFilesRefs.value?.fileList
  // console.log('fieldUploadFilesRefs.value ==>', fieldUploadFilesRefs.value)
  // console.log('fileList ==>', fileList)
  // if (!fileList?.length) return fileIdList

  if (fileList && fileList.length > 0) {

    for (const item of fileList) {
      // 在结果页或确认订单页，点编辑进入写作页后，从知识库选的文件，有此字段
      if (item.repositoryFileId) {
        fileIdList.push({ fileId: item.uid, repositoryFileId: item.repositoryFileId })
        continue
      }
      if (item.uid) {
        fileIdList.push({ fileId: item.uid })
        continue
      }
      if (item.fileUrl) {
        fileIdList.push({ fileId: item.uid })
        continue
      }

      try {
        // 检查文件哈希值
        const sha256 = await getFileSha256(item.file)
        const checkFileShaResult = await checkFileHash({
          sha256: `${sha256}`,
        })
        let params
        // 如果文件已存在，直接使用已有数据
        if (checkFileShaResult.data != null) {
          params = {
            fileName: item.name,
            fileUrl: checkFileShaResult.data.fileUrl,
            fileSha256: `${sha256}`
          }
        } else {
          const result = await generatePutUrl({ filename: item.name })
          if (!result.ok || !result.data) {
            message.error(item.name + '上传失败')
            continue
          }
          const response = await axios.put(result.data.url, item.file, {
            headers: {
              'Content-Type': result.data.contentType
            }
          })
          if (response.status !== 200) {
            message.error(item.name + '上传失败')
            continue
          }
          params = {
            fileName: item.name,
            fileUrl: removeQuestionMarkText(result.data.url),
            fileSha256: `${sha256}`
          }
        }
        // const credentials = result.data.response.credentials
        // const cosClientAndParams = await getUploadAliossSign({
        //   ...credentials,
        //   region: result.data.region,
        //   bucket: result.data.bucket
        // })

        // const uploadResult = await cosClientAndParams.multipartUpload(result.data.key, item.file, {
        //   progress: (percent: number) => {
        //     // 进度处理
        //   }
        // })

        // if (!uploadResult?.res?.status || !Array.isArray(uploadResult.res.requestUrls)) {
        //   continue
        // }
        // const fileUrl = removeQuestionMarkText(uploadResult.res.requestUrls[0])
        const uploadByUrlResult = await uploadByUrl(params)
        if (!uploadByUrlResult.ok) {
          message.error(item.name + '上传失败')
          continue
        }

        fileIdList.push({ fileId: uploadByUrlResult.data.id })
      } catch (error) {
        console.error('Upload error:', error)
        message.error('文件上传失败，请重试')
      }
    }
  }

  const selectedKnowledgeFileIdList = fieldUploadFilesRefs.value?.selectedKnowledgeFileIds
  if (selectedKnowledgeFileIdList && selectedKnowledgeFileIdList.value.length > 0) {
    fileIdList.push(...selectedKnowledgeFileIdList.value)
  }

  return fileIdList
}

const setupData = async () => {
  topicValue.value = ''

  const draftData = readSubmissionDraft(props.currentCode)

  if (!draftData) return

  if (draftData.formData.topic && draftData.formData.topic !== '0' && draftData.formData.topic !== '-') {
    topicValue.value = draftData.formData.topic
  }

  creatorRequireFieldInfoList.value?.forEach(item => {
    const lastValue = draftData.formData.params[item.fieldCode]
    if (lastValue) {
      formValues.value[item.fieldCode] = lastValue
    }
  })

  if (shouldShowCustomPaperOutline.value) {
    formValues.value.outline = CreateSubmissionAssistType.ai
    isShowOutline.value = true
  }

  if (draftData.formData.userOutline) {
    formValues.value.outline = CreateSubmissionAssistType.custom
    againEditOutlineList.value = JSON.parse(draftData.formData.userOutline)

    isShowOutline.value = true
  }

  const attachmentsFile = draftData.attachments || draftData.formData.attachments
  console.log("attachmentsFile ==>", attachmentsFile)
  if (attachmentsFile) {

    attachmentsFile.map((item: any) => {
      uploadedOfSuccessAttachments.value.push({
        name: item.fileName || '万能小in',
        title: item.fileName || '万能小in',
        fileType: getFileExtension(item.fileName || '万能小in'),
        wordCount: item.wordCount,
        id: item.id || item.fileId,
        uid: item.id || item.fileId,
        repositoryFileId: item.repositoryFileId //知识库选的文件有此值
      })
    })
    if (uploadedOfSuccessAttachments.value.length > 0) {
      if (referencesList.value.length > 0) {
        referencesList.value[0].defaultValue = CreateSubmissionAssistChineseType.custom
        referencesList.value[0].fieldValue = CreateSubmissionAssistChineseType.custom
      }
      formValues.value['references'] = CreateSubmissionAssistChineseType.custom
      formValues.value['upload'] = CreateSubmissionAssistChineseType.custom
    } else {
      formValues.value['references'] = CreateSubmissionAssistChineseType.ai
    }
  }

  console.log("uploadedOfSuccessAttachments ==>", uploadedOfSuccessAttachments.value)



}

const setPPTDraftData = async () => {
  await setupData()
}

onMounted(() => {
  setupData()
})

defineExpose({
  setPPTDraftData,
})
</script>

<style scoped>
.white-spin :deep(.ant-spin-dot-item) {
  background-color: white !important;
}
</style>