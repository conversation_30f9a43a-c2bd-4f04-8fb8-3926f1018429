<template>
  <a-modal v-model:open="modelValue" :title="null" :width="1000" :footer="null" :closable="true" :centered="true"
    :bodyStyle="{ height: modalHeight + 'px' }" :destroyOnClose="true" @cancel="handleCancel">
    <div class="flex flex-col h-full">
      <!-- 头部标题区域 -->
      <div class="flex items-start justify-between">
        <div>
          <h3 class="text-lg font-medium text-gray-900">从知识库选择文件</h3>
          <p class="mt-1 text-sm text-gray-500">选择合适的文件作为论文参考资料</p>
        </div>

        <!-- 上传进度提示 -->
        <div v-if="uploadingFiles.length > 0" class="ml-4 max-w-[300px]">
          <div v-for="file in uploadingFiles" :key="file.id"
            class="bg-white border border-gray-300 rounded-lg p-3 mb-2 shadow-sm">
            <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
              <span class="truncate flex-1 mr-2">{{ file.name }}</span>
              <span class="text-gray-400 whitespace-nowrap">{{ file.progress }}%</span>
            </div>
            <UProgress :value="file.progress" color="blue" />
          </div>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="flex flex-1 min-h-0">
        <!-- 左侧区域 -->
        <div class="w-[60%] flex flex-col min-h-0">
          <!-- 搜索框固定区域 -->
          <div class="pt-4 bg-white shrink-0">
            <div class="flex items-center">
              <div
                class="flex-1 flex items-center h-[43px] rounded-[10px] border border-[#2551B5] overflow-hidden mr-2">
                <div class="flex items-center flex-1 px-3">
                  <search theme="outline" size="18" fill="#2551B5" class="mr-2" />
                  <input type="text" placeholder="搜索文档标题、内容..."
                    class="w-full h-full text-sm bg-transparent border-none outline-none" v-model="searchQuery"
                    @keyup.enter="handleKnowledgeSearchFileData" />
                </div>
                <button
                  class="h-full px-4 bg-[#2551B5] text-white text-sm font-medium hover:bg-[#2551B5]/90 transition-colors"
                  @click="handleKnowledgeSearchFileData">
                  搜索
                </button>
              </div>
              <div class="pr-4">


                <XUploadKnowledgeFile :folder-id="currentFolderId" :files="knowledgefiles" :accept="props.options"
                  @ok="loadStoreKnowledgeFileData" @upload-start="handleUploadStart"
                  @upload-progress="handleUploadProgress" @upload-complete="handleUploadComplete"
                  @upload-success="handleUploadSuccess">
                  <div class="relative">
                    <!-- 一直显示的提示文字 -->
                    <div
                      class="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-white text-blue-700 text-xs px-3 py-2 rounded-lg border border-blue-200 shadow-lg whitespace-nowrap z-10">
                      上传本地文件
                      <!-- 箭头指向下方 -->
                      <div
                        class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-r-[6px] border-t-[6px] border-l-transparent border-r-transparent border-t-blue-200">
                      </div>
                      <div
                        class="absolute top-full left-1/2 transform -translate-x-1/2 -mt-[1px] w-0 h-0 border-l-[6px] border-r-[6px] border-t-[6px] border-l-transparent border-r-transparent border-t-white">
                      </div>
                    </div>
                    <!-- 按钮 -->
                    <button
                      class="h-full p-1 text-sm font-medium text-gray-700 transition-colors border border-gray-300 rounded-md hover:bg-gray-100">
                      <plus size="24" class="cursor-pointer" />
                    </button>
                  </div>
                </XUploadKnowledgeFile>

              </div>
            </div>

            <!-- 面包屑 -->
            <Breadcrumb v-if="breadcrumbPaths.length > 1" :paths="breadcrumbPaths" @click="handleBreadcrumbClick"
              class="mt-2" />
          </div>

          <!-- 文件列表可滚动区域 -->
          <div class="flex-1 pr-2 mt-4 overflow-y-auto">
            <!-- Loading 状态 -->
            <div v-if="loading" class="absolute inset-0 z-10 flex items-center justify-center bg-white/80">
              <div class="flex flex-col items-center">
                <!-- <div class="w-8 h-8 border-b-2 border-blue-500 rounded-full animate-spin"></div> -->
                <a-spin :spinning="true" class="text-gray-600"></a-spin>
                <span class="mt-2 text-sm text-gray-500">加载中...</span>
              </div>
            </div>

            <!-- 空状态 -->
            <div class="flex items-center justify-center h-full" v-if="!loading && knowledgeFileOptions.length === 0">
              <EmptyState />
            </div>

            <!-- 文件列表 -->
            <template v-else>
              <!-- 如果是聊天且当前页有文件就显示全选按钮 -->
              <div v-if="isChat && hasFilesInCurrentPage" class="flex items-center p-2 mb-2">
                <label class="cursor-pointer "><a-checkbox :checked="isSelectedAll" class="mr-2"
                    @click.stop="handleSelectAll">
                  </a-checkbox><span class="pl-2 text-gray-800">全选</span></label>
              </div>
              <div v-for="item in knowledgeFileOptions" :key="item.id" class="file-tree-item"
                :style="{ paddingLeft: `${item.level * 20}px` }">
                <div class="flex items-center px-2 py-1.5 mb-2 rounded-lg group" :class="[
                  item.status == 'error' ? 'cursor-not-allowed opacity-60' : 'cursor-pointer hover:bg-gray-100/80'
                ]" @click="handleItemClick(item)">
                  <!-- 如果是聊天不显示文件夹 复选框  -->
                  <template v-if="!isChat">
                    <!-- Checkbox  -->
                    <a-checkbox :checked="isSelected(item.id)"
                      :indeterminate="item.isFolder && isPartiallySelected(item)" :disabled="item.status == 'error'"
                      @change="(e) => handleCheckboxChange(e, item)" class="mr-2" @click.stop>
                    </a-checkbox>
                  </template>
                  <template v-else>
                    <div v-if="!item.isFolder">
                      <a-checkbox :checked="isSelected(item.id)"
                        :indeterminate="item.isFolder && isPartiallySelected(item)" :disabled="item.status == 'error'"
                        @change="(e) => handleCheckboxChange(e, item)" class="mr-2" @click.stop>
                      </a-checkbox>
                    </div>
                  </template>

                  <!-- 文件/文件夹图标 -->
                  <div class="px-2">
                    <Iconfont :name="`${item.isFolder ? 'folder' : getFileIcon(item?.title || '')}`" :size="20">
                    </Iconfont>
                  </div>
                  <!-- 文件信息 -->
                  <div class="flex-1 min-w-0" @click="item.isFolder && toggleFolder(item)">
                    <h4 class="text-sm font-medium text-gray-700 truncate"> {{ getFileTitle(item) }} </h4>
                  </div>

                  <!-- 文件夹右侧箭头 -->
                  <div v-if="item.isFolder" class="ml-2 transition-transform duration-200 transform"
                    :class="[item.isExpanded ? 'rotate-90' : '']">
                    <right theme="outline" size="16" fill="#6B7280" />
                  </div>
                </div>
              </div>
            </template>
          </div>

          <!-- 分页控件固定区域 -->
          <div v-if="isMobile" class="py-3 text-center bg-white shrink-0">
            <a-pagination v-model:current="knowledgeStore.currentPage" :total="knowledgeStore.total" show-less-items
              :showSizeChanger="false" simple @change="handlePageChange" />
          </div>
          <div v-else class="px-2 pb-3 bg-white shrink-0">
            <Pagination v-model:current="knowledgeStore.currentPage" :page-count="knowledgeStore.pageSize"
              :total="knowledgeStore.total" @change="handlePageChange"></Pagination>
          </div>
        </div>

        <!-- 右侧区域 -->
        <div class="w-[40%] flex flex-col min-h-0 border-l border-gray-200">
          <!-- 右侧标题固定区域 -->
          <div class="px-4 pt-4 bg-white shrink-0">
            <div class="flex items-center justify-between">
              <h3 class="text-sm font-medium text-gray-700">
                已选择文件 ({{ selectedDocs.length }}/{{ maxLength }})
              </h3>
              <button v-if="selectedDocs.length > 0" class="text-sm text-blue-500 hover:text-blue-600"
                @click="clearSelection">
                清空
              </button>
            </div>
          </div>

          <!-- 右侧已选列表可滚动区域 -->
          <div class="flex-1 px-4 mt-4 overflow-y-auto">
            <div v-if="selectedDocs.length === 0"
              class="flex flex-col items-center justify-center h-full text-gray-400">
              <inbox theme="outline" size="32" fill="currentColor" class="mb-2" />
              <span class="text-sm">暂未选择文件</span>
            </div>

            <div v-else class="space-y-2">
              <div v-for="doc in selectedDocsList" :key="doc.id"
                class="flex items-center px-3 py-1.5 rounded-md bg-gray-50 group">
                <div class="pr-2">
                  <Iconfont :name="`${doc.isFolder ? 'folder' : getFileIcon(doc?.title || '')}`" :size="20">
                  </Iconfont>
                </div>
                <div class="flex-1 min-w-0">
                  <h4 class="text-sm font-medium text-gray-700 truncate">{{ doc.title }}</h4>
                </div>
                <button class="ml-2 text-gray-400 hover:text-red-500" @click="removeSelected(doc.id)">
                  <close theme="outline" size="16" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部区域 -->
      <div class="h-[60px] shrink-0 px-6 flex items-center justify-end space-x-4 border-t border-gray-200 bg-white">
        <a-button class="px-8" @click="handleCancel">取消</a-button>
        <a-button type="primary" class="px-8" @click="handleConfirmSelect">
          确认
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { getFilesByIds } from '@/api/repositoryFile';
import Breadcrumb from '@/components/Common/Breadcrumb.vue';
import EmptyState from '@/components/EmptyState.vue';
import { Close, Inbox, Plus, Right, Search } from '@icon-park/vue-next';
import { Button as AButton, Checkbox as ACheckbox, Modal as AModal, message } from 'ant-design-vue';
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { useMobileDetection } from '~/composables/useMobileDetection';
import { useKnowledgeStore } from '~/stores/knowledgeStore';
import { useUserStore } from '~/stores/user';
import XUploadKnowledgeFile from './XUploadKnowledgeFile.vue';

const props = defineProps({

  appCode: {
    type: String,
    default: () => ''
  },
  selectedIds: {
    type: Array,
    default: () => []
  },
  options: {
    type: String,
    default: () => ''
  },
  maxLength: {
    type: Number,
    default: () => 20
  },
  isChat: {
    type: Boolean,
    default: () => false
  },
  code: {
    type: String,
    default: () => ''
  },
  fileType: {
    type: String,
    default: () => 'file'
  }
})

const emit = defineEmits(['update:modelValue', 'select'])

const { isMobile } = useMobileDetection()

const modelValue = ref(true)

const searchQuery = ref('')
const loading = ref(false)
// 使用 ref 来跟踪选中状态
const selectedDocs = ref([...props.selectedIds])
// 独立存储选中的文件完整信息，不依赖文件列表
const selectedDocsData = ref([])
// 移除本地的 selectedFolderIds，使用 store 中的状态
// const selectedFolderIds = ref([]) //用于保存选中文件夹的id，只做展示
// 新增的状态和方法
const breadcrumbPaths = ref([{ id: 0, title: '知识库' }]);
const defaultParams = props.isChat ? { status: '' } : {}

const currentFolderId = ref('0')
const knowledgefiles = ref([])
// 直接使用 store 中的数据，保持响应式
const knowledgeFileOptions = computed(() => knowledgeStore.knowledgeFileOptions)

// 上传进度相关状态
const uploadingFiles = ref([])

// 开始上传
const handleUploadStart = (file) => {
  uploadingFiles.value.push(file)
}

// 上传进度
const handleUploadProgress = (file) => {
  const index = uploadingFiles.value.findIndex(f => f.id === file.id)
  if (index !== -1) {
    uploadingFiles.value[index] = { ...file }
  }
}

// 上传完成
const handleUploadComplete = (fileId) => {
  uploadingFiles.value = uploadingFiles.value.filter(f => f.id !== fileId)
}

// 处理上传成功的文件信息
const handleUploadSuccess = (uploadedFiles) => {

  uploadedFiles.forEach((file) => {
    // 🎯 直接选中新上传的文件
    if (!selectedDocs.value.includes(file.fileId)) {

      selectedDocs.value.push(file.fileId)

      // 同时存储文件的完整信息到独立数组
      const fileData = {
        id: file.fileId,
        title: file.fileName,
        fileType: file.fileType || 'file',
        isFolder: false,
        createTime: file.uploadTime,
        // 可以添加更多需要显示的字段
      }
      selectedDocsData.value.push(fileData)

      console.log(`✅ 已自动选中文件: ${file.fileName} (ID: ${file.fileId})`)
    }
  })
}

const knowledgeStore = useKnowledgeStore()
const userStore = useUserStore()

// 计算当前空间ID
const spaceId = computed(() => {
  return userStore.currentLoginInfo?.id || ''
})


const screenHeight = ref(window.innerHeight); // 用于存储当前屏幕高度

const MODAL_PADDING = 80; // Modal 上下内边距总和
const MIN_HEIGHT = 500; // 最小高度
const MAX_HEIGHT = 780; // 最大高度

// 计算 Modal 高度
const modalHeight = computed(() => {
  const height = screenHeight.value - MODAL_PADDING;
  return Math.min(Math.max(height, MIN_HEIGHT), MAX_HEIGHT);
});

// 监听窗口大小变化
const updateScreenHeight = () => {
  screenHeight.value = window.innerHeight;
};

// 搜索
const handleKnowledgeSearchFileData = async () => {
  const keywords = searchQuery.value.trim();
  knowledgeStore.reqParams.keywords = keywords;
  knowledgeStore.isSearchMode = true;
  knowledgeStore.hasSearchKeyword = !!keywords;
  knowledgeStore.reqParams.fileNames = props.options;
  knowledgeStore.reqParams.fileType = props.fileType;

  knowledgeStore.total = 0
  knowledgeStore.totalPages = 0
  knowledgeStore.currentPage = 1

  if (!keywords) {
    // 如果搜索框为空，恢复到普通列表模式
    knowledgeStore.currentPage = 1;
    await loadStoreKnowledgeFileData();
    return;
  }

  loading.value = true;
  try {
    await knowledgeStore.loadKnowledgeSearchFileData();
    // knowledgeFileOptions 现在是计算属性，会自动更新
  } catch (error) {
    message.error('搜索失败');
  } finally {
    loading.value = false;
  }
}


// 处理项目点击的方法
const handleItemClick = async (item) => {
  // 如果文件状态为 error，阻止点击操作
  if (item.status == 'error') {
    message.warning(item.processData.error.message || '该文件处理失败，无法选择');
    return;
  }

  await toggleDocument(item);
}

// 更新选择状态的方法
const toggleDocument = async (doc) => {
  if (doc.isFolder) {
    // 如果是文件夹，加载文件夹内容
    try {
      loading.value = true;
      // 更新面包屑路径
      breadcrumbPaths.value = [...breadcrumbPaths.value, { id: doc.id, title: doc.title }];
      currentFolderId.value = `${doc.id}`;
      await knowledgeStore.loadKnowledgeFileData(doc.id, defaultParams);
      // knowledgeFileOptions 现在是计算属性，会自动更新
    } catch (error) {
      message.error('加载文件夹内容失败');
    } finally {
      loading.value = false;
    }
    return;
  }

  const index = selectedDocs.value.indexOf(doc.id);
  if (index === -1) {
    // 选中文件：检查是否超过限制
    selectedDocs.value.push(doc.id);
    selectedDocsData.value.push(doc);
  } else {
    // 取消选中：同时移除ID和完整信息
    selectedDocs.value.splice(index, 1);
    const dataIndex = selectedDocsData.value.findIndex(item => item.id === doc.id);
    if (dataIndex !== -1) {
      selectedDocsData.value.splice(dataIndex, 1);
    }
  }
}

// 检查是否选中
const isSelected = (id) => {
  // 使用 store 中的 selectedFolderIds
  return [...selectedDocs.value, ...knowledgeStore.selectedFolderIds].includes(id)
}
// 检查当前页是否有文件（非文件夹且非错误状态）
const hasFilesInCurrentPage = computed(() => {
  return knowledgeFileOptions.value.some(d => !d.isFolder && d.status != 'error')
})

const isSelectedAll = computed(() => {
  // 只考虑非文件夹且非错误状态的文件
  const validFiles = knowledgeFileOptions.value.filter(d => !d.isFolder && d.status != 'error')
  const validIds = validFiles.map(d => d.id)
  const selectedValidFiles = selectedDocs.value.filter(d => validIds.includes(d))
  return validIds.length > 0 && validIds.length == selectedValidFiles.length
})

// 全选 
const handleSelectAll = () => {
  if (isSelectedAll.value) {
    // 如果全选了，就取消当前页的所有选中
    const currentPageFiles = knowledgeFileOptions.value.filter(d => !d.isFolder && d.status != 'error')
    const currentPageIds = currentPageFiles.map(d => d.id)

    // 从选中ID列表中移除当前页的文件ID
    selectedDocs.value = selectedDocs.value.filter(d => !currentPageIds.includes(d));

    // 从选中数据列表中移除当前页的文件
    selectedDocsData.value = selectedDocsData.value.filter(item => !currentPageIds.includes(item.id));
  } else {
    // 否则就全选当前页的文件（排除错误状态的文件）
    const currentPageFiles = knowledgeFileOptions.value.filter(d => !d.isFolder && d.status != 'error')

    // 检查是否有错误状态的文件被跳过
    const errorFiles = knowledgeFileOptions.value.filter(d => !d.isFolder && d.status == 'error')
    if (errorFiles.length > 0) {
      // message.warning(`有 ${errorFiles.length} 个文件处理失败，已自动跳过`);
    }

    currentPageFiles.forEach(file => {
      if (!selectedDocs.value.includes(file.id)) {
        // 添加到选中ID列表
        selectedDocs.value.push(file.id);
        // 添加到选中数据列表
        selectedDocsData.value.push(file);
      }
    });
  }
}

// 监听父组件传入的 selectedIds 变化
watch(() => props.selectedIds, async (newIds) => {
  selectedDocs.value = [...newIds]

  // 如果有新的选中ID，需要获取对应的文件信息
  if (newIds.length > 0) {
    await loadSelectedFilesData(newIds)
  } else {
    selectedDocsData.value = []
  }
}, { deep: true })

// 加载选中文件的完整信息
const loadSelectedFilesData = async (fileIds) => {
  try {
    // 这里可以调用API获取文件详情，或者从store中查找
    // 暂时先从store的allKnowledgeFileOptions中查找
    const foundFiles = knowledgeStore.allKnowledgeFileOptions.filter(item =>
      fileIds.includes(item.id)
    )
    console.log('🔍 从store找到的文件:', knowledgeStore.allKnowledgeFileOptions)
    const missingIds = fileIds.filter(id => !foundFiles.some(file => file.id === id))
    if (missingIds.length > 0) {
      console.log('🔍 需要通过API获取的文件ID:', missingIds)
      // 调用API获取文件详情
      const apiFiles = await fetchFilesByIds(missingIds)
      selectedDocsData.value = [...foundFiles, ...apiFiles]
      console.log('🔍 合并后的选中文件数据:', selectedDocsData.value)
    } else {
      selectedDocsData.value = foundFiles
      console.log('🔍 全部从store找到，选中文件数据:', selectedDocsData.value)
    }

    // 如果store中没有找到所有文件，可能需要调用API获取
    // TODO: 如果需要，可以在这里添加API调用逻辑
  } catch (error) {
    console.error('加载选中文件信息失败:', error)
  }
}

// 通过API获取文件详情的函数
const fetchFilesByIds = async (fileIds) => {
  try {
    // 调用批量获取文件详情的API
    const response = await getFilesByIds({
      fileIds: fileIds,
      spaceId: spaceId.value
    })

    console.log('📡 API响应:', response)

    if (!response.success || !response.data) {
      console.error('API调用失败:', response)
      return []
    }

    // 转换API返回的数据格式为组件需要的格式
    const apiFiles = response.data.map(item => ({
      id: item.id,
      title: item.fileName || item.name || `文件 ${item.id.slice(-8)}`,
      fileType: item.fileType || 'file',
      isFolder: item.type === 'folder',
      createTime: item.createTime,
      wordCount: item.wordCount,
      status: item.status,
      processData: item.processData,
    }))

    console.log('📡 转换后的文件数据:', apiFiles)
    return apiFiles

  } catch (error) {
    console.error('API获取文件详情失败:', error)

    // 如果API调用失败，返回占位符数据
    const placeholderFiles = fileIds.map(id => ({
      id: id,
      title: `文件 ${id.slice(-8)}`,
      fileType: 'file',
      isFolder: false,
      createTime: new Date().toISOString(),
    }))

    return placeholderFiles
  }
}

const handleClose = () => {
  emit('update:modelValue', false)
  knowledgeStore.resetData()
  // 重置面包屑路径
  breadcrumbPaths.value = [{ id: 0, title: '知识库' }];
  // 重置搜索相关状态
  searchQuery.value = '';
  knowledgeStore.isSearchMode = false;
  knowledgeStore.hasSearchKeyword = false;
  // 清空选中状态 - 同时清空两个数组
  selectedDocs.value = [];
  selectedDocsData.value = [];
}

const handleCancel = () => {
  handleClose()
}

const handleConfirmSelect = () => {

  if (!props.isChat && selectedDocs.value.length == 0) {
    message.warning('请选择文档')
    return
  }

  // 使用独立存储的选中文件数据
  const list = selectedDocsData.value

  // 根据 code 进行不同的限制检查
  if (list.length > props.maxLength) {
    message.warning(`最大选择${props.maxLength}个文件`)
    return
  }
  emit('select', list)
  emit('update:modelValue', false)
}

// 分页
const handlePageChange = async (page) => {
  // if (knowledgeStore.currentPage === page) return;
  // knowledgeStore.currentPage = page;
  await loadStoreKnowledgeFileData();
}

const loadStoreKnowledgeFileData = async () => {
  loading.value = true;
  try {
    const folderId = breadcrumbPaths.value[breadcrumbPaths.value.length - 1].id
    if (props.appCode == 'book') {
      knowledgeStore.reqParams.fileType = ''
    }
    currentFolderId.value = `${folderId}`
    await knowledgeStore.loadKnowledgeFileData(folderId, defaultParams);
    // knowledgeFileOptions 现在是计算属性，会自动更新

    // console.log("knowledgeFileOptions 456 ==>", knowledgeFileOptions.value)
  } catch (error) {
    message.error('加载知识库列表失败');
  } finally {
    loading.value = false;
  }
}

// watchEffect(() => {
// if (!knowledgeStore.appCode) {
//   return
// }
// if (knowledgeStore.appCode != props.appCode) {
//   knowledgeStore.initKnowledgeFileData()
//   console.log("knowledgeStore.appCode != props.appCode");
//   loadStoreKnowledgeFileData()
// }
// })

onMounted(async () => {
  window.addEventListener('resize', updateScreenHeight);
  updateScreenHeight(); // 初始化高度

  knowledgeStore.setAppCode(props.appCode)
  knowledgeStore.setOptions(props.options)
  knowledgeStore.reqParams.fileType = props.fileType

  // 先加载文件列表，确保 allKnowledgeFileOptions 有数据
  console.log("knowledgeStore.knowledgeFileOptions.length == 0");
  await loadStoreKnowledgeFileData()

  // 然后加载已选中文件的数据
  if (props.selectedIds.length > 0) {
    console.log("selectedIds 123 ==>", props.selectedIds)
    await loadSelectedFilesData(props.selectedIds)
  }
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateScreenHeight);
  // 清理知识库状态检查定时器
  knowledgeStore.stopStatusCheckTimer();
})

// 新增的状态和方法
const selectedDocsList = computed(() => {
  // 直接返回独立存储的选中文件数据，不依赖文件列表
  return selectedDocsData.value
})

const clearSelection = () => {
  selectedDocs.value = []
  selectedDocsData.value = []
  // 清空 store 中的选中文件夹
  knowledgeStore.selectedFolderIds = []
}

const removeSelected = (id) => {
  const index = selectedDocs.value.indexOf(id)
  if (index > -1) {
    selectedDocs.value.splice(index, 1)
  }

  const dataIndex = selectedDocsData.value.findIndex(item => item.id === id);
  if (dataIndex !== -1) {
    selectedDocsData.value.splice(dataIndex, 1);
  }
}

const getFileTitle = (item) => {
  // 处理错误状态
  if (item.processData?.error) {
    return '文件学习失败，无法添加';
  }
  // 使用文件名
  if (item.title) {
    return item.title;
  }
  // 默认状态
  return '文件正在学习中…';
};

// 点击文件夹
const toggleFolder = (item) => {
  item.isExpanded = !item.isExpanded
}

const isPartiallySelected = (folder) => {
  if (!folder.children?.length) return false;
  const selectedChildren = folder.children.filter(child => isSelected(child.id));
  return selectedChildren.length > 0 && selectedChildren.length < folder.children.length;
};

// 点击复选框
const handleCheckboxChange = async (e, item) => {
  const checked = e.target.checked;

  if (item.isFolder) {
    loading.value = true;
    try {
      // 获取文件夹下所有文件
      const files = await knowledgeStore.listAllFiles(item.id);

      if (checked) {
        // 🔧 过滤掉状态为错误的文件，只选择状态正常的文件
        const validFiles = files.filter(file => file.status !== 'error');
        const fileIds = validFiles.map(file => file.id);
        // console.log("fileIds 456 ==>", fileIds)
        // 使用 store 的方法添加选中文件夹
        knowledgeStore.addSelectedFolder(item.id);
        selectedDocs.value = [...new Set([...selectedDocs.value, ...fileIds])];

        // 🔧 修复：同时更新 selectedDocsData，只添加状态正常的文件
        validFiles.forEach(file => {
          // 检查文件是否已经在 selectedDocsData 中，避免重复添加
          const existsInSelectedData = selectedDocsData.value.some(selectedFile => selectedFile.id === file.id);
          if (!existsInSelectedData) {
            selectedDocsData.value.push(file);
          }
        });

        // 使用Map进行去重处理，只添加状态正常的文件
        const uniqueFiles = new Map();
        // 先添加现有的文件
        knowledgeStore.allKnowledgeFileOptions.forEach(file => uniqueFiles.set(file.id, file));
        // 添加新的文件，如果ID相同会自动覆盖，只添加状态正常的文件
        validFiles.forEach(file => uniqueFiles.set(file.id, file));
        // 转换回数组
        knowledgeStore.allKnowledgeFileOptions = Array.from(uniqueFiles.values());
      } else {
        // 🔧 取消选中时，只处理状态正常的文件（因为错误文件本来就没有被选中）
        const validFiles = files.filter(file => file.status !== 'error');
        const fileIds = validFiles.map(file => file.id);
        // 使用 store 的方法移除选中文件夹
        knowledgeStore.removeSelectedFolder(item.id);
        selectedDocs.value = selectedDocs.value.filter(id => !fileIds.includes(id));
        // 🔧 修复：同时从 selectedDocsData 中移除文件夹下的有效文件
        selectedDocsData.value = selectedDocsData.value.filter(selectedFile => !fileIds.includes(selectedFile.id));
        knowledgeStore.allKnowledgeFileOptions = knowledgeStore.allKnowledgeFileOptions.filter(item => !fileIds.includes(item.id));
      }
    } catch (error) {
      message.error('获取文件夹内容失败');
    } finally {
      loading.value = false;
    }
  } else {
    // 如果是文件，直接切换选中状态
    if (checked && !selectedDocs.value.includes(item.id)) {
      selectedDocs.value.push(item.id);
      // 同时添加到完整信息数组
      selectedDocsData.value.push(item);
    } else if (!checked) {
      const index = selectedDocs.value.indexOf(item.id);
      if (index > -1) {
        selectedDocs.value.splice(index, 1);
        // 同时从完整信息数组中移除
        const dataIndex = selectedDocsData.value.findIndex(doc => doc.id === item.id);
        if (dataIndex !== -1) {
          selectedDocsData.value.splice(dataIndex, 1);
        }
      }
    }
  }
};

const getAllChildrenIds = (folder) => {
  const ids = [folder.id];
  if (folder.children) {
    folder.children.forEach(child => {
      if (child.isFolder) {
        ids.push(...getAllChildrenIds(child));
      } else {
        ids.push(child.id);
      }
    });
  }
  return ids;
};

// 面包屑
const handleBreadcrumbClick = async (item, index) => {

  try {
    loading.value = true;
    // 更新面包屑路径
    breadcrumbPaths.value = breadcrumbPaths.value.slice(0, index + 1);
    currentFolderId.value = `${item.id}`;
    await knowledgeStore.loadKnowledgeFileData(item.id, defaultParams);
    // knowledgeFileOptions 现在是计算属性，会自动更新
  } catch (error) {
    message.error('加载文件夹内容失败');
  } finally {
    loading.value = false;
  }
}

// 更新 watch 监听
watch(() => searchQuery.value, (newValue) => {
  // 当输入框清空时，不自动触发搜索
  if (!newValue.trim()) {
    knowledgeStore.hasSearchKeyword = false;
  }
})

const getFileNameAndExtension = (fullName) => {
  const match = fullName.match(/^(.+)(\.[^.]+)$/)
  return {
    name: match ? match[1].toLowerCase() : fullName.toLowerCase(),
    extension: (match ? match[2] : '').toLowerCase().replace('.', '')
  }
}

const getFileIcon = (name) => {
  if (!name) return KnowledgeFileIcon.text;

  const { extension } = getFileNameAndExtension(name);
  // 根据扩展名返回对应图标
  switch (extension) {
    case 'pdf':
      return KnowledgeFileIconSvg.pdf;
    case 'doc':
    case 'docx':
      return KnowledgeFileIconSvg.doc;
    case 'ppt':
    case 'pptx':
      return KnowledgeFileIconSvg.ppt;
    case 'img':
    case 'jpg':
    case 'jpeg':
    case 'png':
      return KnowledgeFileIconSvg.img;
    case 'txt':
    case 'md':
    case 'text':
      return KnowledgeFileIconSvg.text;
    case 'xlsx':
    case 'csv':
      return KnowledgeFileIconSvg.xlsx;
    default:
      return KnowledgeFileIconSvg.encode;
  }
}
</script>

<style scoped>
/* 修改 ant-design-vue 的默认样式 */
:deep(.ant-modal-content) {
  padding: 0;
  overflow: hidden;
}

:deep(.ant-modal-body) {
  height: 100%;
}

/* 其他样式保持不变 */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #E5E7EB transparent;
  -webkit-overflow-scrolling: touch;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: #E5E7EB;
  border-radius: 3px;
}

/* 文件树项动画 */
.file-tree-item {
  transition: all 0.2s ease-in-out;
}

.file-tree-item:hover {
  transform: translateX(2px);
}

/* 优化checkbox样式 */
:deep(.ant-checkbox-wrapper) {
  font-size: 14px;
}

:deep(.ant-checkbox) {
  top: 0;
}

/* 优化hover效果 */
.group:hover .transform {
  opacity: 1;
}
</style>