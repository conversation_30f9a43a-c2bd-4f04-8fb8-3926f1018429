<template>
    <!-- 隐藏的文件输入框 -->
    <input ref="fileInput" type="file" class="hidden" multiple :accept="formattedAccept" @change="handleFileSelect" />

    <div @click="handleUploadLocal">
        <slot></slot>
    </div>
</template>
<script lang="ts" setup>
import { addFiles, checkFileHash } from '@/api/repositoryFile.js';
import { generatePutUrl, uploadByUrl } from '@/api/upload';
import { BindPhoneModal, HTTP_STATUS } from '@/utils/constants';
import { getFileSha256, removeQuestionMarkText } from '@/utils/utils';
import { message } from 'ant-design-vue';
import axios from 'axios';
import { computed, ref } from 'vue';
import { useSpaceStore } from '~/stores/space';
import { useUserStore } from '~/stores/user';


const props = defineProps({
    files: {
        type: Array,
        default: [],
    },
    pasteFileInfo: {
        type: Object as () => any,
        default: null
    },
    accept: {
        type: String,
        default: '.pdf,.docx,.doc,.ppt,.pptx,.txt,.md,.jpg,.png,.jpeg,.xlsx,.csv'
    },
    folderId: {
        type: String,
        default: '0'
    },
})
const emit = defineEmits(['update:files', 'ok', 'upload-progress', 'upload-start', 'upload-complete', 'upload-success'])

const user = useUserStore()
// 获取空间信息
const spaceStore = useSpaceStore()
const { spaceQuotaBytes, spaceUsedBytes } = storeToRefs(spaceStore)

// 文件输入框引用
const fileInput = ref<HTMLInputElement | undefined>()
const uploadingFiles = ref<UploadingFile[]>([])
// 收集上传成功的文件信息
const uploadedFilesInfo = ref<any[]>([])

interface UploadingFile {
    id: number
    name: string
    progress: number
}

const spaceId = computed(() => {
    return user.currentLoginInfo?.id || ''
})

const formattedAccept = computed(() => {
    if (!props.accept) {
        return '.pdf,.docx,.doc,.ppt,.pptx,.txt,.md,.jpg,.png,.jpeg,.xlsx,.csv'
    }

    // 处理 props.accept，确保每个扩展名都带点号
    return props.accept.split(',').map(ext => {
        const trimmed = ext.trim().toLowerCase()
        // 如果没有点号，添加点号；如果有点号，保持原样
        return trimmed.startsWith('.') ? trimmed : `.${trimmed}`
    }).join(',')
})

// 根据accept prop生成允许的扩展名数组
const allowedExtensions = computed(() => {
    if (!props.accept) return []
    console.log('🎯 接收到的 accept 参数:', props.accept)
    const extensions = props.accept.split(',').map(ext => ext.trim().toLowerCase().replace('.', ''))
    console.log('🎯 解析后的允许扩展名:', extensions)
    return extensions
})

// 检查文件是否被允许
const isFileAllowed = (file: File) => {
    const fileExtension = file.name.toLowerCase().split('.').pop()
    if (!fileExtension) return false

    console.log(`🎯 检查文件: ${file.name}, 扩展名: ${fileExtension}`)
    console.log(`🎯 允许的扩展名列表:`, allowedExtensions.value)

    // 检查扩展名是否在允许列表中
    const isAllowed = allowedExtensions.value.includes(fileExtension)
    console.log(`🎯 文件 ${file.name} 是否被允许: ${isAllowed}`)

    return isAllowed
}
// 处理本地上传按钮点击
const handleUploadLocal = () => {
    // 触发文件选择对话框
    if (fileInput.value) {
        fileInput.value.click()
    }
}

// 处理文件选择
const handleFileSelect = (e: Event) => {
    const userStore = useUserStore()
    if (!userStore.isLogined) {
        userStore.openLoginModal()
        return
    }

    const files = (e.target as HTMLInputElement).files
    if (files) {
        const invalidFiles = Array.from(files).filter(file => {
            return !isFileAllowed(file)
        })

        if (invalidFiles.length > 0) {
            message.error(`以下文件格式不支持：${invalidFiles.map(f => f.name).join(', ')}`)
            return
        }

        // 检查文件大小限制（50MB）
        const maxFileSize = 50 * 1024 * 1024 // 50MB
        const oversizedFiles = Array.from(files).filter(file => file.size > maxFileSize)

        if (oversizedFiles.length > 0) {
            message.error(`以下文件大小超过50MB限制：${oversizedFiles.map(f => f.name).join(', ')}`)
            return
        }

        handleFiles(Array.from(files))
    }
}

// 修改处理文件上传方法，添加上传限制
const handleFiles = async (files: File[]) => {
    // 检查是否有有效文件需要上传
    const validFiles = files.filter(file => {
        return isFileAllowed(file)
    })

    if (validFiles.length === 0) {
        return
    }

    // 清空之前的上传文件信息记录
    uploadedFilesInfo.value = []

    // 用于跟踪上传成功的文件数量
    let successCount = 0
    const totalValidFiles = validFiles.length

    // 依次处理每个文件
    for (const file of validFiles) {
        // 检查单个文件大小限制（50MB）
        const maxFileSize = 50 * 1024 * 1024 // 50MB
        if (file.size > maxFileSize) {
            message.error(`文件 ${file.name} 大小超过50MB限制`)
            continue
        }

        if (file.size > spaceQuotaBytes.value - spaceUsedBytes.value) {
            message.error('知识库空间不足，请升级后再添加')
            emit('ok')
            return
        }

        // 检查文件类型是否被允许
        if (!isFileAllowed(file)) {
            message.error(`文件 ${file.name} 格式不支持`)
            continue
        }

        // 将 uploadingFile 定义在 try 块外部
        const uploadingFile: UploadingFile = {
            id: Date.now() + Math.random(),
            name: file.name,
            progress: 0
        }
        uploadingFiles.value.push(uploadingFile)

        // 通知父组件开始上传
        emit('upload-start', uploadingFile)

        try {
            const uploadedFileInfo = await handleUploadFileByFile(file, uploadingFile)
            if (uploadedFileInfo) {
                uploadedFilesInfo.value.push(uploadedFileInfo)
            }
            successCount++

            // 如果所有有效文件都上传成功，才通知父组件
            if (successCount === totalValidFiles) {
                message.success('所有文件上传成功')
                // 传递上传成功的文件信息给父组件
                emit('upload-success', uploadedFilesInfo.value)
                emit('ok')
            }
        } catch (error) {
            console.error('Error uploading file:', error)
            message.error(`文件 ${file.name} 上传失败`)
            // 从上传列表中移除
            uploadingFiles.value = uploadingFiles.value.filter(f => f.id !== uploadingFile.id)
            // 通知父组件上传完成（失败）
            emit('upload-complete', uploadingFile.id)
        }
    }
}
const handleUploadFileByFile = async (file: File, uploadingFile: UploadingFile) => {
    try {
        // 检查文件哈希值
        const sha256 = await getFileSha256(file)

        const checkFileShaResult = await checkFileHash({
            sha256: `${sha256}`,
        })

        if (!checkFileShaResult.ok) {
            uploadingFiles.value = uploadingFiles.value.filter(f => f.id !== uploadingFile.id)
            throw new Error(checkFileShaResult.message || '文件检查失败')
        }

        let uploadByUrlParams;

        // 如果文件已存在，直接使用已有数据
        if (checkFileShaResult.data != null) {
            uploadByUrlParams = {
                fileName: checkFileShaResult.data.fileName,
                fileUrl: checkFileShaResult.data.fileUrl,
                fileSha256: checkFileShaResult.data.fileSha256
            }
            // 更新进度为100%
            const fileIndex = uploadingFiles.value.findIndex(f => f.id === uploadingFile.id)
            if (fileIndex !== -1) {
                uploadingFiles.value[fileIndex].progress = 100
                // 通知父组件进度更新
                emit('upload-progress', { ...uploadingFiles.value[fileIndex] })
            }
        } else {
            const generatePutUrlResult = await generatePutUrl({
                filename: file.name,
            })
            if (!generatePutUrlResult.ok) {
                message.error(`文件 ${file.name} 上传失败`)
                // 从上传列表中移除
                uploadingFiles.value = uploadingFiles.value.filter(f => f.id !== uploadingFile.id)
                throw new Error('获取上传URL失败')
            }

            // 使用axios发送PUT请求上传文件
            const response = await axios.put(generatePutUrlResult.data.url, file, {
                headers: {
                    'Content-Type': generatePutUrlResult.data.contentType
                },
                onUploadProgress: (progressEvent) => {
                    // 更新上传进度
                    if (progressEvent.total) {
                        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                        const fileIndex = uploadingFiles.value.findIndex(f => f.id === uploadingFile.id);
                        if (fileIndex !== -1) {
                            uploadingFiles.value[fileIndex].progress = percentCompleted;
                            // 通知父组件进度更新
                            emit('upload-progress', { ...uploadingFiles.value[fileIndex] })
                        }
                    }
                }
            });

            if (response.status !== 200) {
                throw new Error(`上传失败: ${response.status} ${response.statusText}`);
            }

            // 确保上传进度为100%
            const fileIndex = uploadingFiles.value.findIndex(f => f.id === uploadingFile.id);
            if (fileIndex !== -1) {
                uploadingFiles.value[fileIndex].progress = 100;
                // 通知父组件进度更新
                emit('upload-progress', { ...uploadingFiles.value[fileIndex] })
            }

            uploadByUrlParams = {
                fileName: file.name,
                fileUrl: removeQuestionMarkText(generatePutUrlResult.data.url),
                fileSha256: `${sha256}`,
            }
        }

        const uploadByUrlResult = await uploadByUrl(uploadByUrlParams)

        if (!uploadByUrlResult.ok) {
            message.error(`文件 ${file.name} 上传失败`)
            // 从上传列表中移除
            uploadingFiles.value = uploadingFiles.value.filter(f => f.id !== uploadingFile.id)
            throw new Error('文件注册失败')
        }

        const res = await addFiles({
            "spaceId": spaceId.value,
            "folderId": props.folderId || '',
            fileIds: [uploadByUrlResult.data.id]
        })

        if (res?.code == HTTP_STATUS.MOBILE_NOT_BOUND) {
            const user = useUserStore()
            user.setShowPhoneBoundModal({
                status: BindPhoneModal.SHOW_BINDING,
            })
            throw new Error('需要绑定手机号')
        }

        if (!res || !res.ok) {
            // 从上传列表中移除
            uploadingFiles.value = uploadingFiles.value.filter(f => f.id !== uploadingFile.id)
            throw new Error('添加文件到文件夹失败')
        }

        // 上传成功后延迟移除进度条
        uploadingFiles.value = uploadingFiles.value.filter(f => f.id !== uploadingFile.id)
        // 通知父组件上传完成（成功）
        emit('upload-complete', uploadingFile.id)

        // 返回上传成功的文件信息
        return {
            fileName: file.name,
            fileSize: file.size,
            fileType: file.type,
            fileId: uploadByUrlResult.data.id,
            fileUrl: uploadByUrlParams.fileUrl,
            fileSha256: uploadByUrlParams.fileSha256,
            uploadTime: new Date().toISOString(),
            folderId: props.folderId
        }

    } catch (error: any) {
        console.error('Error uploading file:', error)
        // 从上传列表中移除
        uploadingFiles.value = uploadingFiles.value.filter(f => f.id !== uploadingFile.id)
        throw error
    }
}
</script>