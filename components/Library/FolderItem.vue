<template>
    <div ref="containerRef" class="flex flex-wrap py-4 border-b border-[#D0DCFA] justify-between space-y-1">
        <div v-for="folder in folders" :key="folder.id"
            class="relative flex flex-col items-center rounded-lg  cursor-pointer hover:bg-indigo-100 group/item transition-all duration-200 w-[80px] h-[70px] lg:w-[100px]  lg:h-[90px]"
            @click="handleFolderClick(folder)">
            <div class="absolute top-1 right-1 hidden group-hover/item:flex flex-col gap-2">
                <a-popover trigger="hover">
                    <template #content>
                        <div class="flex flex-col gap-2 items-center">
                            <button
                                class="box-border inline-flex px-2 py-1 text-sm text-[#333333] font-normal rounded-md hover:bg-blue-50 hover:text-blue-700 transition-colors"
                                @click="handleRenameClick(folder)">
                                <span class="inline-block text-[#333333]">重命名</span>
                            </button>
                            <button
                                class="box-border inline-flex px-2 py-1 text-sm text-[#333333] font-normal rounded-md hover:bg-blue-50 hover:text-blue-700 transition-colors"
                                @click="handleMoveClick(folder)">
                                <span class="inline-block text-[#333333]">移动</span>
                            </button>

                            <a-popconfirm @confirm="handleDeleteClick(folder.id)" title="删除文件不会减少已使用空间，是否确认删除?"
                                ok-text="确定" cancel-text="取消">
                                <button
                                    class="box-border inline-flex px-2 py-1 text-sm rounded-md hover:bg-red-50 text-gray-600 transition-colors ">
                                    删除
                                </button>
                            </a-popconfirm>
                        </div>
                    </template>
                    <button @click.stop>
                        <more theme="outline" size="24" fill="#999999" />
                        <!-- <img src="https://static-1256600262.cos.ap-shanghai.myqcloud.com/xiaoin-h5/image/more-icon.png"
                                class="w-4 h-4"> -->
                    </button>
                </a-popover>
            </div>
            <div class="flex-1 flex flex-col items-center justify-center w-full">
                <Iconfont name="folder" :size="44"></Iconfont>
                <h3
                    class="text-xs sm:text-sm text-gray-600 font-medium text-center truncate mt-1 w-full px-1 overflow-hidden">
                    {{ folder.folderName || folder.name }}
                </h3>
            </div>
        </div>

        <!-- 添加隐藏的占位元素来填满最后一行 -->
        <div v-for="n in placeholderCount" :key="`placeholder-${n}`" class="w-[80px] lg:w-[100px] h-0 invisible"></div>

        <!-- 重命名和移动文件的弹窗 -->
        <RenameFolderModal v-if="showRenameModal" v-model="showRenameModal" :item="currentRenameItem"
            @confirm="handleRenameConfirm" type="folder" />
    </div>
</template>

<script setup lang="ts">
import { deleteFolder, updateFolder } from '@/api/repositoryFile';
import RenameFolderModal from '@/components/Library/RenameFolderModal.vue';
import type { RepositoryFolder } from '@/services/types/repositoryFile';
import { More } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { useUserStore } from '~/stores/user';

const props = defineProps<{
    folders: RepositoryFolder[]
}>()

const emit = defineEmits<{
    (e: 'rename'): void
    (e: 'move', folder: RepositoryFolder, type: 'folder'): void
    (e: 'select', folder: RepositoryFolder): void
    (e: 'delete', id: string): void
}>()



const user = useUserStore()

const spaceId = computed(() => {

    return user.currentLoginInfo?.id || ''

})

// 移动文件夹
const handleMoveClick = (folder: RepositoryFolder) => {
    emit('move', folder, 'folder')
}

// 删除文件夹
const handleDeleteClick = async (id: string) => {
    // console.log('删除文件夹：', id)

    const params = {
        folderId: id
    }

    const res = await deleteFolder(spaceId.value, params)
    if (!res.ok) {
        // message.error(res.message || '删除失败')
        return
    }
    message.success(res.message || '删除成功')
    emit('delete', id)
}

// 重命名-------------------------------------------------
const showRenameModal = ref(false)
const currentRenameItem = ref<any>(null)


const handleRenameClick = (item: RepositoryFolder) => {
    currentRenameItem.value = item
    showRenameModal.value = true
}


const handleRenameConfirm = async (item: { id: number, filename: string }) => {

    const params = {
        folderId: item.id.toString(),
        folderName: item.filename
    }
    const res = await updateFolder(spaceId.value, params)
    if (!res.ok) {
        // message.error(res.message || '修改失败')
        return
    }
    message.success(res.message || '修改成功')
    emit('rename')
}

// 文件夹点击处理
const handleFolderClick = (folder: RepositoryFolder) => {
    emit('select', folder)
}


const containerRef = ref<HTMLElement | null>(null)
const placeholderCount = ref(0) // 占位元素数量

// 防抖函数
const debounce = (func: Function, wait: number) => {
    let timeout: NodeJS.Timeout
    return function executedFunction(...args: any[]) {
        const later = () => {
            clearTimeout(timeout)
            func(...args)
        }
        clearTimeout(timeout)
        timeout = setTimeout(later, wait)
    }
}

// 动态计算占位元素数量
const updatePlaceholderCount = () => {
    const container = containerRef.value
    if (!container) return

    const containerWidth = container.clientWidth
    const cardWidth = 100  // 对应 w-[100px]

    // 计算每行能容纳的卡片数（不考虑间距，因为 justify-between 会自动分配间距）
    const cardsPerRow = Math.floor(containerWidth / cardWidth)
    const totalCards = props.folders.length

    // 如果每行容量为0或者没有卡片，直接返回
    if (cardsPerRow <= 0 || totalCards === 0) {
        placeholderCount.value = 0
        return
    }
    // 计算最后一行的卡片数
    const lastRowCards = totalCards % cardsPerRow

    // 如果最后一行不满，计算需要多少个占位元素来填满
    if (lastRowCards > 0 && lastRowCards < cardsPerRow) {
        placeholderCount.value = cardsPerRow - lastRowCards
        console.log('需要', placeholderCount.value, '个占位元素')
    } else {
        placeholderCount.value = 0
        console.log('不需要占位元素')
    }
}


// 创建防抖版本的更新函数，减少延迟
const debouncedUpdatePlaceholderCount = debounce(updatePlaceholderCount, 50)

// resize 处理函数：立即执行一次，然后防抖
const handleResize = () => {
    updatePlaceholderCount() // 立即执行
    debouncedUpdatePlaceholderCount() // 防抖执行
}

// folders 改变时重新计算
watch(() => props.folders.length, () => {
    updatePlaceholderCount()
})

// ResizeObserver 实例
let resizeObserver: ResizeObserver | null = null

onMounted(() => {
    updatePlaceholderCount()

    // 使用 ResizeObserver 监听容器大小变化，比 window resize 更精确
    if (containerRef.value && 'ResizeObserver' in window) {
        resizeObserver = new ResizeObserver(() => {
            updatePlaceholderCount()
        })
        resizeObserver.observe(containerRef.value)
    } else {
        // 降级到 window resize
        window.addEventListener('resize', handleResize)
    }
})

onBeforeUnmount(() => {
    if (resizeObserver) {
        resizeObserver.disconnect()
    } else {
        window.removeEventListener('resize', handleResize)
    }
})
</script>