<template>
    <div class="space-y-2">
        <template v-if="transformedItems.length > 0">
            <div v-for="item in transformedItems" :key="item.id" class="cursor-pointer">
                <div @click="handleOperate(item, NewKnowledgeAssistantType.questions)"
                    class="overflow-hidden transition-all duration-200 border bg-white/80 backdrop-blur-sm rounded-xl border-gray-200/70 group hover:border-blue-700/30">
                    <div class="flex items-center justify-between w-full px-2 py-1">
                        <div class="flex-shrink-0 mx-1 lg:mx-2">
                            <Iconfont :name="getFileIcon(item)" :size="20"></Iconfont>
                        </div>

                        <!-- 左侧：文件信息 -->
                        <div class="w-full flex flex-row justify-between flex-1 min-w-0 space-x-2">
                            <!-- 左上：文件名 -->
                            <h3
                                class="w-[60%] my-1 text-sm font-bold text-gray-600 truncate transition-colors group-hover:text-blue-600 whitespace-nowrap">
                                {{ item.name || '--' }}
                            </h3>
                            <!-- 左下：文件信息 -->
                            <div
                                class="w-[40%] flex items-center space-x-1 my-1 text-sm text-gray-400 truncate transition-colors whitespace-nowrap">
                                <span v-if="item.fileSize" class="flex items-center">
                                    <span> {{
                                        formatStorageSize(item.fileSize, 2) }}</span>
                                    <!-- <span v-else class="max-w-[7em] truncate">{{ item.createMember }}</span> -->
                                </span>
                                <span v-if="item.wordCount" class="flex items-center">
                                    {{ (item.wordCount / 10000).toFixed(2) }}万字
                                </span>
                                <!-- <span v-if="item.createTime" class="flex items-center">
                                    {{ formatDate(item.createTime) }}
                                </span> -->
                            </div>
                        </div>

                        <!-- 右侧：操作按钮 -->
                        <div class="flex items-center flex-shrink-0 gap-2"
                            v-if="item?.status == RepositoryFileStatus.DONE">
                            <button @click.stop.prevent="handleOperate(item, NewKnowledgeAssistantType.questions)"
                                class="box-border inline-flex items-center px-2 my-1 text-sm text-gray-500 rounded-md hover:text-blue-700">
                                <BookIconfont name="xueshusousuo" :size="20" />
                                <!-- <img src="https://static-1256600262.cos.ap-shanghai.myqcloud.com/xiaoin-h5/image/tiwen-icon.png"
                                    alt="" class="w-4 h-4.5 mr-1"> -->
                            </button>
                        </div>

                        <!-- 第三行：功能按钮 -->
                        <div v-if="item?.processData?.error" class="flex flex-row items-center gap-1">
                            <span
                                class="create-time bg-[#FFF2F2] rounded-md error-text text-sm px-2 py-1 text-[#FF4242] border border-white/0 truncate whitespace-nowrap">
                                {{ getErrorMessage(item as any) }}
                            </span>
                            <span
                                v-if="item.processData.error.code === KnowledgeFileProcessCode.NO_ENOUGH_SPACE || item.processData.error.code === KnowledgeFileProcessCode.NO_ENOUGH_WORD">
                                <a href="javascript:void(0);"
                                    class="create-time rounded-md error-text text-sm p-1 text-[#FF4242] border border-white/0 truncate whitespace-nowrap"
                                    @click.stop.prevent="upgradeAssistant">升级></a>
                            </span>
                            <button @click.stop.prevent="handleDelete(item)"
                                class="inline-flex items-center px-2 py-1 text-sm text-gray-600 transition-colors rounded-md hover:bg-red-50">
                                <Delete theme="outline" class="w-3.5 h-3.5" />
                            </button>
                        </div>
                        <div v-else-if="(item.wordCount ?? 0) < 1"
                            class="text-[#2551B5] bg-[#E7EDFE] rounded-md text-sm px-2 py-1 border border-white/0 flex items-center">
                            <span class="truncate whitespace-nowrap">解析中</span>
                        </div>

                        <div v-else-if="item.status == RepositoryFileStatus.ERROR"
                            class="flex flex-row items-center gap-1">
                            <span
                                class="create-time bg-[#FFF2F2] rounded-md error-text text-sm px-2 py-1 text-[#FF4242] border border-white/0 truncate whitespace-nowrap">
                                学习失败
                            </span>
                            <button @click.stop.prevent="handleDelete(item)"
                                class="inline-flex items-center px-2 py-1 text-sm text-gray-600 transition-colors rounded-md hover:bg-red-50">
                                <Delete theme="outline" class="w-3.5 h-3.5" />
                            </button>
                        </div>
                        <div v-else-if="item?.status != RepositoryFileStatus.DONE"
                            class="text-[#0D8093] bg-[#E2F6FD] rounded-md text-sm px-2 py-1 border border-white/0 flex items-center">
                            <span class="truncate whitespace-nowrap">学习中</span>
                        </div>
                        <a-popover v-else trigger="hover">
                            <template #content>
                                <div class="flex flex-col gap-2">
                                    <div class="flex flex-col items-center gap-1">
                                        <button
                                            @click.stop.prevent="handleOperate(item, NewKnowledgeAssistantType.guide)"
                                            class="box-border inline-flex items-center px-2 py-1 text-sm text-[#333333] font-normal rounded-md hover:bg-blue-50 hover:text-blue-700 transition-colors ">
                                            <span class="inline-block text-[#333333]">{{
                                                NewKnowledgeAssistantTypeCn.guide }}</span>
                                        </button>
                                        <button
                                            @click.stop.prevent="handleOperate(item, NewKnowledgeAssistantType.mindmap)"
                                            class="box-border inline-flex items-center px-2 py-1 text-sm text-[#333333] font-normal rounded-md hover:bg-blue-50 hover:text-blue-700 transition-colors ">
                                            <span class="inline-block text-[#333333]">{{
                                                NewKnowledgeAssistantTypeCn.mindmap
                                            }}</span>
                                        </button>

                                        <button
                                            @click.stop.prevent="handleOperate(item, NewKnowledgeAssistantType.translation)"
                                            class="box-border inline-flex items-center px-2 py-1 text-sm text-[#333333] font-normal rounded-md hover:bg-blue-50 hover:text-blue-700 transition-colors ">
                                            <span class="text-[#333333]">{{ NewKnowledgeAssistantTypeCn.translation
                                            }}</span>
                                        </button>

                                        <button
                                            @click.stop.prevent="handleOperate(item, NewKnowledgeAssistantType.notes)"
                                            class="box-border inline-flex items-center px-2 py-1 text-sm text-[#333333] font-normal rounded-md hover:bg-blue-50 hover:text-blue-700 transition-colors ">
                                            <span class="inline-block text-[#333333]">{{
                                                NewKnowledgeAssistantTypeCn.notes }}</span>
                                        </button>

                                        <button @click.stop.prevent="handleDownload(item)" v-if="item.type !== 'html'"
                                            class="box-border inline-flex items-center px-2 py-1 text-sm text-[#333333] font-normal rounded-md hover:bg-blue-50 hover:text-blue-700 transition-colors ">
                                            <span class="inline-block text-[#333333]">{{
                                                NewKnowledgeAssistantTypeCn.download }}</span>
                                        </button>
                                        <button v-if="getPermissions(item)" @click.stop.prevent="handleRename(item)"
                                            class="box-border inline-flex items-center px-2 py-1 text-sm text-[#333333] font-normal rounded-md hover:bg-blue-50 hover:text-blue-700 transition-colors ">
                                            <span class="inline-block text-[#333333]">{{
                                                NewKnowledgeAssistantTypeCn.rename }}</span>
                                        </button>
                                        <button v-if="getPermissions(item)" @click.stop.prevent="handleMoveFile(item)"
                                            class="box-border inline-flex items-center px-2 py-1 text-sm text-[#333333] font-normal rounded-md hover:bg-blue-50 hover:text-blue-700 transition-colors">
                                            <span class="inline-block text-[#333333]">{{
                                                NewKnowledgeAssistantTypeCn.move }}</span>
                                        </button>

                                        <a-popconfirm v-if="getPermissions(item)" title="删除文件不会减少已使用空间，是否确认删除?"
                                            ok-text="确定" cancel-text="取消" @confirm="handleDelete(item)">
                                            <button
                                                class="box-border inline-flex items-center px-2 py-1 text-sm text-gray-600 transition-colors rounded-md hover:bg-red-50 ">
                                                删除
                                            </button>
                                        </a-popconfirm>
                                    </div>
                                </div>
                            </template>
                            <button @click.stop class="px-2 py-1.5">
                                <img src="https://static-1256600262.cos.ap-shanghai.myqcloud.com/xiaoin-h5/image/more-icon.png"
                                    class="w-5 h-5">
                            </button>
                        </a-popover>
                    </div>
                </div>
            </div>
        </template>
    </div>
    <!-- 添加重命名弹窗 -->
    <RenameFolderModal type="file" v-if="showRenameModal" v-model="showRenameModal" :item="currentRenameItem"
        @confirm="handleRenameConfirm" />
</template>

<script setup lang="ts">
import { getDownloadLink, nameUpdate, repositoryFileDelete } from '@/api/repositoryFile';
import RenameFolderModal from '@/components/Library/RenameFolderModal.vue';
import type { NewRepositoryFile } from '@/services/types/repositoryFile';
import { useUserStore } from '@/stores/user';
import {
    KnowledgeFileIcon,
    KnowledgeFileProcessCode,
    NewKnowledgeAssistantType,
    NewKnowledgeAssistantTypeCn,
    RepositoryFileStatus
} from '@/utils/constants';
import { formatStorageSize } from '@/utils/utils';
import {
    Delete
} from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import type { PropType } from 'vue';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useTracking } from '~/composables/useTracking';
import { useRechargeStore } from '~/stores/recharge';
const props = defineProps({
    items: {
        type: Array as PropType<NewRepositoryFile[]>,
        required: true,
    },
    links: {
        type: Array as PropType<any[]>,
        required: false,
    },
    // PropType<{ id: number; name: string; fileCount: number; updateTime: string }[]>
})

const { track } = useTracking();


const emit = defineEmits<{
    (e: 'move', file: NewRepositoryFile, type: 'file'): void
    (e: 'delete', id: string): void
    (e: 'rename', item: any): void
}>()

const router = useRouter()
const rechargeStore = useRechargeStore()

const user = useUserStore()


const upgradeAssistant = () => {

    rechargeStore.openRechargeModal()
}

const transformItem = (item: NewRepositoryFile) => {
    // 增加安全检查，确保 createTime 是有效值
    let formattedTime = item.createTime.match(/^(\d{4})-(\d{2})-(\d{2})/) ? item.createTime.replace(/^(\d{4})-(\d{2})-(\d{2}) .*$/, '$1-$2-$3') : item.createTime;

    return {
        ...item,
        type: item.type === 'html' ? 'html' : 'file',
        createTime: formattedTime,
    }
}

const transformedItems = computed(() => {
    return props.items.map(item => transformItem(item))
})


const spaceId = computed(() => {
    return user.currentLoginInfo?.id || ''
})


const getPermissions = (item: NewRepositoryFile): boolean => {
    if (item.userId === user.knowledgeAssistantMemberInfo?.userId) {
        return true
    }
    return false;
};


const getErrorMessage = (record: {
    type: keyof typeof getKnowledgeFileType;
    processData?: {
        error?: {
            code: string;
            message: string;
        };
    };
}) => {
    if (!record.processData?.error) return '';
    const { code, message } = record.processData.error;
    if (code === KnowledgeFileProcessCode.NO_DOCUMETS) {
        if (record.type === KnowledgeFileType.html) {
            return '由于网站限制，未能读取网页中的内容'
        }

        if (record.type === KnowledgeFileType.file) {
            return message || '文件内容无法读取'
        }
        return `${getKnowledgeFileType[record.type]}格式错误`
    }
    return message || ''
}

const handleDelete = async (item: NewRepositoryFile) => {

    const res = await repositoryFileDelete(spaceId.value, { fileId: item.id, folderId: item.folderId })
    if (!res.ok) {
        return
    }
    message.success(res.message || '删除成功')
    emit('delete', item.id)
    track('library_delete', item.id, '文件删除')
}

const handleOperate = (item: NewRepositoryFile, type: NewKnowledgeAssistantType) => {

    // 获取当前操作的文件
    const currentFile = transformedItems.value.find(res => res.id === item.id)

    if (currentFile?.processData?.error) {
        message.info(getErrorMessage(item as any))
        return
    }

    if ((currentFile?.wordCount ?? 0) < 1) {
        message.info('解析中')
        return
    }


    if (item.status === RepositoryFileStatus.ERROR) {
        const errorMessage = item.processData?.error?.message || '学习失败'
        message.error(errorMessage)
        return
    }

    // 如果文件未完成处理，显示提示信息并返回
    if (currentFile?.status !== RepositoryFileStatus.DONE) {
        const errorMessage = item.processData?.error?.message || '学习中'
        message.info(errorMessage)
        return
    }

    try {
        // 检查是否已有数据，如果有则先删除
        if (sessionStorage.getItem('library_back_data')) {
            sessionStorage.removeItem('library_back_data')
        }

        // 只需保存面包屑信息
        if (props.links && props.links.length > 0) {
            const backData = {
                links: props.links
            }

            sessionStorage.setItem('library_back_data', JSON.stringify(backData))
            // console.log('保存面包屑到 sessionStorage:', backData)
        }
    } catch (error) {
        console.error('保存面包屑到 sessionStorage 失败:', error)
    }

    router.push(`/library/${item.id}?type=${type}`)
    track(`library_${type}`, item.id, `文件${NewKnowledgeAssistantTypeCn[type as keyof typeof NewKnowledgeAssistantTypeCn]}`)
}


// 移动文件-------------------------------------------------
const handleMoveFile = (file: NewRepositoryFile) => {
    emit('move', file, 'file')
}


const formatDate = (dateString: string) => {
    if (!dateString) return '';

    // 使用工具函数获取日期部分
    const formattedDateTime = formatDateTimeString(dateString);
    // 只返回日期部分，去掉时间
    return formattedDateTime.split(' ')[0];

    // const date = new Date(dateString);
    // return date.toISOString().split('T')[0];
}



// 下载文件-------------------------------------------------
const handleDownload = async (item: NewRepositoryFile) => {
    const params = {
        id: item.id,
        spaceId: spaceId.value
    }

    const res = await getDownloadLink(params)
    if (!res.ok) {
        // message.error(res.message || '下载失败')
        return
    }

    // 从原始文件名中获取扩展名
    const originalName = item.name || ''
    const extension = originalName.split('.').pop() || ''

    // 构建新的文件名（原文件名 + 扩展名）
    const fileName = item.name ? item.name : `未命名文件.${extension}`

    try {
        const response = await fetch(res.data.sourceFileUrl)
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = fileName
        document.body.appendChild(link)
        link.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(link)
    } catch (error) {
        message.error('下载失败，请稍后重试')
    }
}

// 重命名-------------------------------------------------
const showRenameModal = ref(false)
const currentRenameItem = ref<any>(null)
const handleRename = (item: any) => {
    currentRenameItem.value = item
    showRenameModal.value = true
}

const handleRenameConfirm = async (item: { id: number, filename: string }) => {
    try {
        const res = await nameUpdate(spaceId.value, { fileName: item.filename, fileId: item.id })
        if (!res.ok) {
            // message.error(res.message || '重命名失败')
            return
        }
        message.success('重命名成功')
        showRenameModal.value = false
        emit('rename', item)

    } catch (error) {
        message.error('重命名失败')
    }
}

const getFileNameAndExtension = (fullName: string) => {
    const match = fullName.match(/^(.+)(\.[^.]+)$/)
    return {
        name: match ? match[1].toLowerCase() : fullName.toLowerCase(),
        extension: (match ? match[2] : '').toLowerCase().replace('.', '')
    }
}

const getFileIcon = (item: NewRepositoryFile) => {
    if (!item.name) return KnowledgeFileIcon.encode;

    const { extension } = getFileNameAndExtension(item.name);


    // 根据扩展名返回对应图标
    switch (extension) {
        case 'pdf':
            return KnowledgeFileIconSvg.pdf;
        case 'doc':
        case 'docx':
            return KnowledgeFileIconSvg.doc;
        case 'ppt':
        case 'pptx':
            return KnowledgeFileIconSvg.ppt;
        case 'img':
        case 'jpg':
        case 'jpeg':
        case 'png':
            return KnowledgeFileIconSvg.img;
        case 'txt':
        case 'md':
        case 'text':
            return KnowledgeFileIconSvg.text;
        case 'xlsx':
        case 'csv':
            return KnowledgeFileIconSvg.xlsx;
        default:
            return KnowledgeFileIconSvg.encode;
    }
}
</script>