<template>
    <!--知识库提问--->
    <div class="flex flex-col flex-1 h-full overflow-hidden">
        <Entrance :is-visible-top="false" :is-agent="true" :is-knowledge="true" :exampleList="exampleList || []"
            :repository-folder-id-list="repositoryFolderIdList || []" :session-id="sessionId"
            :is-new-session-id="isNewSessionId" :key="sessionId" :session-from="sessionFrom"
            :is-empty-document="isEmptyDocument" :session-list="sessionList" @change-session-id="handleChangeSessionId"
            :question-mode="questionModeValue" />
    </div>
</template>

<script setup lang="ts">
import { type RepositoryFile } from '@/services/types/repositoryFile';
import type { AppMessageRecord } from '~/services/types/appMessage';
const Entrance = defineAsyncComponent(() => import('~/components/Chat/Entrance.vue'));
interface Props {
    knowledgeDetail: RepositoryFile | null
    repositoryFolderIdList: string[] | null
    sessionId: string
    exampleList: string[]
    isEmptyDocument: boolean
    sessionList: AppMessageRecord[]
}
const props = withDefaults(defineProps<Props>(), {
    knowledgeDetail: null,
    sessionId: '',
    isEmptyDocument: false,
    sessionList: [] as any
})
const questionModeValue = ref([QuestionTypeEnum.useR1])
const isNewSessionId = computed(() => props.sessionId.length < 2 ? true : false)
const sessionFrom = computed(() => {
    const list = props.repositoryFolderIdList || []
    return list.length > 0 ? SessionFrom.KnowledgeFolder : SessionFrom.KnowledgeFolderRoot
})
// const isLoading = ref(true)
// onMounted(() => {
//     const chat = useChatStore()
//     if (props.sessionId) {
//         chat.setIsNewSessionId(false)
//     } else {
//         chat.setIsNewSessionId(true)
//     }
//     isLoading.value = false
// })
const emit = defineEmits(['changeSessionId'])
// 切换sessionId事件
const handleChangeSessionId = (_sessionId: string) => {
    emit('changeSessionId', _sessionId)
}

</script>