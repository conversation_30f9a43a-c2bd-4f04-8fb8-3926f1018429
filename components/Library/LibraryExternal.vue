<template>
    <div class="flex flex-col space-y-2">
        <button v-for="tab in tabs" :key="tab.value" @click="handleAddFromSource(tab.value)"
            class="flex-1 flex items-center space-x-2 text-xs sm:text-sm px-3 sm:px-4 py-2 text-[#333333] rounded-lg transition-colors font-medium hover:text-[#2551B5]">
            <!-- <component :is="tab.icon" theme="outline" size="18" class="text-inherit" :fill="'currentColor'" /> -->
            <BookIconfont :name="tab.icon" :size="16"></BookIconfont>
            <span>{{ tab.label }}</span>
        </button>


        <UModal :model-value="modelValue" :ui="{
            width: 'sm:max-w-lg md:max-w-xl lg:max-w-2xl',
            container: 'items-center',
        }">
            <div class="w-full flex justify-evenly items-center px-2 mb-[20px] ">
                <div class="flex-1 p-2"></div>
                <div class="flex p-2 ">
                    <div class="px-4 py-2 text-base transition-colors relative group mt-4">
                        <span class="pb-1 inline-block text-[#333333]">{{tabs.find(tab => tab.value ===
                            activeTab)?.label
                        }}</span>
                    </div>
                </div>
                <div class="flex-1 flex justify-end p-2">
                    <button @click="modelValue = false" class="text-gray-400 hover:text-gray-600">
                        <close theme="outline" size="18" fill="#333" />
                    </button>
                </div>
            </div>

            <div class="mb-[30px] mx-[20px] sm:mb-[30px] sm:mx-[40px] md:mb-[50px] md:mx-[70px]">
                <div class="mb-5">
                    <label class="text-sm font-medium  text-[#333333]">{{tabs.find(tab => tab.value ===
                        activeTab)?.slogan
                        }}</label>
                </div>

                <div class="mb-8">
                    <div class="relative">
                        <textarea v-model="inputValue" :rows="5" type="text"
                            :placeholder="tabs.find(tab => tab.value === activeTab)?.placeholder"
                            :maxlength="currentMaxLength"
                            class="w-full pl-4 pr-16 py-2.5 text-sm bg-white border border-gray-200 rounded-xl focus:outline-none focus:border-blue-300/50 focus:ring-1 focus:ring-blue-300/50"></textarea>
                        <button @click="clearField"
                            class="absolute right-3.5 bottom-3.5 px-2 py-1 text-xs text-gray-400 hover:text-gray-600">
                            清空
                        </button>
                    </div>
                    <div class="flex justify-end mt-1">
                        <span class="text-xs" :class="isOverLimit ? 'text-red-500' : 'text-gray-400'">
                            {{ inputValue?.length || 0 }} / {{ currentMaxLength }}
                        </span>
                    </div>
                </div>

                <div class="flex justify-center">
                    <button @click="importContent" :disabled="!isValidUrl"
                        class="px-6 py-3 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-500 rounded-lg transition-colors shadow-sm">
                        {{tabs.find(tab => tab.value === activeTab)?.buttonText}}
                    </button>
                </div>
            </div>
        </UModal>
    </div>
</template>

<script setup lang="ts">
import { addFiles } from '@/api/repositoryFile.js';
import { generatePutUrl, uploadByUrl } from '@/api/upload';
import { removeQuestionMarkText } from '@/utils/utils';
import { Close } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import axios from 'axios';
import { computed, ref } from 'vue';
import { useUserStore } from '~/stores/user';

// 定义组件 props
const props = defineProps<{
    folderId?: string
}>()


const user = useUserStore()
const spaceId = computed(() => {
    return user.currentLoginInfo?.id || ''
})


const emit = defineEmits(['action'])

const modelValue = ref(false)
const activeTab = ref('url')
const inputValue = ref('')

const handleAddFromSource = (type: string) => {
    activeTab.value = type
    modelValue.value = true
}

const tabs = ref([
    {
        label: '网址导入',
        value: 'url',
        slogan: '请避免非法抓取他人网站的侵权行为，保证链接可公开访问，且网站内容可复制抓取的网页仅为内容固定不变的静态网页，例如新闻文章、产品介绍等',
        placeholder: '请粘贴网址或输入链接，一次支持添加1个网址',
        buttonText: '导入网页',
        maxLength: 500,
        icon: 'wangzhidaoru'
    },
    {
        label: '搜索添加',
        value: 'search',
        slogan: '搜索专业学术文献，一键打包下载',
        placeholder: '请输入你想搜索的文献题目或关键词',
        buttonText: '搜索',
        maxLength: 300,
        icon: 'sousuotianjia'
    },
    {
        label: '文本粘贴',
        value: 'paste',
        slogan: '以TXT格式保存到知识库',
        placeholder: '请输入你想保存文本内容',
        buttonText: '添加',
        maxLength: 10000,
        icon: 'wenbenniantie'
    }
])

// 计算当前标签页的最大字符数限制
const currentMaxLength = computed(() => {
    const currentTab = tabs.value.find(tab => tab.value === activeTab.value)
    return currentTab?.maxLength || 1000
})

// 检查是否超过字数限制
const isOverLimit = computed(() => {
    return (inputValue.value?.length || 0) > currentMaxLength.value
})

const isValidUrl = computed(() => {
    // 首先检查是否超过字数限制
    if (isOverLimit.value) {
        return false
    }

    if (activeTab.value === 'paste') {
        // 文本粘贴模式下，只要有内容就可以
        return inputValue.value.trim().length > 0
    }

    if (activeTab.value === 'url') {
        // URL模式下验证URL格式
        try {
            new URL(inputValue.value)
            return true
        } catch {
            return false
        }
    }

    // 搜索模式下，只要有内容就可以
    return inputValue.value.trim().length > 0
})

// 添加网址导入方法
const importUrl = () => {
    const userStore = useUserStore()
    if (!userStore.isLogined) {
        userStore.openLoginModal()
        return
    }

    if (!inputValue.value) {
        message.error('请输入有效的网址')
        return
    }

    emit('action', { type: 'urlImported', value: inputValue.value })
    inputValue.value = ''
    modelValue.value = false
}

// 添加搜索导入方法
const importSearch = () => {
    const userStore = useUserStore()
    if (!userStore.isLogined) {
        userStore.openLoginModal()
        return
    }
    if (inputValue.value) {
        // 这里可以添加搜索逻辑
        console.log('搜索内容:', inputValue.value)
        // TODO: 实现搜索功能
        navigateTo(`/scholar/search?keyword=${inputValue.value}`)
    }
}

// 添加文本粘贴导入方法
const importPaste = async () => {
    const userStore = useUserStore()
    if (!userStore.isLogined) {
        userStore.openLoginModal()
        return
    }

    if (!inputValue.value.trim()) {
        message.error('请输入文本内容')
        return
    }

    try {
        // 取前8个字符作为文件名，如果不足8个字符则使用全部内容
        const fileName = inputValue.value.trim().substring(0, 8) + '.txt'

        // 创建文本文件的 Blob
        const textBlob = new Blob([inputValue.value], { type: 'text/plain;charset=utf-8' })

        // 直接上传新创建的文件
        const cosClientAndParams = await generatePutUrl({
            filename: fileName
        })

        if (!cosClientAndParams.ok) {
            message.error('文件上传失败，请重试')
            return
        }
        // 上传文件到云存储 - 尝试直接使用 Blob
        const response = await axios.put(cosClientAndParams.data.url, textBlob, {
            headers: {
                'Content-Type': cosClientAndParams.data.contentType
            }
        })

        console.log('上传响应:', response)
        if (response.status !== 200) {
            message.error('文件上传失败')
            return
        }

        const params = {
            fileName: fileName,
            fileUrl: removeQuestionMarkText(cosClientAndParams.data.url),
            fileSha256: ''
        }

        // 保存文件信息到系统
        const uploadByUrlResult = await uploadByUrl(params)

        if (!uploadByUrlResult.ok) {
            message.error('文件保存失败')
            return
        }

        const res = await addFiles({
            "spaceId": spaceId.value,
            "folderId": props.folderId || '',
            fileIds: [uploadByUrlResult.data.id]
        })

        if (res?.code == HTTP_STATUS.MOBILE_NOT_BOUND) {
            user.setShowPhoneBoundModal({
                status: BindPhoneModal.SHOW_BINDING,
            })
            return
        }
        // 触发成功事件
        emit('action', {
            type: 'pasteImported',
        })

        // 清空输入并关闭弹窗
        inputValue.value = ''
        modelValue.value = false
    } catch (error) {
        console.error('文本粘贴导入失败:', error)
        message.error('文本导入失败，请重试')
    }
}

// 添加文本粘贴导入方法
const importContent = () => {
    if (activeTab.value == 'url') {
        importUrl()
    } else if (activeTab.value == 'search') {
        importSearch()
    } else if (activeTab.value == 'paste') {
        importPaste()
    }
}

// 清空输入框内容
const clearField = () => {
    inputValue.value = ''
}



</script>