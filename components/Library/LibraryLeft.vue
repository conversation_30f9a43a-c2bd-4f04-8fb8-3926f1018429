<template>
    <div class="flex flex-col h-screen space-y-3 border-r border-gray-200/70 ">
        <!-- 头部 -->
        <div class="py-2 sm:py-4  pl-2 sm:pl-4 pr-2 lg:pr-4
            h-20 flex items-center justify-between bg-gradient-to-l from-[#E1E6FF] to-[#DBEAFB]">
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    <div
                        class="flex items-center justify-center flex-shrink-0 w-12 h-12 mr-2 text-blue-700 bg-white rounded-full">
                        <BookIconfont name="defaultlibrary" :size="24" />
                    </div>

                    <h2
                        class="text-lg font-medium text-[#2551B5] bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text">
                        知识库</h2>
                    <!-- <p class="text-sm text-gray-500">我的AI外脑</p> -->
                </div>

                <!-- 存储空间信息 -->
                <div
                    class="hidden lg:flex items-center text-xs sm:text-sm text-[#333333] font-medium whitespace-nowrap">
                    <!-- <save-one theme="outline" size="18" fill="#333333" /> -->
                    <span class="text-[#333333] px-1">
                        {{ formatStorageSize(spaceUsedBytes, 0) }}/{{
                            formatStorageSize(spaceQuotaBytes, 0) }}
                    </span>
                </div>
            </div>


            <!-- 新建文件夹 大屏显示 -->
            <div class="flex items-center space-x-1 lg:space-x-2 xl:space-x-4">
                <button v-if="canCreateFolder" @click="handleCreateFolderClick"
                    class="flex-1 flex items-center justify-between space-x-1 whitespace-nowrap text-xs w-auto xl:min-w-[120px] p-1.5 px-2 xl:px-4 sm:text-sm bg-[#F2F6FF] text-blue-700 border border-[#C3D3F5] rounded-lg font-medium">
                    <BookIconfont name="xinjianwenjianjia" :size="16" class="hidden xl:flex"></BookIconfont>
                    <span>新建文件夹</span>
                </button>
                <LibraryUploadEntry :folderId="page.folderId" @action="handleAction"></LibraryUploadEntry>
            </div>
        </div>


        <!-- 搜索框 -->
        <div class="flex items-center bg-white border sm:mx-4 md:flex-none border-blue-700 rounded-xl ">
            <!-- 搜索图标 -->
            <div class="px-3 text-blue-500">
                <search theme="outline" size="24" fill="#3b82f6" />
            </div>

            <!-- 输入框 -->
            <input v-model="searchQuery" type="text" placeholder="搜索文件名、文件夹名称"
                class="w-full h-9 bg-transparent text-[#333] placeholder-[#999] focus:outline-none text-base "
                @keyup.enter="handleSearch" />
            <!-- 搜索按钮 -->
            <button @click="handleSearch"
                class="bg-[#2551B5] h-full px-4 rounded-lg text-white text-sm font-medium transition-all duration-200 hover:bg-blue-600 whitespace-nowrap">
                <span>搜索</span>
            </button>
        </div>

        <!-- 面包屑 -->
        <div v-if="links.length > 1"
            class="h-[48px] flex items-center flex-shrink-0 px-2 sm:px-4 py-1 sm:py-2 overflow-x-auto">
            <div class="flex flex-wrap items-center gap-1 sm:gap-2">
                <button @click="handleBreadcrumbClick(links[0])"
                    class="text-xs text-gray-500 cursor-pointer sm:text-base hover:text-blue-600 whitespace-nowrap">
                    {{ links[0]?.label || '根目录' }}
                </button>
                <template v-for="(link, index) in links.slice(1)" :key="link.id">
                    <div class="text-gray-400">
                        <right theme="outline" size="18" class="sm:size-16" />
                    </div>
                    <button @click="index < links.length - 2 && handleBreadcrumbClick(link)" :class="[
                        'text-xs sm:text-base transition-colors whitespace-nowrap max-w-[100px] sm:max-w-[150px] md:max-w-none overflow-hidden text-ellipsis',
                        index === links.length - 2
                            ? 'text-blue-600 font-medium cursor-default'
                            : 'text-gray-500 hover:text-blue-600 cursor-pointer'
                    ]">
                        {{ link.label }}
                    </button>
                </template>
            </div>
        </div>

        <!-- 文件和文件夹 -->
        <div class="flex-1 overflow-y-auto ">
            <div class="flex items-center flex-1 h-full overflow-hidden">
                <!-- 加载状态 -->
                <div v-if="isFilesLoading || isFoldersLoading" class="flex items-center justify-center w-full py-10">
                    <loading theme="outline" size="40" class="text-gray-400 animate-spin" />
                </div>

                <!-- 内容区域：文件夹和文件列表 -->
                <div v-if="(folders.length > 0 || teamFiles.length > 0) && !isFilesLoading && !isFoldersLoading"
                    class="flex flex-col justify-between flex-1 w-full h-full">
                    <!-- 文件夹和文件列表容器 -->
                    <div ref="scrollContainer" class="flex-1 min-h-0 overflow-y-auto" @scroll="handleScroll">
                        <!-- 文件夹列表 -->
                        <div v-if="folders.length > 0" class="px-2 sm:px-4">
                            <FolderItem :folders="folders" @select="handleFolderClick" @rename="handleRename('folder')"
                                @move="handleMove" @delete="handleDeleteItem('folder')" />
                        </div>

                        <!-- 文件列表 -->
                        <div v-if="teamFiles.length > 0" class="px-2 sm:px-4">
                            <KnowledgeItem :items="teamFiles" @delete="handleDeleteItem('file')"
                                @rename="handleRename('file')" @move="handleMove" :links="links" />
                        </div>

                        <!-- 加载更多指示器 -->
                        <div v-if="isLoadingMore" class="flex items-center justify-center py-4">
                            <loading theme="outline" size="24" class="mr-2 text-gray-400 animate-spin" />
                            <span class="text-sm text-gray-500">加载中...</span>
                        </div>

                        <!-- 没有更多数据提示 -->
                        <div v-if="teamFiles.length > 0 && !hasMoreData && !isLoadingMore"
                            class="flex items-center justify-center py-4">
                            <span class="text-sm text-gray-400">没有更多数据了</span>
                        </div>
                    </div>
                </div>

                <!-- 空状态 -->
                <div v-if="!isFilesLoading && !isFoldersLoading && folders.length === 0 && teamFiles.length === 0"
                    class="flex flex-col items-center justify-center w-full py-12 sm:py-20">
                    <div class="flex flex-col items-center">
                        <div
                            class="flex items-center justify-center w-16 h-16 mb-3 rounded-full sm:w-20 sm:h-20 sm:mb-4 bg-gray-50">
                            <folder-open theme="outline" size="36" class="text-gray-400" />
                        </div>
                        <h3 class="mb-1 text-base font-medium text-gray-600 sm:mb-2 sm:text-lg">知识库中暂无文档
                        </h3>
                        <p class="text-xs text-gray-500 sm:text-sm">上传一个文档，开始使用您的知识库吧</p>
                    </div>
                </div>
            </div>

            <!-- 新建文件夹弹窗 -->
            <CreateFolderModal v-model="showCreateFolderModal" @confirm="handleCreateFolder" />
            <!-- 移动文件弹窗 -->
            <MoveFileModal v-if="showMoveModal" :type="moveType" v-model="showMoveModal" @confirm="handleMoveConfirm"
                :currentItem="currentFolder" />
        </div>
    </div>
</template>


<script setup lang="ts">
import { addFolder, addHtml, getFilesByIds, getFolder, getSubFolders, listFile } from '@/api/repositoryFile';
import BookIconfont from '@/components/Book/BookIconfont.vue';
import FolderItem from '@/components/Library/FolderItem.vue';
import KnowledgeItem from '@/components/Library/KnowledgeItem.vue';
import type { NewRepositoryFile, RepositoryFolder } from '@/services/types/repositoryFile';
import { useUserStore } from '@/stores/user';
import { FolderOpen, Loading, Right, Search } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useTracking } from '~/composables/useTracking';
import { UserService } from '~/services/user';
import { useSpaceStore } from '~/stores/space';

interface Folder {
    id: number
    name: string
    fileCount: number
    updateTime: string
}
interface Options {
    isShowLoading?: boolean
    isLoadMore?: boolean
}

const props = defineProps({
    currentFolderId: {
        type: String,
        default: '0',
    },
    exampleList: {
        type: Array as PropType<string[]>,
        default: [],
    },
    isEmptyDocument: {
        type: Boolean,
        default: false,
    },
})
const emit = defineEmits(['update:currentFolderId', 'update:exampleList', 'update:isEmptyDocument'])

const { track } = useTracking();
const user = useUserStore()
const spaceStore = useSpaceStore()

// 获取空间信息
const { spaceQuotaBytes, spaceUsedBytes } = storeToRefs(spaceStore)


const showMoveModal = ref(false)
const currentFolder = ref<RepositoryFolder | NewRepositoryFile | null>(null)
const moveType = ref<'folder' | 'file'>('folder')
// 添加一个 ref 来跟踪是否有处理中的文件
let hasProcessingFiles = false
let timerId: NodeJS.Timeout | undefined = undefined
const isFilesLoading = ref(true)
const isFoldersLoading = ref(true)
const showCreateFolderModal = ref(false)
const teamFiles = ref<NewRepositoryFile[]>([])
const folders = ref<RepositoryFolder[]>([])

// 无限滚动相关状态
const isLoadingMore = ref(false)
const hasMoreData = ref(true)
const scrollContainer = ref<HTMLElement | null>(null)


// 面包屑
const links = ref<Array<{
    label: string;
    to: string;
    id: string | number;
}>>([])
const route = useRoute()

const page = reactive({
    current: 1,
    pageSize: 15,
    total: 0,
    pages: 1,
    folderId: route.query.folderId || '0'
})

const spaceId = computed(() => {
    return user.currentLoginInfo?.id || ''
})


const searchQuery = ref('');
const handleSearch = () => {
    const userStore = useUserStore()
    if (!userStore.isLogined) {
        userStore.openLoginModal()
        return
    }
    const trimmedQuery = searchQuery.value.trim();
    if (!trimmedQuery) {
        message.warning('请输入搜索关键词');
        return;
    }
    navigateTo(`/library/search?keyword=${encodeURIComponent(trimmedQuery)}`);
};

// 滚动处理函数
const handleScroll = () => {
    if (!scrollContainer.value || isLoadingMore.value || !hasMoreData.value) return

    const { scrollTop, scrollHeight, clientHeight } = scrollContainer.value
    const isBottom = scrollTop + clientHeight >= scrollHeight - 50 // 距离底部 50px 内触发

    if (isBottom && page.current < page.pages) {
        loadMoreFiles()
    }
}

// 加载更多文件
const loadMoreFiles = async () => {
    if (isLoadingMore.value || !hasMoreData.value) return

    isLoadingMore.value = true
    page.current++

    try {
        await getFileType({ isShowLoading: false, isLoadMore: true })
    } catch (error) {
        console.error('加载更多文件失败:', error)
        page.current-- // 回退页码
    } finally {
        isLoadingMore.value = false
    }
}

// 添加计算属性判断是否可以创建文件夹
const canCreateFolder = computed(() => {
    // 条件1：文件夹层级不超过三级（不包括根目录）
    const levelCondition = links.value.length <= 3;
    // 条件2：当前目录下的文件夹数量不超过10个
    let folderCountCondition = false
    folderCountCondition = folders.value.length < 30;
    return levelCondition && folderCountCondition;
})


// 面包屑点击处理
const handleBreadcrumbClick = async (link: { id: string | number; to: string; label: string }) => {
    page.current = 1
    // 重置无限滚动状态
    hasMoreData.value = true
    isLoadingMore.value = false

    // 设置当前文件夹ID
    page.folderId = link.id.toString()

    // 更新面包屑导航（删除当前点击项之后的所有项）
    const index = links.value.findIndex(item => item.id === link.id)
    if (index !== -1) {
        links.value = links.value.slice(0, index + 1)
    }

    // 重新加载文件夹内容
    await getFileType({ isShowLoading: true })
    await getFolders({ isShowLoading: true })
    emit('update:isEmptyDocument', folders.value.length === 0 && teamFiles.value.length === 0 ? true : false)
}

// 新建文件夹
const handleCreateFolderClick = () => {
    const userStore = useUserStore()
    if (!userStore.isLogined) {
        userStore.openLoginModal()
        return
    }
    showCreateFolderModal.value = true;
}

// 创建文件夹
const handleCreateFolder = async (data: { name: string }) => {

    const params = {
        folderName: data.name,
        parentId: page.folderId
    }
    const res = await addFolder(spaceId.value, params)
    if (!res.ok) {
        // console.error('创建文件夹失败')
        return
    }
    // 重置无限滚动状态
    hasMoreData.value = true
    isLoadingMore.value = false

    await getFileType({ isShowLoading: true })
    await getFolders({ isShowLoading: true })
    emit('update:isEmptyDocument', folders.value.length === 0 && teamFiles.value.length === 0 ? true : false)
}


// 上传文件
const handleAction = async (payload: any) => {
    if (payload.type === 'uploadComplete') {
        page.current = 1
        // 重置无限滚动状态
        hasMoreData.value = true
        isLoadingMore.value = false

        await getFileType({ isShowLoading: true })
        // await getFolders({ isShowLoading: true })
        // spaceStore.loadSpaceInfo()

        track('library_upload',
            payload.postId,
            '文件上传'
        );
    } else if (payload.type === 'urlImported') {
        const res = await addHtml({
            "spaceId": spaceId.value,
            "folderId": page.folderId,
            url: payload.value,
        })
        if (!res.ok) {
            // console.error('网页抓取失败')
            return
        }
        message.success(res.message || '导入成功')
        page.current = 1
        // 重置无限滚动状态
        hasMoreData.value = true
        isLoadingMore.value = false

        await getFileType({ isShowLoading: true })
        // await getFolders({ isShowLoading: true })
        // spaceStore.loadSpaceInfo()

        track('library_import',
            payload.value,
            '网页导入'
        );
    }
}

// 获取文件进度
const getProgress = (record: NewRepositoryFile) => {
    if (record?.processData?.error) {
        return -1
    }
    if (record.status == RepositoryFileStatus.DONE) {
        return 100
    }
    return record.processData?.summary_status || 0
}


// 获取文件夹
const getFolders = async (options: Options | null = null) => {
    const defaultOptions: Options = { isShowLoading: true }
    const _options = options ?? defaultOptions

    if (_options.isShowLoading) {
        isFoldersLoading.value = true
    }
    if (!spaceId.value) {
        return
    }
    const params = {
        folderId: page.folderId,
    }
    const res = await getSubFolders(spaceId.value, params)
    if (!res.ok) {
        // console.error('获取文件夹列表失败')
        if (_options.isShowLoading) {
            isFoldersLoading.value = false
        }
        return
    }
    folders.value = res.data?.map((d: Folder) => ({
        ...d,
        folderId: page.folderId
    }))

    if (_options.isShowLoading) {
        isFoldersLoading.value = false
    }
}

// 获取文件
const getFileType = async (options: Options | null = null) => {
    const defaultOptions: Options = { isShowLoading: true, isLoadMore: false }
    const _options = options ?? defaultOptions

    if (_options.isShowLoading) {
        isFilesLoading.value = true
    }
    if (!spaceId.value) {
        console.error('spaceId.value==>', spaceId.value)
        return
    }

    const params = {
        folderId: page.folderId,
        pageNo: page.current,
        pageSize: page.pageSize
    }

    const res = await listFile(spaceId.value, params)

    if (!res.ok) {
        // console.error('获取文档列表失败')
        isFilesLoading.value = false
        return
    }

    // 如果当前页没有数据且不是第一页,自动跳转到上一页
    if (res.data?.records?.length === 0 && page.current > 1 && !_options.isLoadMore) {
        page.current--
        isFilesLoading.value = false
        return await getFileType(options)
    }

    const newFiles = res.data?.records?.map((d: NewRepositoryFile) => ({
        ...d,
        progress: getProgress(d),
        folderId: page.folderId
    })) || []

    // 如果是加载更多，追加到现有列表；否则替换列表
    if (_options.isLoadMore) {
        teamFiles.value = [...teamFiles.value, ...newFiles]
    } else {
        teamFiles.value = newFiles
    }
    emit('update:exampleList', teamFiles.value.length > 0 ? teamFiles.value[0].processData?.questions || [] : [])

    Object.assign(page, {
        current: res.data?.current,
        total: res.data?.total,
        pageSize: res.data?.size,
        pages: res.data?.pages,
    })

    // 更新是否还有更多数据的状态
    hasMoreData.value = page.current < page.pages

    if (_options.isShowLoading) {
        isFilesLoading.value = false
    }

    // 确保 processingList 是一个数组
    const processingList = teamFiles.value.filter((d) => d.status !== RepositoryFileStatus.ERROR && d.status !== RepositoryFileStatus.DONE && d.progress > -1 && d.progress < 100) || []
    if (timerId) {
        clearTimeout(timerId)
    }
    // 检查是否有处理中的文件
    if (processingList.length > 0) {
        hasProcessingFiles = true
        timerId = setTimeout(() => {
            updateProcessingFilesStatus()
        }, 5000)
    } else if (hasProcessingFiles) {
        // 如果之前有处理中的文件，现在没有了，说明文件都处理完成了
        hasProcessingFiles = false
        spaceStore.loadSpaceInfo()
    }
}

// 轮询更新处理中文件的状态
const updateProcessingFilesStatus = async () => {
    // console.log('=== 开始轮询文件状态 ===')

    // 1. 从所有已加载文件中筛选处理中的文件
    const processingFiles = teamFiles.value.filter((file, index) => {
        const isProcessing = file.status !== RepositoryFileStatus.ERROR &&
            file.status !== RepositoryFileStatus.DONE &&
            file.progress > -1 &&
            file.progress < 100

        if (isProcessing) {
            // 计算文件在第几页
            const pageNum = Math.floor(index / page.pageSize) + 1
            console.log(`发现处理中文件: ${file.name} (ID: ${file.id}, 第${pageNum}页, 索引${index})`)
        }

        return isProcessing
    })

    if (processingFiles.length === 0) {
        // console.log('没有处理中的文件，停止轮询')
        if (hasProcessingFiles) {
            hasProcessingFiles = false
            spaceStore.loadSpaceInfo()
        }
        return
    }

    // 2. 准备API请求参数
    const fileIds = processingFiles.map(f => f.id)
    // console.log('准备查询文件状态:', fileIds)

    try {
        // 3. 调用批量查询API
        const res = await getFilesByIds({
            spaceId: spaceId.value,
            fileIds: fileIds
        })

        // console.log('API响应:', res)

        if (res.success && res.data) {
            let updatedCount = 0

            // 4. 更新文件状态
            teamFiles.value.forEach((file, index) => {
                const updatedFile = res.data.find((uf: any) => uf.id === file.id)
                if (updatedFile) {
                    const oldStatus = file.status
                    const oldProgress = file.progress
                    const newStatus = updatedFile.status
                    const newProgress = getProgress(updatedFile)

                    // 更新文件信息
                    teamFiles.value[index] = {
                        ...file,
                        status: newStatus,
                        processData: updatedFile.processData,
                        wordCount: updatedFile.wordCount,
                        progress: newProgress
                    }

                    if (oldStatus !== newStatus || oldProgress !== newProgress) {
                        const pageNum = Math.floor(index / page.pageSize) + 1
                        console.log(`文件状态更新: ${file.name} (第${pageNum}页) ${oldStatus}(${oldProgress}%) -> ${newStatus}(${newProgress}%)`)
                        updatedCount++
                    }
                }
            })

            // console.log(`本次轮询更新了 ${updatedCount} 个文件的状态`)
        }

        // 5. 检查是否需要继续轮询
        const stillProcessing = teamFiles.value.some(file =>
            file.status !== RepositoryFileStatus.ERROR &&
            file.status !== RepositoryFileStatus.DONE &&
            file.progress > -1 &&
            file.progress < 100
        )

        if (stillProcessing) {
            console.log('还有文件在处理中，5秒后继续轮询')
            hasProcessingFiles = true
            timerId = setTimeout(() => {
                updateProcessingFilesStatus()
            }, 5000)
        } else {
            console.log('所有文件处理完成，停止轮询')
            hasProcessingFiles = false
            spaceStore.loadSpaceInfo()
        }

    } catch (error) {
        console.error('轮询API调用失败:', error)
        // 网络错误时继续轮询
        timerId = setTimeout(() => {
            updateProcessingFilesStatus()
        }, 5000)
    }
}

// 点击文件夹
const handleFolderClick = async (folder: RepositoryFolder) => {
    // console.log(folder)
    page.current = 1
    // 重置无限滚动状态
    hasMoreData.value = true
    isLoadingMore.value = false

    page.folderId = folder.id.toString()

    // 添加新的面包屑项
    links.value.push({
        label: folder.folderName,
        to: folder.folderPath,
        id: folder.id
    })

    await getFileType({ isShowLoading: true })
    await getFolders({ isShowLoading: true })
    emit('update:isEmptyDocument', folders.value.length === 0 && teamFiles.value.length === 0 ? true : false)

}

// 重命名
const handleRename = async (item: string) => {
    if (item === 'folder') {
        await getFolders({ isShowLoading: true })
    } else if (item === 'file') {
        // 重置无限滚动状态
        hasMoreData.value = true
        isLoadingMore.value = false
        await getFileType({ isShowLoading: true })
    }
}

// 移动文件
const handleMove = async (item: RepositoryFolder | NewRepositoryFile, type: 'folder' | 'file') => {
    moveType.value = type
    currentFolder.value = item
    showMoveModal.value = true
}


// 移动文件
const handleMoveConfirm = async () => {
    await getFileType({ isShowLoading: true })
    await getFolders({ isShowLoading: true })
    showMoveModal.value = false

    emit('update:isEmptyDocument', folders.value.length === 0 && teamFiles.value.length === 0 ? true : false)
}

// 删除文件
const handleDeleteItem = async (item: string) => {
    if (item === 'folder') {
        await getFolders({ isShowLoading: true })
    } else if (item === 'file') {
        // 重置无限滚动状态
        hasMoreData.value = true
        isLoadingMore.value = false
        await getFileType({ isShowLoading: true })
    }
    // 如果当前页没有数据了,且不是第一页,则跳转到上一页
    if (teamFiles.value.length === 0 && page.current > 1) {
        page.current--
        // 重置无限滚动状态
        hasMoreData.value = true
        isLoadingMore.value = false
        if (item === 'folder') {
            await getFolders({ isShowLoading: true })
        } else if (item === 'file') {
            await getFileType({ isShowLoading: true })
        }
    }
}

// 用户登录成功  加载数据
watch(() => user.isLogined, (isLogined) => {
    if (isLogined) {
        // console.log('用户登录成功，重新加载数据')
        setupData()
    }
})

// 初始化面包屑
const initBreadcrumb = async () => {
    const res = await getFolder(spaceId.value, { folderId: page.folderId.toString() })
    if (!res.ok) {
        // console.error('获取文件夹列表失败')
        return
    }
    // 如果是根目录，只显示个人知识库
    if (page.folderId === '0' || !res.data) {
        links.value = [{
            label: '个人知识库',
            to: '/library',
            id: '0'
        }]
        return
    }

    // 重置面包屑
    links.value = []

    // 添加根目录
    links.value.push({
        label: '个人知识库',
        to: '/library',
        id: '0'
    })

    // 解析 parentIds 和 folderPath 来构建面包屑
    const folderData = res.data
    const parentIds = folderData.parentIds ? folderData.parentIds.split(',') : []
    const folderPath = folderData.folderPath || ''

    // 分割路径，去除空字符串
    const pathSegments = folderPath.split('/').filter(segment => segment.trim() !== '')
    // 构建面包屑链
    if (parentIds.length > 0 && pathSegments.length > 0) {
        // parentIds 包含所有父级ID，包括当前文件夹的ID
        // pathSegments 包含路径的各个部分

        // 为每个路径段创建面包屑项
        for (let i = 0; i < pathSegments.length; i++) {
            const pathToHere = '/' + pathSegments.slice(0, i + 1).join('/')
            // parentIds[i] 对应 pathSegments[i] 的ID
            const folderId = i < parentIds.length ? parentIds[i] : folderData.id

            links.value.push({
                label: pathSegments[i],
                to: pathToHere,
                id: folderId
            })
            console.log(`添加面包屑项 ${i}:`, {
                label: pathSegments[i],
                to: pathToHere,
                id: folderId
            })
        }
    }
}




const setupData = async () => {


    spaceStore.loadSpaceInfo()
    await getFolders({ isShowLoading: true })
    await getFileType({ isShowLoading: true })
    emit('update:isEmptyDocument', folders.value.length === 0 && teamFiles.value.length === 0 ? true : false)
    initBreadcrumb()
}

watch(() => page.folderId, (newValue) => {
    // 这里是提问用的，如果是主目录提问，不期望填 '0'
    console.log(page.folderId, 'page.folderId')
    emit('update:currentFolderId', newValue.length > 1 ? newValue : '')
})


onMounted(() => {
    if (!UserService.isLogined()) {
        isFilesLoading.value = false
        isFoldersLoading.value = false
        return
    }

    // 检查sessionStorage中是否有返回数据
    try {
        const storedData = sessionStorage.getItem('library_back_data')
        if (storedData) {
            // console.log('从sessionStorage获取到返回数据', storedData)
            const options = JSON.parse(storedData)
            // 清除数据，避免重复处理
            sessionStorage.removeItem('library_back_data')

            // 如果存储的数据中包含面包屑信息，则直接使用
            if (options.links && Array.isArray(options.links) && options.links.length > 0) {
                // 设置面包屑
                links.value = options.links

                // 从面包屑中获取当前文件夹ID和知识库类型
                const lastBreadcrumb = options.links[options.links.length - 1]
                if (lastBreadcrumb && lastBreadcrumb.id) {
                    page.folderId = lastBreadcrumb.id.toString()
                }
                // 加载文件和文件夹数据
                getFolders({ isShowLoading: true })
                getFileType({ isShowLoading: true })
                return
            }
        }
    } catch (error) {
        console.error('处理sessionStorage数据出错', error)
    }

    setupData()
})

</script>