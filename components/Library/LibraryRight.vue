<template>
    <!-- 知识库文件夹提问 -->
    <ClientOnly>
        <KnowledgeQuestion v-if="!isLoading" :key="sessionId" :repository-folder-id-list="repositoryFolderIdList"
            :knowledgeDetail="null" :sessionId="sessionId" :example-list="exampleList" :session-list="sessionList"
            :is-empty-document="isEmptyDocument" @change-session-id="handleChangeSessionId" />
    </ClientOnly>
</template>
<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { getMessageSessionList } from '~/api/appMessage';
import type { AppMessageRecord } from '~/services/types/appMessage';
import KnowledgeQuestion from './KnowledgeQuestion.vue';

const props = defineProps({
    currentFolderId: {
        type: String,
        default: '',
    },
    exampleList: {
        type: Array as PropType<string[]>,
        default: [],
    },
    isEmptyDocument: {
        type: Boolean,
        default: false,
    },
})
const repositoryFolderIdList = computed(() => {
    return props?.currentFolderId ? [props?.currentFolderId] : [];
});
const sessionId = ref('')
const isLoading = ref(true);
const sessionList = ref<AppMessageRecord[]>([])
const handleChangeSessionId = (_sessionId: string) => {
    isLoading.value = true;
    setTimeout(() => {
        sessionId.value = _sessionId || ''
        isLoading.value = false;
    }, 300);
}
const getMessageSessionListData = async () => {
    // 检查用户是否已登录
    const userStore = useUserStore()
    if (!userStore.isLogined) {
        // userStore.openLoginModal()
        isLoading.value = false;
        return
    }

    isLoading.value = true;
    let params: any = {
    }
    if (props.currentFolderId && props.currentFolderId.length > 1) {
        params.repositoryFolderId = props.currentFolderId
        params.sessionFrom = SessionFrom.KnowledgeFolder
    } else {
        params.sessionFrom = SessionFrom.KnowledgeFolderRoot
    }
    const res = await getMessageSessionList(params);
    if (!res.success) {
        isLoading.value = false;
        return;
    }
    const list = res.data?.records || []
    sessionList.value = list
    if (list.length > 0) {
        sessionId.value = `${list[0].sessionId}`;
    } else {
        sessionId.value = ''
    }
    isLoading.value = false;
};
watch(() => props.currentFolderId, (newVal, oldValue) => {
    if (newVal != oldValue) {
        getMessageSessionListData()
    }
})
onMounted(() => {
    getMessageSessionListData()
})


</script>