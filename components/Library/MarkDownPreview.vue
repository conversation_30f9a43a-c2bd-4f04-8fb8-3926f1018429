<template>
    <div class="knowledge-markdown-preview">
        <div class="knowledge-markdown-preview-title">{{ title }}</div>
        <!-- <div class="knowledge-markdown-preview-original-link" v-if="suffix != KnowledgeFileSuffix.TXT">
            原文链接：
            <a :href="originalUrl" target="_blank">{{ originalUrl }}</a>
        </div> -->
        <div class="knowledge-markdown-preview-content">

            <div v-if="loading" class="knowledge-markdown-preview-loading">
                <a-spin />
            </div>
            <div class="knowledge-markdown-preview-content1" v-html="content">
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import axios from 'axios';
import markdownit from 'markdown-it';
import { onMounted, ref } from 'vue';

interface Props {
    url: string
    suffix: string
    originalUrl: string
    title: string
}
const props = withDefaults(defineProps<Props>(), {
    url: '',
    suffix: '',
    originalUrl: '',
    title: ''
})

const loading = ref(true)


const content = ref('')
const addNoReferrerMeta = () => {
    // 创建一个新的meta元素
    const meta = document.createElement('meta')

    // 设置meta元素的name和content属性
    meta.name = 'referrer'
    meta.content = 'never'

    // 获取head元素
    const head = document.getElementsByTagName('head')[0]

    // 将新的meta元素添加到head中
    head.appendChild(meta)
}
const insertMetaTags = () => {
    // <meta name="referrer" content="never">
    // 使用 querySelectorAll 和 Array.prototype.some 方法
    const metaTags = document.querySelectorAll('meta[name="referrer"]')
    const hasNeverReferrer = Array.from(metaTags).some((tag: any) => tag.content === 'never')

    if (!hasNeverReferrer) {
        addNoReferrerMeta()
    }
}
onMounted(() => {
    insertMetaTags()
    axios
        .get(props.url)
        .then((response) => {
            let _inspiration_data = response.data
            // console.log(_inspiration_data, '_inspiration_data')
            const regex = /```markdown\n([\s\S]*?)\n```/
            const match = _inspiration_data.match(regex)
            if (match && match[1]) {
                _inspiration_data = match[1]
            }
            const md = markdownit()
            // 自定义插件函数
            function markdownItPluginAddTarget(md) {
                // 使用原生规格器（renderer）
                const defaultRender =
                    md.renderer.rules.link_open ||
                    function (tokens, idx, options, env, self) {
                        return self.renderToken(tokens, idx, options)
                    }

                // 覆盖规格器中的link_open方法
                md.renderer.rules.link_open = function (tokens, idx, options, env, self) {
                    // 调用默认的处理方法
                    const aIndex = tokens[idx].attrIndex('target')

                    if (aIndex < 0) {
                        tokens[idx].attrPush(['target', '_blank']) // 添加target属性
                    }

                    // 调用默认的处理方法
                    return defaultRender(tokens, idx, options, env, self)
                }
            }

            // 使用自定义插件
            md.use(markdownItPluginAddTarget)
            content.value = md.render(_inspiration_data)
            loading.value = false
        })
        .catch(() => {
            loading.value = false
        })
})
</script>
<style>
.knowledge-markdown-preview {
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100%;
    border: none;

    &-original-link {
        color: rgb(153, 153, 153);
        font-size: 14px;
        padding: 10px 30px;
    }

    &-title {
        padding: 15px 10px 0 10px;
        text-align: center;
        font-weight: 800;
        font-size: 16px;
        line-height: 25px;
    }

    &-content {
        flex: 1;
        overflow: hidden;

        font-weight: 400;
        font-size: 16px;
        color: #555555;
        line-height: 31px;
    }

    &-content1 {
        padding: 0 30px;
        width: 100%;
        height: 100%;
        overflow: auto;
    }

    &-iframe {
        width: 100%;
        height: 100%;
        overflow: auto;
        border: none;
    }

    &-loading {
        margin-top: 220px;
        position: absolute;
        left: 50%;
        top: 50px;
        transform: translateX(-50%);
    }
}
</style>