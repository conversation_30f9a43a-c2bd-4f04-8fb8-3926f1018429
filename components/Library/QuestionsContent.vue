<template>
  <!-- 知识库单个文件提问 -->
  <div class="flex flex-col flex-1 h-full overflow-hidden">
    <Entrance v-if="!isLoading" :editorData="props.knowledgeDetail?.processData.summary_data" :is-visible-top="false"
      :key="sessionId" :repositoryFileId="repositoryFileId" :is-knowledge="true" :exampleList="exampleList"
      :session-from="SessionFrom.KnowledgeSingleFile" :session-id="sessionId" :is-new-session-id="isNewSessionId"
      :session-list="sessionList" @change-session-id="handleChangeSessionId" :question-mode="questionModeValue" />
  </div>
</template>

<script setup lang="ts">
import { type RepositoryFile } from '@/services/types/repositoryFile';
import { getMessageSessionList } from '~/api/appMessage';
import type { AppMessageRecord } from '~/services/types/appMessage';
const Entrance = defineAsyncComponent(() => import('~/components/Chat/Entrance.vue'));
interface Props {
  knowledgeDetail: RepositoryFile | null
}
const props = withDefaults(defineProps<Props>(), {
  knowledgeDetail: null
})

const questionModeValue = ref([QuestionTypeEnum.useR1])

const exampleList = computed(() => {
  if (
    !props.knowledgeDetail ||
    !props.knowledgeDetail.processData ||
    !props.knowledgeDetail.processData.questions
  ) {
    return []
  }
  return props.knowledgeDetail.processData.questions || []
})

console.log(props)


const repositoryFileId = computed(() => {
  if (!props.knowledgeDetail) {
    return ''
  }
  return props.knowledgeDetail.id
})
const isLoading = ref(true)
const sessionList = ref<AppMessageRecord[]>([])
const getMessageSessionListData = async () => {
  isLoading.value = true;
  const res = await getMessageSessionList({
    repositoryFileId: repositoryFileId.value
  });
  if (!res.success) {
    isLoading.value = false;
    return;
  }
  const list = res.data?.records || []
  sessionList.value = list
  if (list.length > 0) {
    sessionId.value = `${list[0].sessionId}`;
  } else {
    sessionId.value = ''
  }
  isLoading.value = false;
};
const sessionId = ref('')
const isNewSessionId = computed(() => {
  return sessionId.value === ''
})
// watch(() => props.knowledgeDetail?.id, (newVal, oldValue) => {
//   sessionId.value = ''
//   getMessageSessionListData()
// })

// 这里要获取他的 sessionId
onMounted(() => {
  getMessageSessionListData()
})
const handleChangeSessionId = (_sessionId: string) => {
  isLoading.value = true;
  setTimeout(() => {
    sessionId.value = _sessionId || ''
    isLoading.value = false;
  }, 300);
}

</script>