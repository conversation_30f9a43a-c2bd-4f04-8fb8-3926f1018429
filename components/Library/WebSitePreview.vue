<template>
    <div class="knowledge-website-preview">
        <div class="knowledge-website-preview-title">{{ title }}</div>
        <div class="knowledge-website-preview-content">
            <!-- <div class="knowledge-website-preview-original-link">
                原文链接：
                <a :href="originalUrl" target="_blank">{{ originalUrl }}</a>
            </div> -->
            <div class="knowledge-website-preview-content1">
                <div v-if="loading" class="knowledge-website-preview-loading"><a-spin /></div>
                <iframe :src="url" class="knowledge-website-preview-iframe" @load="onIframeLoad"></iframe>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue';

interface Props {
    url: string
    suffix: string
    originalUrl: string
    title: string
}
const props = withDefaults(defineProps<Props>(), {
    url: '',
    suffix: '',
    originalUrl: '',
    title: ''
})
// 定义加载状态
const loading = ref(true)
const onIframeLoad = () => {
    loading.value = false // 加载完成后，改变 loading 状态为 false
}
</script>
<style lang="scss" scoped>
.knowledge-website-preview {
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: calc(100vh - 95px);
    border: none;

    &-original-link {
        color: rgb(153, 153, 153);
        font-size: 14px;
    }

    &-title {
        padding: 15px 10px;
        text-align: center;
        font-weight: 800;
        font-size: 16px;
        line-height: 25px;
    }

    &-content {
        flex: 1;
        overflow: hidden;
        padding: 10px 30px;
        font-weight: 400;
        font-size: 16px;
        color: #555555;
        line-height: 31px;
    }

    &-content1 {
        width: 100%;
        height: 100%;
    }

    &-iframe {
        width: 100%;
        height: 100%;
        overflow: auto;
        border: none;
    }

    &-loading {
        position: absolute;
        left: 50%;
        top: 50px;
        transform: translateX(-50%);
    }
}
</style>