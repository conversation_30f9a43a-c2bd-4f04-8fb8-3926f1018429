<template>
  <div class="flex flex-col flex-1 p-3 overflow-y-auto sm:p-6">
    <div class="flex-1 w-full space-y-3">
      <div v-if="isLoading" class="flex items-center justify-center h-24">
        <a-spin inning="isLoading"></a-spin>
      </div>
      <EmptyState v-if="!isLoading && orderList.length === 0" />
      <div v-if="!isLoading" v-for="item in orderList" :key="item.id"
        class="transition-all duration-200 bg-white border border-blue-100 rounded-lg group hover:border-blue-200 hover:shadow-sm">
        <!--router-link  :to="getLink(item)" -->
        <div class="block w-full cursor-pointer " @click.stop.prevent="handleOrderItem(item)">
          <div class="p-2 sm:p-4">
            <div class="flex flex-col items-start justify-between gap-2 sm:gap-3 sm:flex-row sm:items-center">
              <!-- 左侧内容区 -->
              <div class="flex-1 min-w-0 w-full sm:max-w-[70%]">
                <div class="flex flex-col sm:flex-row sm:items-center gap-1.5 sm:gap-2 mb-1.5">
                  <h3 class="text-sm text-[#333333] break-words line-clamp-2 sm:line-clamp-none">{{ getTitle(item) }}
                  </h3>
                  <span
                    class="inline-flex items-center shrink-0 px-2 py-0.5 text-xs text-blue-700 bg-blue-50 rounded-full">
                    {{ item.creatorName }}
                  </span>
                </div>
                <p class="text-xs text-gray-500">{{ item.createTime }}</p>
              </div>

              <!-- 右侧操作区 -->
              <div
                class="flex flex-col items-start w-full gap-2 sm:w-auto sm:flex-row sm:items-center sm:gap-4 shrink-0">
                <div class="w-full text-sm whitespace-nowrap sm:w-auto" v-if="item.creatorCode == 'book'">
                  <button @click.stop.prevent="handleOrderItem(item)"
                    class="inline-flex items-center justify-center w-full px-3 py-1 text-sm text-white transition-colors bg-blue-600 rounded sm:w-auto shrink-0 hover:bg-blue-700 focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-blue-500">
                    查看编辑
                  </button>
                </div>

                <div class="flex flex-col items-start w-full gap-2 sm:gap-3" v-if="item.creatorCode != 'book'">
                  <div class="flex flex-wrap items-center justify-between w-full text-sm">
                    <span class="mr-2 text-sm text-gray-600 whitespace-nowrap"
                      v-if="item.status != SubmissionStatus.needPay"
                      :class="`status ${item.status == SubmissionStatus.needPay ? 'active' : ''}`">
                      {{ orderStatusText(item.status, item.creatorCode) }}
                    </span>
                    <div class="flex flex-wrap items-center gap-1 sm:gap-2" v-if="
                      item?.tokens && item.status != SubmissionStatus.needPay && item.status != SubmissionStatus.error
                    ">
                      <span class="text-gray-500">消耗硬币：</span>
                      <span class="text-gray-900">{{ item?.tokens || '' }}</span>
                      <span class="text-gray-900" v-if="item.helpStatus == 2 || item.helpStatus == 3">
                        {{ getFreeHelpText(item.helpStatus) }}
                      </span>
                    </div>
                    <div v-else-if="item.creatorCode == 'online_editing'">
                      <span class="text-gray-500">消耗硬币：</span>
                      <span class="ml-1 text-gray-900">{{ item?.tokens }}</span>
                    </div>
                    <div v-else-if="item.status == SubmissionStatus.error">
                      <span class="text-gray-500">已退还硬币</span>
                    </div>
                    <div v-else class="flex items-center justify-end w-full text-sm whitespace-nowrap">
                      <span class="ml-1 text-gray-900" v-if="item.helpStatus == 2 || item.helpStatus == 3">
                        {{ getFreeHelpText(item.helpStatus) }}
                      </span>
                    </div>
                  </div>

                  <div class="flex flex-wrap items-center w-full gap-2">
                    <div class="text-sm whitespace-nowrap" v-if="isCanDownload(item)">
                      <button @click.stop.prevent="handleDownloadFile(item)"
                        class="inline-flex items-center px-3 py-1 text-sm text-white transition-colors bg-blue-600 rounded shrink-0 hover:bg-blue-700 focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-blue-500">
                        下载
                      </button>
                    </div>
                    <div class="text-sm whitespace-nowrap" v-if="isCanDownload(item)">
                      <button @click.stop.prevent="handleOrderItem(item)"
                        class="inline-flex items-center px-3 py-1 text-sm text-white transition-colors bg-blue-600 rounded shrink-0 hover:bg-blue-700 focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-blue-500">
                        查看编辑
                      </button>
                    </div>
                    <div class="text-sm whitespace-nowrap" v-if="item.status == SubmissionStatus.needPay">
                      <button @click.stop.prevent="handleOrderItem(item)"
                        class="inline-flex items-center px-3 py-1 text-sm text-white transition-colors bg-blue-600 rounded shrink-0 hover:bg-blue-700 focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-blue-500">
                        待确认
                      </button>
                    </div>
                    <div class="text-sm whitespace-nowrap"
                      v-if="item.creatorCode != 'book' && item.status == SubmissionStatus.done || item.status == SubmissionStatus.error">
                      <button @click.stop.prevent="handlePressEditCreate(item)"
                        class="inline-flex items-center px-3 py-1 text-gray-600 bg-white border border-gray-300 rounded-md shrink-0 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <copy theme="outline" size="16" fill="currentColor"
                          class="inline-block mr-1 align-text-bottom" />
                        复制创作任务
                      </button>
                    </div>

                    <div class="ml-auto"
                      v-if="!isHuawen && item.status != SubmissionStatus.ing && item.status != SubmissionStatus.payed && item.status != SubmissionStatus.init && item.status != SubmissionStatus.wait">
                      <a-popconfirm placement="topRight" title="你确定删除该条记录吗?" ok-text="确定" cancel-text="取消"
                        @confirm="handleDeleteOrder(item.id)">
                        <button @click.stop
                          class="text-sm text-gray-400 transition-colors hover:text-red-500 focus:outline-none">
                          删除
                        </button>
                      </a-popconfirm>
                    </div>

                    <a-dropdown @click.stop :trigger="['click']"
                      v-if="isHuawen && item.status != SubmissionStatus.ing && item.status != SubmissionStatus.payed && item.status != SubmissionStatus.init && item.status != SubmissionStatus.wait">
                      <a-tooltip title="更多" placement="top" overlay-class-name="black-tooltip">
                        <div class="h-9 flex items-center justify-center cursor-pointer w-9 sm:w-[71px]">
                          <more theme="outline" size="24" fill="#333" />
                        </div>
                      </a-tooltip>
                      <template #overlay>
                        <a-menu>
                          <a-menu-item key="report" @click="handlePressReport">
                            <div class="flex items-center">
                              <attention theme="outline" size="16" fill="#333" class="mr-1" />
                              <span class="text-sm">举报</span>
                            </div>
                          </a-menu-item>
                          <a-menu-item key="delete" v-if="item.id">
                            <a-popconfirm title="确定删除该条消息吗？" ok-text="确定" cancel-text="取消"
                              @confirm="handleDeleteOrder(item.id)">
                              <div class="flex items-center">
                                <delete-one theme="outline" size="16" fill="#333" class="mr-1" />
                                <span class="text-sm">删除</span>
                              </div>
                            </a-popconfirm>
                          </a-menu-item>
                        </a-menu>
                      </template>
                    </a-dropdown>
                  </div>
                </div>
                <div v-else class="ml-auto">
                  <a-popconfirm v-if="!isHuawen" placement="topRight" title="你确定删除该条记录吗?" ok-text="确定" cancel-text="取消"
                    @confirm="handleDeleteOrder(item.id)">
                    <button @click.stop
                      class="text-sm text-gray-400 transition-colors hover:text-red-500 focus:outline-none">
                      删除
                    </button>
                  </a-popconfirm>

                  <a-dropdown @click.stop :trigger="['click']" v-if="isHuawen">
                    <a-tooltip title="更多" placement="top" overlay-class-name="black-tooltip">
                      <div class="h-9 flex items-center justify-center cursor-pointer w-9 sm:w-[71px]">
                        <more theme="outline" size="24" fill="#333" />
                      </div>
                    </a-tooltip>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item key="report" @click="handlePressReport">
                          <div class="flex items-center">
                            <attention theme="outline" size="16" fill="#333" class="mr-1" />
                            <span class="text-sm">举报</span>
                          </div>
                        </a-menu-item>
                        <a-menu-item key="delete" v-if="item.id">
                          <a-popconfirm title="确定删除该条消息吗？" ok-text="确定" cancel-text="取消"
                            @confirm="handleDeleteOrder(item.id)">
                            <div class="flex items-center">
                              <delete-one theme="outline" size="16" fill="#333" class="mr-1" />
                              <span class="text-sm">删除</span>
                            </div>
                          </a-popconfirm>
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="pb-16 mt-4 sm:mt-6 md:pb-0">
      <Pagination v-model:current="reqParams.pageNo" :total-pages="reqParams.pages" :total="reqParams.total"
        @change="handlePageChange" />
    </div>
  </div>

  <!-- 使用举报弹窗组件 -->
  <ReportModal v-model:open="reportModalOpen" />
</template>

<script setup lang="ts">
import { getSubmissionOrderList, submissionDelete } from '@/api/order'
import { type PageResult } from '@/api/typing'
import EmptyState from '@/components/EmptyState.vue'
import { type SubmissionOrderInfo } from '@/services/types/order'
import { type SubmissionPaperAnswerInfo } from '@/services/types/submission'
import { Attention, Copy, DeleteOne, More } from '@icon-park/vue-next'
import { Spin as ASpin, message } from 'ant-design-vue'
import { computed, onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import ReportModal from '~/components/Common/ReportModal.vue'
import Pagination from '~/components/Pagination.vue'
import { UserService } from '~/services/user'
import { BookService } from '~/utils/book/book'
import { CREATE_CODE, SubmissionStatus } from '~/utils/constants'
import { saveSubmissionDraft } from '~/utils/pc_utils'
import { StarloveUtil } from '~/utils/util'
import { getAttachmentsFromSubmission, isJSON } from '~/utils/utils'
const { $eventBus } = useNuxtApp();


const ordersPageResult = ref<PageResult<SubmissionOrderInfo> | undefined>(undefined)

const app = useApp()
const router = useRouter()

// 翻译：translate、改写：rewrite-document、去AI痕迹：rewrite-document-ai、思维导图：mind_map
const goCreateResultPageCode = ['translate', 'rewrite-document', 'rewrite-document-ai', 'mind_map', 'ppt']

const orderList = computed(() => {
  if (!ordersPageResult.value) {
    return []
  }
  return ordersPageResult.value.records
})

const reqParams = reactive({
  pageNo: 1,
  pageSize: 10,
  pages: 0,
  total: 0,
  topic: ''
})

const isLoading = ref(true)

const loadData = async () => {

  // console.log('creations loadData  ==>', reqParams)
  isLoading.value = true
  const params = {
    ...reqParams,
  }
  const res = await getSubmissionOrderList(params)
  // console.log('creations loadData  res ==>', res)
  if (!res.ok || !res.data) {
    isLoading.value = false
    return
  }
  ordersPageResult.value = res.data
  reqParams.pages = res.data.pages || 0
  reqParams.total = res.data.total || 0
  isLoading.value = false
}

const handlePageChange = () => {
  loadData()
}

function shortenString(str: string) {
  if (str.length > 10) {
    return str.substring(0, 9) + '...'
  } else {
    return str
  }
}

const getTitle = (item: SubmissionOrderInfo) => {
  if (!item.formData.topic) {
    if (item.formData.attachments && item.formData.attachments.length > 0) {
      if (item.formData.attachments[0].fileName) {
        const name = decodeURIComponent(item.formData.attachments[0].fileName)
        if (name) {
          return name.replace(/\.(pdf|docx|doc)$/i, '')
        }
      }
    }
  }
  if (item.formData.topic && item.formData.topic != '0' && item.formData.topic != '-') {
    return item.formData.topic || ''
  }

  if (item.answer) {
    if (isJSON(item.answer)) {
      const data = JSON.parse(item.answer)
      if (!data.content) {
        const content = item.formData?.params?.content || item.formData?.params?.ask || item.creatorName
        return shortenString(content)
      }
      return data.content || ''
    }
    return shortenString(item.answer)
  }
  return item.creatorName || ''
}

const orderStatusText = (status: any, createCode: string) => {
  if (!status) {
    return ''
  }
  if (createCode && createCode == CREATE_CODE.BOOK) {
    return '已完成'
  }
  if (status == SubmissionStatus.needPay) {
    return '待确认'
  }
  if (status == SubmissionStatus.wait) {
    return '生成中'
  }
  if (status == SubmissionStatus.init) {
    return '生成中'
  }
  if (status == SubmissionStatus.payed) {
    return '生成中'
  }
  if (status == SubmissionStatus.ing) {
    return '生成中'
  }
  if (status == SubmissionStatus.done) {
    return '已完成'
  }
  if (status == SubmissionStatus.error) {
    return '写作失败'
  }
  return status
}

const handleDeleteOrder = async (id: string) => {
  const res = await submissionDelete({ id })

  if (!res.ok) {
    return
  }
  await loadData()
}

const getDownloadPath = (answer: any) => {
  try {
    const answerInfo: SubmissionPaperAnswerInfo = JSON.parse(answer)
    if (!answerInfo.finalDocxPath && !answerInfo.pptxPath) {
      return ''
    }
    return answerInfo.finalDocxPath || answerInfo.pptxPath
  } catch (error) {
    return ''
  }
}

const handleDownloadFile = (item: any) => {
  if (!item.answer || !isJSON(item.answer)) {
    return
  }
  const finalPath = getDownloadPath(item.answer)
  if (!finalPath) {
    return
  }
  window.location.href = finalPath
}


const handleOrderItem = async (item: any) => {

  if (item.creatorCode === CREATE_CODE.BOOK) {
    const res = await BookService.getBookBasicInfo({
      submission_id: item.id
    })
    if (!res || !res.key) {
      return message.warning('找不到指定的书籍')
    }
    router.push(`/book/content/${res?.key}`)
    return
  }
  if (item.status == SubmissionStatus.done && item.creatorCode === CREATE_CODE.PPT) {
    try {
      const answerInfo: SubmissionPaperAnswerInfo = JSON.parse(item.answer)
      if (answerInfo?.version == '2') {
        let inSlidesUrl = `${StarloveUtil.getInSlidesBaseUrl()}/?id=${item.id}`
        if (app.value?.isDesktop) {
          inSlidesUrl += `&t=${UserService.getToken()}`
        }
        window.open(inSlidesUrl, '_blank')
        return
      }
    } catch (error) {
      console.error(error)
    }
  }
  if (goCreateResultPageCode.includes(item.creatorCode)) {
    router.push(`/create/detail?id=${item.id}&code=${item.creatorCode}`)
    return
  }
  if (item.status == SubmissionStatus.done || item.creatorCode === 'online_editing') {
    let aiEditorUrl = `${StarloveUtil.getAIEditorBaseUrl()}/?id=${item.id}`
    if (app.value?.isDesktop) {
      aiEditorUrl += `&t=${UserService.getToken()}`
      // return
    }
    // window.open(aiEditorUrl, '_blank')
    window.location.href = aiEditorUrl
    return
  }
  if (item.status && item.status != SubmissionStatus.done) {
    router.push(`/create/detail?id=${item.id}&code=${item.creatorCode}`)
    return
  }
  router.push(`/create`)
  return
}

// const getLink = (item: any) => {
//     if (item.creatorCode === CREATE_CODE.BOOK) {
//         return ``
//     }
//     if (item.status == SubmissionStatus.done || item.creatorCode === 'online_editing') {
//         return ``
//     }

//     if (item.status && item.status != SubmissionStatus.done) {
//         return `/create/detail?id=${item.id}&code=${item.creatorCode}`
//     }
//     return `/create`
// }

const isCanDownload = (item: any) => {
  if (!item.answer || !isJSON(item.answer)) {
    return false
  }
  const finalDocxPath = getDownloadPath(item.answer)
  if (!finalDocxPath) {
    return false
  }
  if (item.status != SubmissionStatus.done) {
    return false
  }
  return true
}


const getFreeHelpText = (helpStatus: any) => {
  if (helpStatus == 2) {
    return '(免费助力中)'
  }
  if (helpStatus == 3) {
    return '(免费助力完成)'
  }
  return ''
}

// 复制创作任务
const handlePressEditCreate = (item: any) => {
  const params: {
    creatorCode: any;
    formData: any;
    attachments?: any;
  } = {
    creatorCode: item.creatorCode,
    formData: item.formData,
    // console.log('handlePressEdit props.submission ==>', props.submission)
  }
  if (item.formData?.attachments) {
    params['attachments'] = getAttachmentsFromSubmission(item)
  }
  saveSubmissionDraft(params)
  router.replace({ path: `/create/${item.creatorCode}` })
}

const handleSearch = (query: { keyword: string }) => {
  reqParams.topic = query.keyword
  reqParams.pageNo = 1
  loadData()
}
onMounted(() => {

  loadData()
  $eventBus.on(StarloveConstants.keyOfEventBus.myQuestionSearch, handleSearch)

})

onBeforeUnmount(() => {
  $eventBus.off(StarloveConstants.keyOfEventBus.myQuestionSearch, handleSearch);
});

// 举报相关状态
const isHuawen = computed(() => {
  return sessionStorage.getItem('utm_source') == 'huawei'
})

const reportModalOpen = ref(false);

const handlePressReport = () => {
  reportModalOpen.value = true;
}

</script>


<style scoped>
.active {
  color: #1e99ff;
}
</style>
