<template>
  <a-modal v-model:open="openVisible" @cancel="handleCancel" :footer="null" :width="600" :getContainer="getContainer"
    :z-index="2003">
    <div class="receiving-modal-view">
      <div class="header-title">请填写赠品AI鼠标的收货地址</div>
      <div class="order-hint" v-if="isShowHint">
        请完善收货信息，也可以稍后在"个人中心/我的-我的订单"中完成。
      </div>
      <div>
        <a-spin :spinning="submitLoaded">
          <a-form :model="formState" name="basic" :label-col="{ span: 6 }" :wrapper-col="{ span: 14 }"
            autocomplete="off" @finish="onFinish" @finishFailed="onFinishFailed">
            <a-form-item label="收货人" name="username" :rules="[{ required: true, message: '请填写收货人!' }]">
              <a-input v-model:value="formState.username" placeholder="请填写收货人" />
            </a-form-item>

            <a-form-item label="手机号码" name="phone" :rules="[
              { required: true, message: '请填写手机号!', trigger: 'blur' },
              { validator: checkPhoneExist, trigger: 'blur' }
            ]">
              <a-input-number style="width: 100%" v-model:value="formState.phone" maxlength="11" placeholder="请填写手机号" />
            </a-form-item>

            <a-form-item label="所在地区" name="location" :rules="[{ required: true, message: '请选择所在地区!' }]">
              <a-cascader v-model:value="formState.location" :options="options" :load-data="loadData"
                placeholder="请选择所在地区" change-on-select :getPopupContainer="getContainer"
                :popup-style="{ zIndex: 10001 }" />
            </a-form-item>

            <a-form-item label="详细地址" name="address" :rules="[{ required: true, message: '请填写详细地址!' }]">
              <a-textarea v-model:value="formState.address" :rows="2" :maxlength="100" placeholder="请填写详细地址" />
            </a-form-item>

            <div class="flex-center space-x-4" style="margin-bottom: 20px;">
              <a-button type="primary"
                class="bg-gradient-to-r from-gray-200 to-gray-300 hover:from-gray-200 hover:to-gray-300"
                @click="handleCancel">
                稍后填写
              </a-button>
              <a-button type="primary" html-type="submit"
                class="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700">确认信息</a-button>
            </div>
          </a-form>
        </a-spin>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { getRechargeOrderList } from '@/api/order'
import { type PageResult } from '@/api/typing'
import { addUserAddress, areaList, getAddressList, setOrderAddress } from '@/api/user'
import { type RechargeOrderInfo } from '@/services/types/order'
import { RechargeOrderStatus } from '@/utils/constants'
import { validatePhoneNumber } from '@/utils/utils'
import { type CascaderProps, Cascader as ACascader, Form as AForm, FormItem as AFormItem, Input as AInput, InputNumber as AInputNumber, Modal as AModal, Spin as ASpin, message } from 'ant-design-vue'
import { type Rule } from 'ant-design-vue/es/form'
import { computed, onMounted, reactive, ref } from 'vue'

interface Props {
  openVisible: boolean
  isShowHint: boolean
  orderId?: string | undefined
  containerId: string
  isIframe?: boolean
}

interface FormState {
  username: string
  phone: string
  province: string
  city: string
  area: string
  location: (string | number)[]
  address: string
}

const formState = reactive<FormState>({
  username: '',
  phone: '',
  province: '',
  city: '',
  area: '',
  location: [],
  address: ''
})

const submitLoaded = ref(false)

const props = withDefaults(defineProps<Props>(), {
  isIframe: false
})

const emit = defineEmits(['update:openVisible', 'addSuccess', 'cancel'])
const openVisible = computed({
  get: () => props.openVisible,
  set: (val) => {
    emit('update:openVisible', val)
  }
})

const ordersPageResult = ref<PageResult<RechargeOrderInfo> | undefined>(undefined)
const newOrderId = computed(() => {
  if (props.orderId) {
    return props.orderId
  }
  if (!ordersPageResult.value) {
    return
  }
  const list = ordersPageResult.value.records.filter(
    (item) => item.status == RechargeOrderStatus.replenishAddress
  )
  if (list.length == 0) {
    return
  }
  return list[0].id
})

const options = ref<CascaderProps['options']>([])

const getContainer = () => {
  const data = document.getElementById(props.containerId)
  return data || document.body
}

const loadData: CascaderProps['loadData'] = async (selectedOptions) => {
  const targetOption = selectedOptions[selectedOptions.length - 1]
  // console.log('loadData  selectedOptions ==>', selectedOptions)
  try {
    const level = selectedOptions.length
    const data = await loadOptionsData(targetOption.value)
    targetOption.children = data.map((item: any) => ({
      label: item.name,
      value: item.id,
      isLeaf: level === 2
    }))
  } catch (error) {
    message.error('加载失败，请重试')
    targetOption.loading = false
  } finally {
    targetOption.loading = false
  }
}

const handleCancel = () => {
  openVisible.value = false
  if (props.isIframe) {
    emit('cancel')
    // console.log('编辑器调用  点击稍后在填写')
  }
}

const checkPhoneExist = async (rule: Rule, value: string) => {
  if (!validatePhoneNumber(value) && value !== undefined) {
    throw new Error('手机号格式错误！')
  }
}

const findLabelById = (options: any, targetId: any) => {
  const option = options.find((item: any) => item.value === targetId)
  return option ? option.label : null
}

const getChildrenById = (options: any, targetId: any) => {
  const option = options.find((item: any) => item.value === targetId)
  return option ? option.children || [] : []
}

const onFinish = async (values: any) => {
  // console.log('Success:', values)
  try {
    submitLoaded.value = true

    const provinceLabel = findLabelById(options.value, values.location[0])
    const provinceOptions = getChildrenById(options.value, values.location[0])

    const cityLabel = findLabelById(provinceOptions, values.location[1])
    const cityOptions = getChildrenById(provinceOptions, values.location[1])

    const districtLabel = findLabelById(cityOptions, values.location[2])

    const params = {
      provinceName: provinceLabel,
      provinceCode: values.location[0],
      cityName: cityLabel,
      cityCode: values.location[1],
      districtName: districtLabel,
      districtCode: values.location[2],
      addressDetail: values.address,
      receiverName: values.username,
      receiverMobile: values.phone
    }
    // console.log('params:', params)
    const res = await addUserAddress(params)
    submitLoaded.value = false
    if (!res.ok) {
      message.error(res.message || '地址添加失败')
      return
    }
    await onAddRessSuccess()
    message.success('提交成功')
    setTimeout(() => {
      openVisible.value = false
      emit('addSuccess')
    }, 100)
  } catch (error) {
    submitLoaded.value = false
  }
}

const onAddRessSuccess = async () => {
  const list = await getAddressData()
  if (list.length == 0 || !newOrderId.value) {
    return
  }
  const params = {
    orderId: newOrderId.value,
    addressId: list[0].id
  }
  await setOrderAddress(params)
}

const getAddressData = async () => {
  const res = await getAddressList()
  if (!res.ok || !res.data) {
    return []
  }
  return res.data || []
}

const onFinishFailed = (errorInfo: any) => {
  console.log('Failed:', errorInfo)
}

const loadOptionsData = async (pid: any) => {
  const params = { pid }
  const res = await areaList(params)
  if (!res.ok) {
    throw new Error(res.message || '数据加载失败')
  }
  return res.data
}

// 初始化加载省份数据
const loadProvinces = async () => {
  try {
    const data = await loadOptionsData(0)
    options.value = data.map((item: any) => ({
      label: item.name,
      value: item.id,
      isLeaf: false
    }))
  } catch (error) {
    message.error('加载省份数据失败')
  }
}

const loadOrderData = async () => {
  const params = {
    pageNo: 1,
    pageSize: 100,
    pages: 0
  }
  const res = await getRechargeOrderList(params)
  if (!res.ok || !res.data) {
    return
  }
  ordersPageResult.value = res.data
}

onMounted(() => {
  if (!props.orderId) {
    loadOrderData()
  }
  loadProvinces()
})
</script>

<style lang="scss" scoped>
.receiving-modal-view {
  .header-title {
    font-weight: 500;
    font-size: 18px;
    color: #333333;
    line-height: 29px;
    text-align: center;
    margin-bottom: 15px;
  }

  .order-hint {
    text-align: center;
    margin-bottom: 20px;
    font-size: 14px;
    color: rgb(135, 134, 134);
    line-height: 21px;
  }

  .cancel-button {
    border: none;
    background: #f5f5f5;
    color: #333333;
    padding: 0 40px;
  }

  .submit-button {
    margin-left: 50px;
    padding: 0 40px;
    border: none;
    background: linear-gradient(270deg, #42e5b5 0%, #249cff 100%);
  }

  .flex-center {
    display: flex;
    justify-content: center;
  }
}
</style>
