<template>
    <UModal :model-value="modelValue" @update:model-value="$emit('update:modelValue', $event)" :ui="{
        container: 'items-center',
    }">
        <div class="pb-8">
            <div class="w-full flex justify-evenly items-center p-4">
                <div class="flex-1 p-2"></div>
                <div class="flex p-2">
                    <div class="text-center text-md font-medium text-gray-800 ">
                        文献未能成功加入知识库
                    </div>
                </div>
                <div class="flex-1 flex justify-end p-2">
                    <button @click="handleClose" class="text-gray-400 hover:text-gray-600">
                        <close theme="outline" size="18" />
                    </button>
                </div>
            </div>
            <div class="px-10">
                <p class="text-sm text-gray-800 leading-relaxed mb-5">
                    有些搜索结果的PDF文件可能因为网络原因暂时无法获取，导致"添加到知识库"操作未完成。
                </p>

                <div class="mb-5">
                    <p class="text-sm text-gray-800 font-semibold mb-3">建议</p>
                    <div class="flex items-start mb-2 text-sm text-gray-600 leading-tight">
                        <span class="flex-1">
                            1.点击文件链接查看原文，下载到本地后在知识库页面手动上传
                        </span>
                    </div>
                    <div class="flex items-start mb-2 text-sm text-gray-600 leading-tight">
                        <span class="flex-1">2.稍后再次尝试重新添加到知识库</span>
                    </div>
                </div>

                <p class="text-sm text-gray-500 leading-relaxed m-0">
                    我们正在持续优化下载策略，后续将尽量减少这类中断。感谢理解与耐心！
                </p>
            </div>
        </div>
    </UModal>
</template>

<script setup lang="ts">
import { Close } from '@icon-park/vue-next';

const props = defineProps<{
    modelValue: boolean
}>()

const emit = defineEmits<{
    (e: 'update:modelValue', value: boolean): void
}>()

const handleClose = () => {
    emit('update:modelValue', false)
}

</script>
