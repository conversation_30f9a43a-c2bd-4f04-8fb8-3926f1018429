<template>
    <div style="border-radius: 15px 15px 0px 0px;" class="flex-1 bg-[#FFFFFF] flex flex-col overflow-y-auto">
        <!-- 上 -->
        <div class="w-full h-[60px] p-1 sm:p-2 md:p-4 flex items-center border-b border-[#F3F3F3] flex-shrink-0">
            <!-- 左 -->
            <div class="w-1/5  min-w-[240px] flex items-center space-x-2 flex-shrink-0">
                <Iconfont name="xueshuwenzhang" :size="15"></Iconfont>
                <span class="text-[#333333] text-sm">文章</span>
            </div>
            <!-- 右 -->
            <div class="flex flex-row items-center justify-between  flex-1">
                <!-- <div class="md:flex-none text-[#999999] text-sm text-right md:text-left">
                    找到约 {{ page.total }} 条结果
                </div> -->
                <div class="flex justify-center md:hidden">
                    <a-popconfirm ok-text="确认" cancel-text="取消" :icon="null" placement="bottom" :show-arrow="false"
                        trigger="click" :open="showMobileFilter" @openChange="handleMobileFilterOpenChange">
                        <template #title>
                            <div class="w-80 p-2 space-y-4">
                                <!-- 时间筛选 -->
                                <div class="space-y-2">
                                    <div class="text-sm font-medium text-[#333]">时间范围</div>
                                    <div class="flex flex-wrap gap-2">
                                        <button @click="selectTimeRange('')"
                                            class="px-3 py-1.5 text-xs rounded-full border transition-colors"
                                            :class="filters.timeRange === '' ? 'bg-[#2551B5] text-white border-[#2551B5]' : 'bg-white text-[#666] border-[#E5E7EB] hover:border-[#2551B5]'">
                                            时间不限
                                        </button>
                                        <button v-for="item in timeTabs" :key="item.time"
                                            @click="selectTimeRange(item.time)"
                                            class="px-3 py-1.5 text-xs rounded-full border transition-colors"
                                            :class="filters.timeRange === item.time ? 'bg-[#2551B5] text-white border-[#2551B5]' : 'bg-white text-[#666] border-[#E5E7EB] hover:border-[#2551B5]'">
                                            {{ item.name }}
                                        </button>
                                    </div>
                                </div>

                                <!-- 文章类型筛选 -->
                                <div class="space-y-2">
                                    <div class="text-sm font-medium text-[#333]">文章类型</div>
                                    <div class="flex flex-wrap gap-2">
                                        <button @click="selectArticleType(0)"
                                            class="px-3 py-1.5 text-xs rounded-full border transition-colors"
                                            :class="filters.as_rr === 0 ? 'bg-[#2551B5] text-white border-[#2551B5]' : 'bg-white text-[#666] border-[#E5E7EB] hover:border-[#2551B5]'">
                                            类型不限
                                        </button>
                                        <button @click="selectArticleType(1)"
                                            class="px-3 py-1.5 text-xs rounded-full border transition-colors"
                                            :class="filters.as_rr === 1 ? 'bg-[#2551B5] text-white border-[#2551B5]' : 'bg-white text-[#666] border-[#E5E7EB] hover:border-[#2551B5]'">
                                            评论性文章
                                        </button>
                                    </div>
                                </div>

                                <!-- 排序方式筛选 -->
                                <div class="space-y-2">
                                    <div class="text-sm font-medium text-[#333]">排序方式</div>
                                    <div class="flex flex-wrap gap-2">
                                        <button @click="selectSortType(0)"
                                            class="px-3 py-1.5 text-xs rounded-full border transition-colors"
                                            :class="filters.scisbd === 0 ? 'bg-[#2551B5] text-white border-[#2551B5]' : 'bg-white text-[#666] border-[#E5E7EB] hover:border-[#2551B5]'">
                                            按相关性
                                        </button>
                                        <button @click="selectSortType(1)"
                                            class="px-3 py-1.5 text-xs rounded-full border transition-colors"
                                            :class="filters.scisbd === 1 ? 'bg-[#2551B5] text-white border-[#2551B5]' : 'bg-white text-[#666] border-[#E5E7EB] hover:border-[#2551B5]'">
                                            按日期
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </template>

                        <button
                            class="flex items-center space-x-1 px-3 py-1.5 bg-[#F5F7FF] border border-[#E5E7EB] rounded-lg text-sm text-[#666] hover:bg-[#EEF2FF] hover:text-[#2551B5] transition-colors">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z">
                                </path>
                            </svg>
                            <span>筛选</span>
                            <svg class="w-3 h-3 transition-transform" :class="showMobileFilter ? 'rotate-180' : ''"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 9l-7 7-7-7">
                                </path>
                            </svg>
                        </button>
                    </a-popconfirm>
                </div>
            </div>
        </div>

        <!-- 下 -->
        <div class="flex-1 h-full overflow-y-auto flex">
            <!-- 左侧筛选区域 - 桌面端显示 -->
            <div class="hidden md:flex w-1/5 min-w-[240px] flex-col space-y-6 p-4 flex-shrink-0">
                <!-- 时间筛选 -->
                <div class="flex flex-col space-y-3 pl-[10%] text-sm">
                    <div class="flex flex-col space-y-2">
                        <div class="cursor-pointer" @click="selectTimeRange('')"
                            :class="filters.timeRange === '' ? 'text-[#2551B5] font-medium' : 'text-[#666]'">
                            时间不限
                        </div>
                        <div v-for="item in timeTabs" :key="item.time" class="cursor-pointer"
                            @click="selectTimeRange(item.time)"
                            :class="filters.timeRange === item.time ? 'text-[#2551B5] font-medium' : 'text-[#666]'">
                            {{ item.name }}
                        </div>
                        <div class="cursor-pointer" @click="toggleCustomRange"
                            :class="filters.timeRange === 'custom' ? 'text-[#2551B5] font-medium' : 'text-[#666] hover:text-[#333]'">
                            自定义范围...
                        </div>

                        <!-- 只有选中自定义范围时才显示年份选择区域 -->
                        <div v-if="filters.timeRange === 'custom'" class="relative">
                            <!-- 输入框区域 -->
                            <div @click="addCustomRange" data-year-picker="true"
                                class="p-2 gap-2 flex items-center flex-row rounded-lg border border-gray-400 cursor-text hover:border-blue-500 transition-colors w-full max-w-full">
                                <input type="number" :value="filters.as_ylo || ''" placeholder="选择年份"
                                    @input="handleStartYearInput" @blur="handleStartYearBlur"
                                    @focus="handleStartYearFocus" @click="showCustomRange = true"
                                    class="w-[45%] outline-none bg-transparent text-sm cursor-text min-w-0 no-spinner text-center year-input" />
                                <ArrowRight theme="outline" size="20" fill="#999999" @click="showCustomRange = true"
                                    class="cursor-pointer flex-shrink-0" />
                                <input type="number" :value="filters.as_yhi || ''" placeholder="选择年份"
                                    @input="handleEndYearInput" @blur="handleEndYearBlur" @focus="handleEndYearFocus"
                                    @click="showCustomRange = true"
                                    class="w-[45%] outline-none bg-transparent text-sm cursor-text min-w-0 no-spinner text-center year-input" />
                                <Plan theme="outline" size="20" fill="#999999" @click="showCustomRange = true"
                                    class="cursor-pointer flex-shrink-0" />
                            </div>

                            <!-- 年份选择浮窗 -->
                            <div v-if="showCustomRange" ref="customRangeRef"
                                class="absolute left-0 right-0 mt-2 w-[160px] bg-white rounded-xl shadow-2xl ring-1 ring-indigo-200/10 transform transition-all duration-200 ease-out z-50"
                                @click.stop>


                                <!-- 年份选择区域 -->
                                <div class="flex flex-1">
                                    <!-- 开始年份 -->
                                    <div class="flex-1">
                                        <div class="h-44 overflow-y-auto custom-scrollbar">
                                            <div v-for="year in yearOptions" :key="year"
                                                class="px-3 py-1 cursor-pointer hover:bg-gray-50 text-center text-sm"
                                                :class="tempStartYear === year ? 'bg-blue-50 text-blue-600 font-medium' : ''"
                                                @click="tempStartYear = year">
                                                {{ year }}
                                            </div>
                                        </div>
                                    </div>

                                    <div class="h-hull w-[1px] bg-gray-300"></div>

                                    <!-- 结束年份 -->
                                    <div class="flex-1">
                                        <div class="h-44 overflow-y-auto custom-scrollbar">
                                            <div v-for="year in yearOptions" :key="year"
                                                class="px-3 py-1 cursor-pointer hover:bg-gray-50 text-center text-sm"
                                                :class="tempEndYear === year ? 'bg-blue-50 text-blue-600 font-medium' : ''"
                                                @click="selectEndYear(year)">
                                                {{ year }}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 按钮区域 -->
                                <div class="flex justify-end space-x-2 border-t border-gray-300 py-2 px-1">
                                    <button class="px-2 py-1 rounded-md text-xs text-gray-800 bg-gray-200"
                                        @click="clearCustomRange">
                                        清除
                                    </button>
                                    <button class="px-2 py-1 rounded-md text-xs text-white bg-blue-600"
                                        @click="confirmCustomRange">
                                        确定
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 文章类型筛选 -->
                <div class="flex flex-col space-y-3 pl-[10%] text-sm">
                    <div class="flex flex-col space-y-2">
                        <div class="cursor-pointer" @click="selectArticleType(0)"
                            :class="filters.as_rr === 0 ? 'text-[#2551B5] font-medium' : 'text-[#666]'">
                            类型不限
                        </div>
                        <div class="cursor-pointer" @click="selectArticleType(1)"
                            :class="filters.as_rr === 1 ? 'text-[#2551B5] font-medium' : 'text-[#666]'">
                            评论性文章
                        </div>
                    </div>
                </div>

                <!-- 排序方式筛选 -->
                <div class="flex flex-col space-y-3 pl-[10%] text-sm">
                    <div class="flex flex-col space-y-2">
                        <div class="cursor-pointer" @click="selectSortType(0)"
                            :class="filters.scisbd === 0 ? 'text-[#2551B5] font-medium' : 'text-[#666]'">
                            按相关性
                        </div>
                        <div class="cursor-pointer" @click="selectSortType(1)"
                            :class="filters.scisbd === 1 ? 'text-[#2551B5] font-medium' : 'text-[#666]'">
                            按日期
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧内容区域 -->
            <div class="flex-1 md:w-4/5 h-full overflow-y-auto">
                <ScholarResultsContent :loading="loading" :cholar-list="cholarList" :search-query="searchQuery"
                    :page="page" @go-to-page="goToPage" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { scholarSearch } from '@/api/scholar';
import Iconfont from '@/components/Iconfont.vue';
import { ArrowRight, Plan } from "@icon-park/vue-next";
import { message } from 'ant-design-vue';
import { onMounted, onUnmounted, reactive, ref } from 'vue';
import { UserService } from '~/services/user';
import { useExchangeSearchStore } from '~/stores/exchangeSearch';
import { HTTP_STATUS } from '~/utils/constants';
import ScholarResultsContent from './ScholarResultsContent.vue';

// IndexDB 缓存工具类
class ScholarCacheManager {
    private dbName = 'ScholarSearchCache';
    private storeName = 'searchResults';
    private version = 1;
    private db: IDBDatabase | null = null;

    // 初始化数据库
    async init(): Promise<void> {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);

            request.onerror = () => reject(request.error);
            request.onsuccess = () => {
                this.db = request.result;
                resolve();
            };

            request.onupgradeneeded = (event) => {
                const db = (event.target as IDBOpenDBRequest).result;
                if (!db.objectStoreNames.contains(this.storeName)) {
                    db.createObjectStore(this.storeName, { keyPath: 'cacheKey' });
                }
            };
        });
    }

    // 生成缓存键
    generateCacheKey(params: any): string {
        const keyData = {
            query: params.query || '',
            page: params.page || 1,
            as_rr: params.as_rr || 0,
            scisbd: params.scisbd || 0,
            as_ylo: params.as_ylo || 0,
            as_yhi: params.as_yhi || 0
        };
        return JSON.stringify(keyData);
    }

    // 存储缓存
    async setCache(params: any, data: any): Promise<void> {
        if (!this.db) await this.init();

        const cacheKey = this.generateCacheKey(params);
        const cacheData = {
            cacheKey,
            data,
            timestamp: Date.now()
        };

        return new Promise((resolve, reject) => {
            const transaction = this.db!.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);
            const request = store.put(cacheData);

            request.onerror = () => reject(request.error);
            request.onsuccess = () => resolve();
        });
    }

    // 获取缓存
    async getCache(params: any): Promise<any | null> {
        if (!this.db) await this.init();

        const cacheKey = this.generateCacheKey(params);

        return new Promise((resolve, reject) => {
            const transaction = this.db!.transaction([this.storeName], 'readonly');
            const store = transaction.objectStore(this.storeName);
            const request = store.get(cacheKey);

            request.onerror = () => reject(request.error);
            request.onsuccess = () => {
                const result = request.result;
                resolve(result ? result.data : null);
            };
        });
    }

    // 清空所有缓存
    async clearAllCache(): Promise<void> {
        if (!this.db) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db!.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);
            const request = store.clear();

            request.onerror = () => reject(request.error);
            request.onsuccess = () => resolve();
        });
    }
}

// 创建缓存管理器实例
const cacheManager = new ScholarCacheManager();

// Props 定义
interface Props {
    searchQuery: string;
}

const props = withDefaults(defineProps<Props>(), {
    searchQuery: ''
});

const knowledgeAssistantMemberInfo = computed(() => {
    return UserService.getKnowledgeAssistantMemberInfo()
})


// 内部状态管理
const page = reactive({
    current: 1,
    pageSize: 15,
    total: 0,
    pages: 1,
});

// 搜索结果和加载状态
const cholarList = ref<any[]>([]);
const loading = ref(false);

// 筛选条件
const filters = reactive({
    timeRange: '', // 时间范围，空字符串表示不限
    as_rr: 0, // 是否为评论性文章 (0: 不限, 1: 评论性文章)
    scisbd: 0, // (0:相关性,  1:日期)
    // as_vis: 1, // 是否包含引用 (0: 不包含, 1: 包含) - 默认包含引用
    // as_sdt: 0, // 是否包含专利 (0: 不包含, 7: 包含)
    as_ylo: 0, // 开始时间 - 默认为0表示不限
    as_yhi: 0, // 结束时间 - 默认为0表示不限
    includePatents: false, // 专利复选框状态
    includeCitations: true // 引用复选框状态 - 默认选中
});

// 移动端筛选相关
const showMobileFilter = ref(false);

// 自定义时间范围相关
const showCustomRange = ref(false);
const customRangeRef = ref<HTMLElement | null>(null);
const tempStartYear = ref<number | null>(null);
const tempEndYear = ref<number | null>(null);

// 生成年份选项（从1990年到当前年份+5年）
const yearOptions = ref<number[]>([]);
const initYearOptions = () => {
    const currentYear = new Date().getFullYear();
    for (let year = 1990; year <= currentYear + 5; year++) {
        yearOptions.value.push(year);
    }
};
initYearOptions();

// 获取当前年份
const currentYear = new Date().getFullYear();

// 构建时间选项数组
const timeTabs = ref([
    {
        name: `${currentYear}以来`,
        time: currentYear.toString()
    },
    {
        name: `${currentYear - 1}以来`,
        time: (currentYear - 1).toString()
    },
    {
        name: `${currentYear - 3}以来`,
        time: (currentYear - 3).toString()
    }
]);



// 学术搜索API调用
const getScholar = async (customQuery?: string) => {
    const queryToUse = customQuery || props.searchQuery;
    if (!queryToUse.trim()) return;

    const params: any = {
        "query": queryToUse,
        "page": page.current,
    };

    // 添加筛选参数
    if (filters.as_rr !== 0) {
        params.as_rr = filters.as_rr;
    }

    if (filters.scisbd !== 0) {
        params.scisbd = filters.scisbd;
    }

    // if (filters.as_vis !== 0) {
    //     params.as_vis = filters.as_vis;
    // }


    // if (filters.as_sdt !== 0) {
    //     params.as_sdt = filters.as_sdt;
    // }

    // 处理时间范围
    if (filters.timeRange && filters.timeRange !== '') {
        if (filters.timeRange === 'custom') {
            // 自定义时间范围
            if (filters.as_ylo && filters.as_ylo !== 0) params.as_ylo = filters.as_ylo;
            if (filters.as_yhi && filters.as_yhi !== 0) params.as_yhi = filters.as_yhi;
        } else {
            // 预设时间范围（从某年开始到现在）
            params.as_ylo = filters.timeRange;
        }
    }

    // 先尝试从缓存获取数据
    try {
        const cachedData = await cacheManager.getCache(params);
        if (cachedData) {
            console.log('使用缓存数据 - 显示800ms loading');
            // 缓存命中时也显示loading状态
            loading.value = true;

            // 延迟800ms后显示缓存数据，提供更好的用户体验
            setTimeout(() => {
                cholarList.value = cachedData.records || [];
                Object.assign(page, {
                    current: cachedData.current,
                    total: cachedData.total,
                    pageSize: cachedData.size,
                    pages: cachedData.pages,
                });
                loading.value = false;
            }, 800);
            return;
        }
    } catch (cacheError) {
        console.log('缓存读取失败，将调用接口:', cacheError);
    }

    // 缓存未命中，显示loading并调用接口
    loading.value = true;
    try {
        console.log('缓存未命中，调用接口');
        const res = await scholarSearch(params);

        if (res.code == HTTP_STATUS.ACADEMIC_SEARCH_COUNT_INSUFFICIENT) {
            // message.warning(res.message || '学术搜索次数不足');
            // 通过store打开兑换模态弹窗
            const exchangeSearchStore = useExchangeSearchStore()
            exchangeSearchStore.openExchangeModal()
            return
        }

        if (!res.ok) {
            return;
        }

        const responseData = res.data;
        cholarList.value = responseData?.records || [];

        Object.assign(page, {
            current: responseData?.current,
            total: responseData?.total,
            pageSize: responseData?.size,
            pages: responseData?.pages,
        });

        // 将结果存入缓存
        try {
            await cacheManager.setCache(params, responseData);
            console.log('数据已缓存');
        } catch (cacheError) {
            console.warn('缓存存储失败:', cacheError);
        }

        // 搜索完成后重新加载一下用户信息
        await UserService.loadKnowledgeAssistantMemberInfo()
        const max = knowledgeAssistantMemberInfo.value?.maxScholarSearch || 0;
        const used = knowledgeAssistantMemberInfo.value?.usedScholarSearch || 0;
        const remaining = max - used;
        if (remaining < 10) {
            message.warning(`剩余学术搜索：${remaining} 次`);
        }

    } catch (error) {
        console.error('搜索失败:', error);
    } finally {
        loading.value = false;
    }
};




// 处理筛选条件变化
const handleFilterChange = () => {
    // 重置页码
    page.current = 1;
    // 重新搜索
    getScholar();
};

// 选择时间范围
const selectTimeRange = (timeValue: string) => {
    filters.timeRange = timeValue;
    // 如果选择时间不限，重置时间参数
    if (timeValue === '') {
        filters.as_ylo = 0;
        filters.as_yhi = 0;
    }
    // 关闭自定义范围选择器
    showCustomRange.value = false;
    handleFilterChange();
};

// 处理移动端筛选弹窗开关
const handleMobileFilterOpenChange = (open: boolean) => {
    showMobileFilter.value = open;
};

// 切换自定义范围显示
const toggleCustomRange = () => {
    // 选中自定义范围选项
    filters.timeRange = 'custom';
    // 初始化临时年份值
    if (!tempStartYear.value && filters.as_ylo) {
        tempStartYear.value = filters.as_ylo;
    }
    if (!tempEndYear.value && filters.as_yhi) {
        tempEndYear.value = filters.as_yhi;
    }
    showCustomRange.value = !showCustomRange.value;
};

// 切换年份选择器显示
const toggleYearPicker = () => {
    showCustomRange.value = !showCustomRange.value;
};

const addCustomRange = () => {
    console.log('showCustomRange before', showCustomRange.value);
    console.log('yearOptions', yearOptions.value);
    showCustomRange.value = true;
    console.log('showCustomRange after', showCustomRange.value);

    // 延迟检查状态，看是否被 handleClickOutside 重置了
    setTimeout(() => {
        console.log('showCustomRange after timeout', showCustomRange.value);
    }, 100);
};

// 处理开始年份输入
const handleStartYearInput = (event: Event) => {
    const target = event.target as HTMLInputElement;
    const value = target.value;
    if (value === '') {
        filters.as_ylo = 0;
    } else {
        const year = parseInt(value);
        if (!isNaN(year) && year >= 1990 && year <= new Date().getFullYear() + 5) {
            filters.as_ylo = year;
            tempStartYear.value = year;
        }
    }
};

// 处理开始年份失焦
const handleStartYearBlur = () => {
    if (filters.as_ylo && filters.as_yhi) {
        filters.timeRange = 'custom';
        handleFilterChange();
    }
};

// 处理开始年份获取焦点
const handleStartYearFocus = () => {
    // 确保选中自定义范围
    filters.timeRange = 'custom';
};

// 处理结束年份输入
const handleEndYearInput = (event: Event) => {
    const target = event.target as HTMLInputElement;
    const value = target.value;
    if (value === '') {
        filters.as_yhi = 0;
    } else {
        const year = parseInt(value);
        if (!isNaN(year) && year >= 1990 && year <= new Date().getFullYear() + 5) {
            filters.as_yhi = year;
            tempEndYear.value = year;
        }
    }
};

// 处理结束年份失焦
const handleEndYearBlur = () => {
    if (filters.as_ylo && filters.as_yhi) {
        filters.timeRange = 'custom';
        handleFilterChange();
    }
};

// 处理结束年份获取焦点
const handleEndYearFocus = () => {
    // 确保选中自定义范围
    filters.timeRange = 'custom';
};

// 选择文章类型
const selectArticleType = (typeValue: number) => {
    filters.as_rr = typeValue;
    handleFilterChange();
};

// 选择排序类型
const selectSortType = (typeValue: number) => {
    filters.scisbd = typeValue;
    handleFilterChange();
};



// 清除自定义时间范围
const clearCustomRange = () => {
    tempStartYear.value = null;
    tempEndYear.value = null;
    filters.as_ylo = 0;
    filters.as_yhi = 0;
    filters.timeRange = '';
    showCustomRange.value = false;
    handleFilterChange();
};

// 选择结束年份
const selectEndYear = (year: number) => {
    // 如果开始年份没有选择，自动将结束年份赋值给开始年份
    if (!tempStartYear.value) {
        tempStartYear.value = year;
    }
    tempEndYear.value = year;
};

// 确定自定义时间范围
const confirmCustomRange = () => {
    if (tempStartYear.value && tempEndYear.value) {
        // 确保开始年份不大于结束年份
        if (tempStartYear.value > tempEndYear.value) {
            const temp = tempStartYear.value;
            tempStartYear.value = tempEndYear.value;
            tempEndYear.value = temp;
        }

        filters.as_ylo = tempStartYear.value;
        filters.as_yhi = tempEndYear.value;
        filters.timeRange = 'custom';
        showCustomRange.value = false;
        handleFilterChange();
    }
};

// 点击外部关闭自定义范围选择器
const handleClickOutside = (event: Event) => {
    const target = event.target as Node;

    // 检查是否点击了浮窗内部
    if (customRangeRef.value && customRangeRef.value.contains(target)) {
        return;
    }

    // 检查是否点击了输入框区域（通过查找父元素）
    let element = target as Element;
    while (element) {
        if (element.classList && (
            element.classList.contains('year-input') ||
            element.tagName === 'INPUT' ||
            element.getAttribute('data-year-picker') === 'true'
        )) {
            return;
        }
        element = element.parentElement as Element;
    }

    // 如果都不是，则关闭浮窗
    showCustomRange.value = false;
};



// 跳转到指定页面
const goToPage = (pageNum: number) => {
    if (pageNum >= 1 && pageNum <= page.pages && pageNum !== page.current) {
        page.current = pageNum;
        getScholar();
    }
};



// 执行搜索的方法（供父组件调用）
const performSearch = (query: string) => {
    if (query.trim()) {
        page.current = 1;
        getScholar(query);
    }
};

// 移除自动搜索的监听器，改为手动触发搜索

// 清理缓存的函数
const clearCache = async () => {
    try {
        await cacheManager.clearAllCache();
        console.log('缓存已清理');
    } catch (error) {
        console.warn('缓存清理失败:', error);
    }
};

// 生命周期钩子
onMounted(async () => {
    // 添加点击外部事件监听器
    document.addEventListener('click', handleClickOutside);

    // 页面加载时清理缓存
    await clearCache();

    // 监听页面刷新和关闭事件
    const handleBeforeUnload = () => {
        // 页面刷新或关闭时清理缓存（同步操作）
        navigator.sendBeacon && navigator.sendBeacon('/api/clear-cache'); // 可选的服务端通知
    };

    // 监听页面可见性变化（切换标签页）
    const handleVisibilityChange = async () => {
        if (document.hidden) {
            // 页面隐藏时清理缓存
            await clearCache();
        }
    };

    // 监听路由变化
    const router = useRouter();
    const route = useRoute();
    const currentPath = route.path;

    const unwatch = router.beforeEach(async (to, from) => {
        // 如果离开当前页面，清理缓存
        if (from.path === currentPath && to.path !== currentPath) {
            await clearCache();
        }
    });

    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // 存储事件监听器引用以便清理
    (window as any).__scholarCacheCleanup = {
        handleBeforeUnload,
        handleVisibilityChange,
        unwatch
    };
});

onUnmounted(async () => {
    // 移除事件监听器
    document.removeEventListener('click', handleClickOutside);

    // 组件卸载时清理缓存
    await clearCache();

    // 清理页面事件监听器
    const cleanup = (window as any).__scholarCacheCleanup;
    if (cleanup) {
        window.removeEventListener('beforeunload', cleanup.handleBeforeUnload);
        document.removeEventListener('visibilitychange', cleanup.handleVisibilityChange);
        // 清理路由监听器
        if (cleanup.unwatch) {
            cleanup.unwatch();
        }
        delete (window as any).__scholarCacheCleanup;
    }
});

// 暴露给父组件的方法和数据
defineExpose({
    page,
    filters,
    performSearch,
    getScholar
});


</script>

<style scoped>
.custom-scrollbar::-webkit-scrollbar {
    width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Firefox */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db transparent;
}

/* 隐藏数字输入框的上下箭头 */
.no-spinner::-webkit-outer-spin-button,
.no-spinner::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.no-spinner[type=number] {
    -moz-appearance: textfield;
    appearance: textfield;
}

/* 年份输入框样式 */
.year-input {
    color: #9ca3af !important;
    /* text-gray-400 */
    text-align: center;
}

.year-input::placeholder {
    color: #9ca3af !important;
    /* text-gray-400 */
    text-align: center;
}
</style>
