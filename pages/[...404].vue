<!-- src/views/NotFoundPage.vue -->
<template>
    <div class="not-found">
        <img src="//static-1256600262.file.myqcloud.com/xiaoin-h5/image/page-404-1.png" alt="" />
        <h1>您访问的页面走丢了</h1>
        <p>即将离开本页，{{ countdown }}秒后自动返回首页</p>
        <router-link to="/"><button
                class="px-4 py-2 text-blue-700 transition-colors rounded-lg bg-blue-50 hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap">返回首页</button></router-link>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

const countdown = ref(3) // 倒计时初始值为 3 秒
const router = useRouter()
const store = useUserStore()

const startCountdown = () => {
    const interval = setInterval(() => {
        countdown.value -= 1
        if (countdown.value === 0) {
            clearInterval(interval)
            router.push('/') // 跳转到首页
            store.setChannel(`comcn-home`)
        }
    }, 1000)
}
onMounted(() => {
    startCountdown()
})
definePageMeta({
    layout: 'empty'
})
</script>

<style lang="scss" scoped>
.not-found {
    text-align: center;
    padding: 50px;

    img {
        width: 792px;
        height: 526px;
    }
}

.not-found h1 {
    color: #333;
}

.not-found p {
    margin: 30px 0;
    font-size: 14px;
}
</style>