<template>
  <div class="flex w-full h-screen">
    <!---升级中的显示的组件-->
    <client-only>
      <template v-if="upgradeIng">
        <submission-close :keyword="_code" />
      </template>

    </client-only>

    <!-- 根据状态显示不同组件 -->
    <template v-if="currentSubmission && creatorData && !upgradeIng">
      <!-- 待支付状态 -->
      <ConfirmComponent v-if="currentSubmission.status === SubmissionStatus.needPay" @pay-success="handlePaySuccess" />

      <!-- 写作中状态 -->
      <WritingComponent
        v-else-if="currentSubmission.status === SubmissionStatus.ing || currentSubmission.status === SubmissionStatus.payed || currentSubmission.status === SubmissionStatus.init || currentSubmission.status === SubmissionStatus.wait || (currentSubmission.status === SubmissionStatus.done && currentSubmission.creatorCode == 'insight_chart')"
        @creation-complete="handleCreationComplete" />

      <!-- 写作完成状态 -->
      <ResultComponent
        v-else-if="(currentSubmission.status === SubmissionStatus.done || currentSubmission.status === SubmissionStatus.error) && currentSubmission.creatorCode !== 'online_editing'" />

      <!-- (currentSubmission.status === SubmissionStatus.done || currentSubmission.status === SubmissionStatus.error) && currentSubmission.creatorCode !== 'online_editing' -->
      <!-- 在线编辑完成状态 -->
      <ResultEditingComponent
        v-else-if="(currentSubmission.status === SubmissionStatus.done || currentSubmission.status === SubmissionStatus.error) && currentSubmission.creatorCode === 'online_editing'" />

    </template>

    <!-- Loading状态 -->
    <div v-else class="flex items-center justify-center flex-1">
      <client-only>
        <template v-if="!upgradeIng">
          <!-- <div class="w-8 h-8 border-b-2 border-blue-500 rounded-full animate-spin"></div> -->
          <a-spin :spinning="true" class="text-gray-600"></a-spin>
        </template>
      </client-only>
    </div>

  </div>
</template>

<script setup lang="ts">
import ConfirmComponent from '@/components/Create/Detail/ConfirmComponent.vue'
import ResultComponent from '@/components/Create/Detail/ResultComponent.vue'
import WritingComponent from '@/components/Create/Detail/WritingComponent.vue'
import SubmissionClose from '@/components/Create/component/SubmissionClose.vue'
import { computed, onBeforeUnmount, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getCreateSubmissionResult } from '~/api/create'
import { useApp } from '~/composables/useApp'
import type { SubmissionPaperAnswerInfo } from '~/services/types/submission'
import { UserService } from '~/services/user'
import { useSubmissionStore } from '~/stores/submission'
import { CREATE_CODE, SubmissionStatus } from '~/utils/constants'

const app = useApp()
const route = useRoute()
const router = useRouter()
const submissionStore = useSubmissionStore()
const _code = route.query.code ? route.query.code.toString() : '';
let timerId: NodeJS.Timeout | undefined = undefined
// 升级中
const upgradeIng = ref(false)

// 使用 useAsyncData 预加载数据
const { data: preloadData } = await useAsyncData(
  `submission-${route.query.id}`,
  async () => {
    if (!route.query.id) {
      return null
    }

    // 如果 store 中已有匹配的数据，直接返回
    if (submissionStore.currentSubmission?.id === route.query.id) {
      return {
        submission: submissionStore.currentSubmission,
        creator: submissionStore.currentCreator
      }
    }

    let success = false
    // 如果有 code，直接加载完整数据
    if (route.query.code) {
      success = await submissionStore.loadSubmissionData(
        route.query.code as string,
        route.query.id as string,
        (msg: string) => {
          if (msg && msg.indexOf('应用升级中，') > -1) {
            upgradeIng.value = true
          }
        }
      )
    } else {
      // 只有 id 时，通过 id 加载数据
      success = await submissionStore.loadSubmissionById(route.query.id as string)
    }

    if (!success) {
      // 加载失败时重定向到 create 页面
      if (!upgradeIng.value) {
        router.replace('/create')
      }
      return null
    }

    return {
      submission: submissionStore.currentSubmission,
      creator: submissionStore.currentCreator,
    }
  },
  {
    watch: [() => route.query.id, () => route.query.code]
  }
)

// 计算属性获取当前数据
const currentSubmission = computed(() =>
  submissionStore.currentSubmission || preloadData.value?.submission || null
)
const creatorData = computed(() =>
  submissionStore.currentCreator || preloadData.value?.creator || null
)

// 添加 computed 属性
const submissionStatus = computed(() => submissionStore.currentSubmission?.status)
// 如果是ppt，判断ansers是否为空, version=2则去跳转去ppt编辑页面
const toPPTEditor = (answer: string) => {
  if (!answer) {
    return
  }
  try {
    const answerInfo: SubmissionPaperAnswerInfo = JSON.parse(answer)
    if (answerInfo?.version == '2') {
      setTimeout(() => {
        window.location.href = `${StarloveUtil.getInSlidesBaseUrl()}/?id=${currentSubmission.value?.id}`
      }, 300);
    }
  } catch (error) {
    console.error(error)
  }
}

// 如果是智能图表 跳转到 编辑器
const toInsightChart = (answer: string) => {
  if (!answer) {
    return
  }
  // 如果状态是错误，不允许跳转
  if (currentSubmission.value?.status === SubmissionStatus.error) {
    return
  }
  try {
    let aiEditorUrl = `${StarloveUtil.getAIEditorBaseUrl()}/?id=${currentSubmission.value?.id}`
    if (app.value?.isDesktop) {
      aiEditorUrl += `&t=${UserService.getToken()}`
      // return
    }
    window.location.href = aiEditorUrl
  } catch (error) {
    console.error(error)
  }
}

// 修改 loadSubmissionDetail 函数
const loadSubmissionDetail = async () => {
  try {
    clearTimeoutData()
    if (!route.query.id) {
      return
    }

    const res = await getCreateSubmissionResult({ submissionId: route.query.id as string })
    if (!res.ok || !res.data) {
      const msg = res.message || ''
      if (msg && msg.indexOf('应用升级中，') > -1) {
        upgradeIng.value = true
      }
      // message.error(res.message || '结果查询失败')
      timerId = setTimeout(loadSubmissionDetail, 3000)
      router.replace('/create')
      return
    }

    // 更新 store 中的数据
    submissionStore.updateSubmission(res.data)

    // 如果写作者数据还没有加载，尝试加载
    if (!submissionStore.currentCreator && res.data.creatorCode) {
      await submissionStore.loadSubmissionById(route.query.id as string)
    }
    if (res.data.creatorCode == CREATE_CODE.PPT) {
      toPPTEditor(res.data.answer)
    }
    if (res.data.creatorCode == 'insight_chart') {
      toInsightChart(res.data.answer)
    }
    if (res.data.status === SubmissionStatus.ing || res.data.status === SubmissionStatus.payed || res.data.status === SubmissionStatus.wait || res.data.status === SubmissionStatus.init) {
      timerId = setTimeout(loadSubmissionDetail, 5000)
    }
  } catch (error) {
    console.error('Load submission detail error:', error)
    timerId = setTimeout(loadSubmissionDetail, 3000)
  }
}

// 处理支付成功
const handlePaySuccess = () => {
  loadSubmissionDetail()
}

// 处理写作完成
const handleCreationComplete = () => {
  loadSubmissionDetail()
}

onMounted(async () => {
  // console.log("currentSubmission ==》", currentSubmission.value)
  // 如果数据加载失败，重新初始化
  if (!currentSubmission.value || !creatorData.value) {
    if (route.query.code) {
      await submissionStore.loadSubmissionData(
        route.query.code as string,
        route.query.id as string,
        (msg: string) => {
          if (msg && msg.indexOf('应用升级中，') > -1) {
            upgradeIng.value = true
          }
        }
      )
    } else if (route.query.id) {
      await submissionStore.loadSubmissionById(route.query.id as string)
    }
  }

  // 如果状态是进行中，启动轮询
  if (currentSubmission.value?.status === SubmissionStatus.ing ||
    currentSubmission.value?.status === SubmissionStatus.payed ||
    currentSubmission.value?.status === SubmissionStatus.wait ||
    currentSubmission.value?.status === SubmissionStatus.init) {
    loadSubmissionDetail()
  } else {
    //如果是ppt, 生成完成后，直接去ppt页面
    if (currentSubmission.value?.status === SubmissionStatus.done && currentSubmission.value?.id && currentSubmission.value.creatorCode == CREATE_CODE.PPT && !route.query.debug) {
      toPPTEditor(currentSubmission.value?.answer)
    }

    if (currentSubmission.value?.status === SubmissionStatus.done && currentSubmission.value?.id && currentSubmission.value.creatorCode == 'insight_chart' && !route.query.debug) {
      toInsightChart(currentSubmission.value?.answer)
    }
  }
})

const clearTimeoutData = () => {
  if (timerId) {
    clearTimeout(timerId)
  }
}

// 组件卸载时清理
onBeforeUnmount(() => {
  clearTimeoutData()
  // 可选：清理store数据
  // submissionStore.clearData()
})
</script>