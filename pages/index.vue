<template>
  <div class="min-h-screen min-h-[100dvh] bg-gradient-to-br from-blue-50/80 via-blue-50/20 to-green-50/20">
    <!-- 顶部轮播广告 -->
    <div v-if="isOpenTopBar"
      class="fixed top-0 left-0 right-0 z-10 py-2 sm:py-4 bg-purple-50 lg:pl-[180px] flex items-center justify-center">
      <TopBar></TopBar>
      <div class="absolute top-0 w-10 h-full leading-none text-center right-1 " @click="handleCloseTopBanner">
        <close class="px-1.5 py-1.5 lne-he cursor-pointer" :size="14" fill="#AAAAAA" />
      </div>
    </div>
    <!-- 主体部分 - 完美居中 -->
    <div class="min-h-[calc(100vh-120px)] grid place-items-center px-4 pb-8 pt-12 sm:pt-16">
      <!-- 主内容容器 -->
      <div class="w-full max-w-[1216px] mx-auto">
        <div class="w-full">
          <!-- 主内容区域 -->
          <div class="p-4 rounded-lg">
            <div
              class="mt-3 mb-6 text-2xl font-bold text-center text-transparent md:mt-1 md:text-3xl bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text">
              AI驱动，知识无界
            </div>
            <Ask />
            <PopularCreations1 class="mt-6" :data="data?.result" />
          </div>
        </div>
      </div>
    </div>

    <!-- 底部区域 - 固定在底部 -->
    <Footer :class="[
      'left-0 right-0 z-20 lg:pl-[180px]',
      // 小屏幕非fixed，大屏幕fixed
      ' lg:fixed',
      'sm:static sm:pl-0',
      'bottom-0'
    ]" />

  </div>
</template>

<script lang="ts" setup>
import { Close } from '@icon-park/vue-next';
import { computed, onMounted, } from 'vue';
import { apiHomePath } from '~/api/home';
import Ask from '~/components/index/Ask.vue';
import Footer from '~/components/index/Footer.vue';
import PopularCreations1 from '~/components/index/PopularCreationsV1.vue';
import TopBar from '~/components/index/TopBar.vue';
import { getPlatformNew } from '~/composables/useApp';
import { useHomeStore } from '~/stores/home';

interface HomeResponse {
  success: boolean;
  code: number | null;
  result: Record<string, any>;
  message: string;
}

const { isOpenTopBar } = storeToRefs(useHomeStore())
// 是否显示顶部广告
const handleCloseTopBanner = () => {
  isOpenTopBar.value = false;
};
// const config = useRuntimeConfig()
const { public: { apiBase } } = useRuntimeConfig()
const { data } = useAsyncData<HomeResponse>('fetchData', async () => {
  try {
    const controller = new AbortController()
    const timeout = setTimeout(() => controller.abort(), 8000) // 8秒超时

    const res = await $fetch<HomeResponse>(
      `${apiBase}${apiHomePath}?platform=${getPlatformNew()}`,
      { signal: controller.signal }
    )
    clearTimeout(timeout)
    return res
  } catch (error) {
    console.error('获取首页数据失败:', error)
    // 可以根据需要处理错误，比如上报或提示
    return { success: false, code: null, result: {}, message: (error as Error).message || '请求失败' }
  }
}, {
  server: true, // 是否在服务端运行
  lazy: false,  // 延迟加载 (适合在用户操作后加载)
  immediate: true, // 是否在组件加载时立即执行
  default: () => ({ success: false, code: null, result: {}, message: '' })
})
// const homeData = computed(() => asyncData.value?.result || {})


useHead({
  title: '万能小in - 我的AI外脑',
  meta: [
    {
      name: 'description',
      content: '万能小in是一款AI知识助手应用，在行业内首创了基于个人知识库的文档学习、检索提问、长文写作、AI写书等产品服务，致力于让每个人拥有自己的AI 外脑。不止是工具,更是AI学习伙伴!',
      tagPriority: 'critical', // 提高优先级
    },
    {
      name: 'keywords',
      content: '万能小in,小in,人工智能,AI工具,智能写作,AI伙伴,长文写作,万能小in官网入口',
      tagPriority: 'critical', // 提高优先级
    }
  ],
  script: [
    {
      type: 'application/ld+json',
      tagPriority: 'critical', // 提高优先级
      children: JSON.stringify({
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "万能小in",
        "url": "https://xiaoin.com.cn/",
        "description": "万能小in是一款AI知识助手应用，在行业内首创了基于个人知识库的文档学习、检索提问、长文写作、AI写书等产品服务，致力于让每个人拥有自己的AI 外脑。",
        "potentialAction": {
          "@type": "SearchAction",
          "target": {
            "@type": "EntryPoint",
            "urlTemplate": "https://xiaoin.com.cn/create"
          },
          "query-input": "required name=q"
        },
        "publisher": {
          "@type": "Organization",
          "name": "万能小in logo",
          "logo": {
            "@type": "ImageObject",
            "url": "https://static.xiaoin.cn/prod/logo.png",
            "width": 40,
            "height": 40
          }
        }
      }, null, 2)
    }
  ]
})


// console.log('Current API Base:', config.public.apiBase)

const { data: asyncData } = useAsyncData<HomeResponse>('fetchData', async () => {
  try {
    const controller = new AbortController()
    const timeout = setTimeout(() => controller.abort(), 8000) // 8秒超时

    const res = await $fetch<HomeResponse>(
      `${apiBase}${apiHomePath}?platform=${getPlatformNew()}`,
      { signal: controller.signal }
    )
    clearTimeout(timeout)
    return res
  } catch (error) {
    console.error('获取首页数据失败:', error)
    // 可以根据需要处理错误，比如上报或提示
    return { success: false, code: null, result: {}, message: (error as Error).message || '请求失败' }
  }
}, {
  server: true, // 是否在服务端运行
  lazy: false,  // 延迟加载 (适合在用户操作后加载)
  immediate: true, // 是否在组件加载时立即执行
  default: () => ({ success: false, code: null, result: {}, message: '' })
})
const homeData = computed(() => asyncData.value?.result || {})

onMounted(async () => {
  // const res = await getHomeData({})
  // console.log(res, 'res')
  // if (res.ok) {
  //   homeData.value = res.data
  // }
})
</script>