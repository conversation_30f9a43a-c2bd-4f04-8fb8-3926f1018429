<template>
  <div class="flex flex-col h-screen lg:flex-row" v-if="!isError">
    <!-- 左侧区域：包含顶部导航和PDF预览 -->
    <div class="flex flex-col w-full lg:w-1/2 h-[60vh] lg:h-screen">
      <!-- 顶部导航区域 -->
      <div class="p-2 bg-white border-b border-gray-200 lg:p-4">
        <div class="flex items-start">
          <button @click="router.back()"
            class="mr-2 lg:mr-4 p-1.5 lg:p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <left-small theme="outline" size="24" fill="#666" />
          </button>

          <div class="flex-1 min-w-0">
            <!-- 标题区域 -->
            <div class="flex items-center gap-2 mb-1 lg:mb-2">
              <h1
                class="text-base font-bold text-transparent bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text line-clamp-2">
                {{ filename }}
              </h1>
              <button v-if="knowledgeDetail" @click="isEditTitleOpen = true"
                class="flex-shrink-0 p-1 lg:p-1.5 hover:bg-gray-100 rounded-lg transition-colors">
                <edit theme="outline" size="18" fill="#666" />
              </button>
            </div>

            <!-- 文件信息区域 -->
            <div class="flex items-center text-xs text-gray-500 gap-x-2 lg:gap-x-3 lg:text-sm">
              <span class="flex items-center gap-1">
                <time theme="outline" size="16" />
                {{ knowledgeDetail?.createTime }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- PDF预览区域 -->
      <div class="flex-1 overflow-hidden" v-if="knowledgeDetail?.id">
        <client-only>
          <FilePreview :url="getUrl || ''" :suffix="fileSuffix" :original-url="knowledgeDetail?.fileUrl || ''"
            :title="knowledgeDetail?.fileName || ''" :page-num="Number(pageNum) || 1" :meta="String(queryMeta || '')" />
        </client-only>
      </div>
    </div>

    <!-- 右侧功能区域 -->
    <client-only>
      <div class="flex flex-col w-full border-t border-gray-200 lg:w-1/2 lg:border-t-0 lg:border-l">
        <!-- Tab导航 -->
        <div class="flex border-b border-gray-200">
          <div class="flex flex-col justify-end flex-1">
            <div class="flex mb-0 space-x-2 overflow-x-auto lg:space-x-4">
              <button v-for="tab in tabs" :key="tab.id" @click="handleTabChange(tab)" :class="[
                'px-3 lg:px-4 py-2 text-sm lg:text-base font-medium transition-colors whitespace-nowrap',
                currentTab === tab.id
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 border-b-2 border-transparent hover:text-gray-700',
              ]">
                {{ tab.name }}
              </button>
            </div>
          </div>

          <div class="flex items-center px-2 py-2 lg:px-4">
            <user-avatar />
          </div>
        </div>

        <!-- Tab内容区域 -->
        <div class="flex-1 overflow-auto">
          <div v-if="knowledgeDetail?.id" v-for="tab in tabs" :key="tab.id" :class="[
            tab.isVisible ? 'h-full' : 'hidden',
          ]">
            <component v-if="tab.isLoaded" :is="tab.component" :knowledgeDetail="knowledgeDetail" :key="tab.id">
            </component>
          </div>
        </div>
      </div>
    </client-only>

    <!-- 添加标题编辑弹窗 -->
    <UModal v-model="isEditTitleOpen">
      <div class="p-4 lg:p-6">
        <h3 class="mb-3 text-base font-medium lg:text-lg lg:mb-4">修改文档标题</h3>
        <UInput v-model="newFilename" placeholder="请输入新标题" class="mb-4">
          <template #trailing>
            <span class="text-xs text-gray-500 dark:text-gray-400">{{ suffixName }}</span>
          </template>
        </UInput>
        <div class="flex justify-end gap-2">
          <UButton color="gray" variant="soft" label="取消" @click="isEditTitleOpen = false" />
          <UButton color="primary" label="确认" @click="updateTitle()" />
        </div>
      </div>
    </UModal>
  </div>
  <div v-else class="flex items-center justify-center h-screen lg:flex-row">
    <div class="text-center">
      <a-result status="error" title="提问原文件已删除" sub-title="请尝试查看其他提问记录">
        <template #extra>
          <a-button type="primary" @click="goBack()">返回提问记录</a-button>
        </template>
      </a-result>
    </div>
  </div>
</template>

<script setup lang="ts">
import { nameUpdate } from '@/api/repositoryFile';
import { useUserStore } from '@/stores/user';
import { Edit, LeftSmall } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { computed, defineAsyncComponent, markRaw, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';



// 导入组件
import { repositoryFileGetDetail } from '@/api/repositoryFile';
import UserAvatar from '@/components/Auth/UserAvatar.vue';
import { type RepositoryFile } from '@/services/types/repositoryFile';
const FilePreview = markRaw(defineAsyncComponent(() =>
  import('~/components/Library/FilePreview.vue')
))
const LibraryPPt = markRaw(defineAsyncComponent(() =>
  import('~/components/Library/LibraryPPt.vue')
))
const NotesContent = markRaw(defineAsyncComponent(() =>
  import('~/components/Library/NotesContent.vue')
))
const QuestionsContent = markRaw(defineAsyncComponent(() =>
  import('~/components/Library/QuestionsContent.vue')
))
const TranslationContent = markRaw(defineAsyncComponent(() =>
  import('~/components/Library/TranslationContent.vue')
))
const MindmapContent = markRaw(defineAsyncComponent(() =>
  import('~/components/Library/MindmapContent.vue')
))



const router = useRouter();
const route = useRoute();
const type = route.query.type || 'questions'
const currentTab = ref(type)
const zoom = ref(1)
const pdfUrl = ref<string | undefined>('')
const user = useUserStore()


const avatar = computed(() => {
  return user?.currentLoginInfo?.avatar || defaultAvatar
})

const getUrl = computed(() => {
  return knowledgeDetail.value?.parsedUrl || knowledgeDetail.value?.fileUrl
})

const fileSuffix = computed(() => {
  const url = knowledgeDetail.value?.parsedUrl || knowledgeDetail.value?.fileUrl
  if (!url) {
    return ''
  }

  // 去掉查询字符串
  const cleanUrl = url.split('?')[0]
  return cleanUrl.substring(cleanUrl.lastIndexOf('.') + 1).toLowerCase()
})

const pdfPageTotals = ref()


const onloadPdf = (params: any) => {
  pdfPageTotals.value = params.pageTotals
}
const pageNum = computed(() => Number(router.currentRoute.value.query.pageNum) || 1)
const queryMeta = computed(() => String(router.currentRoute.value.query.meta || ''))

const title = computed(() => {
  if (knowledgeDetail.value?.processData?.title) {
    return knowledgeDetail.value.processData.title
  }
  if (knowledgeDetail.value?.fileName) {
    return knowledgeDetail.value.fileName
  }
  return '无标题'
})



const tabsList = [
  { id: 'questions', name: '提问', isLoaded: false, isVisible: false, component: QuestionsContent },
  { id: 'mindmap', name: '思维导图', isLoaded: false, isVisible: false, component: MindmapContent },
  { id: 'translation', name: '翻译', isLoaded: false, isVisible: false, component: TranslationContent },
  //笔记需要预加载
  { id: 'notes', name: '笔记', isLoaded: true, isVisible: false, component: NotesContent },
  { id: 'PPT', name: 'PPT', isLoaded: false, isVisible: false, component: LibraryPPt },
]

// 设置初始状态
tabsList.forEach(d => {
  if (d.id == type) {
    d.isVisible = true
    d.isLoaded = true
  }
})

const tabs = computed(() => tabsList.filter(tab => tab.id !== 'PPT' || shouldShowPPT.value))

const handleTabChange = (_tab: any) => {
  currentTab.value = _tab.id
  tabsList.forEach(d => {
    if (d.id == _tab.id) {
      d.isVisible = true
      d.isLoaded = true
    } else {
      d.isVisible = false
    }
  })
}

// 添加新的响应式变量
const isEditTitleOpen = ref(false)

let spaceId = ''

// 添加更新标题的方法
const updateTitle = async () => {

  if (knowledgeDetail.value?.fileName.trim().length === 0) {
    return message.error('名字不能为空')
  }
  const res = await nameUpdate(spaceId, { fileId: knowledgeDetail.value?.fileId, fileName: `${newFilename.value.trim()}${suffixName.value}` })
  if (!res.ok) {
    // message.error(res.message || '修改失败')
    isEditTitleOpen.value = false
    return
  }
  message.success(res.message || '修改成功')
  // 直接更新当前的 knowledgeDetail，而不是重新获取整个数据
  if (knowledgeDetail.value) {
    knowledgeDetail.value.fileName = `${newFilename.value.trim()}${suffixName.value}`
  }

  isEditTitleOpen.value = false
}




const knowledgeDetail = ref<RepositoryFile | null>(null)
const isError = ref(false)
const goBack = () => {
  router.push('/profile/questions')
}
const setupData = async () => {
  const newsId = route.params.id as string;
  try {

    spaceId = user.knowledgeAssistantMemberInfo?.userId || ''

    const res = await repositoryFileGetDetail({ spaceId: spaceId, id: newsId });
    if (!res.ok) {
      isError.value = true;
      console.log('查询失败');
      return;
    }
    if (res.data) {
      knowledgeDetail.value = res.data as RepositoryFile;
    }
  } catch (err) {
    console.error('获取新闻数据失败:', err);
  }
};


const newFilename = ref<string>('') // 初始化为当前的文件名


const filename = computed(() => {
  if (!knowledgeDetail.value?.fileName) {
    return '';
  }
  const lastDotIndex = knowledgeDetail.value.fileName.lastIndexOf('.');
  if (lastDotIndex !== -1 && lastDotIndex < knowledgeDetail.value.fileName.length - 1) {
    const name = knowledgeDetail.value.fileName.substring(0, lastDotIndex);
    newFilename.value = name;
    return name;
  }
  newFilename.value = knowledgeDetail.value.fileName;
  return knowledgeDetail.value.fileName;
});

const suffixName = computed(() => {
  if (!knowledgeDetail.value?.fileName) {
    return '';
  }
  const lastDotIndex = knowledgeDetail.value.fileName.lastIndexOf('.');
  if (lastDotIndex !== -1 && lastDotIndex < knowledgeDetail.value.fileName.length - 1) {
    // 返回带点的后缀名
    return knowledgeDetail.value.fileName.substring(lastDotIndex).toLowerCase();
  }
  return '';
});

// 判断PPT按钮是否显示的计算属性
const shouldShowPPT = computed(() => {
  if (!knowledgeDetail.value) {
    return false;
  }

  // 检查字数是否大于等于200
  const wordCountValid = (knowledgeDetail.value.wordCount || 0) >= 200;

  // 检查文件后缀是否为pdf或docx
  const suffix = suffixName.value;
  const suffixValid = suffix === '.pdf' || suffix === '.docx';

  return wordCountValid && suffixValid;
});

onMounted(() => {
  setupData()
})
</script>

<style scoped>
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
