<template>
    <div class="relative flex flex-row justify-between w-full h-screen m-0 bg-indigo-50">
        <!-- 移动端菜单按钮 -->
        <button @click="toggleLeftPanel" v-show="!isLeftPanelVisible || isSmallScreen"
            class="fixed z-50 flex items-center justify-center w-8 h-12 bg-gray-500 hover:bg-gray-600 shadow-lg top-20 rounded-r-lg transition-all duration-300"
            :class="isLeftPanelVisible ? 'left-80' : 'left-0'">
            <right v-if="!isLeftPanelVisible" theme="outline" size="20" fill="#fff" />
            <left v-else theme="outline" size="20" fill="#fff" />
        </button>

        <!-- 遮罩层 -->
        <transition name="fade">
            <div v-if="isLeftPanelVisible" @click="closeLeftPanel"
                class="fixed inset-0 z-30 bg-black bg-opacity-50 lg:hidden"></div>
        </transition>

        <!-- 左侧面板 -->
        <div class="bg-indigo-50 " :class="[
            'fixed lg:relative lg:translate-x-0 transition-transform duration-300 overflow-y-auto box-border z-40',
            'w-80 lg:w-1/2 h-full shadow-xl lg:shadow-none',
            isLeftPanelVisible ? 'translate-x-0' : '-translate-x-full'
        ]">
            <LibraryLeft v-model:current-folder-id="currentFolderId" v-model:example-list="exampleList"
                v-model:is-empty-document="isEmptyDocument" />
        </div>


        <!-- 右侧面板 -->
        <div class="box-border flex-1 w-full h-full overflow-y-auto transition-all duration-300 lg:w-1/2">
            <LibraryRight :current-folder-id="currentFolderId" :example-list="exampleList"
                :is-empty-document="isEmptyDocument" />
        </div>
    </div>
</template>

<script setup lang="ts">
import LibraryLeft from '@/components/Library/LibraryLeft.vue';
import LibraryRight from '@/components/Library/LibraryRight.vue';
import { Left, Right } from '@icon-park/vue-next';
import { onMounted, onUnmounted, ref } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute()

const currentFolderId = ref(typeof route.query.folderId === 'string' ? route.query.folderId : '')
const exampleList = ref([
    // "石昊是如何在修炼学院中快速掌握引气入体技巧的？",
    // "石昊的‘以身化种’修炼法具体是如何运作的？",
    // "石昊在修炼过程中是如何与自然和谐统一的？"
])
const isEmptyDocument = ref(false)

// 左侧面板显示状态 - 默认展开
const isLeftPanelVisible = ref(true)

// 屏幕尺寸状态
const isSmallScreen = ref(false)

// 切换左侧面板
const toggleLeftPanel = () => {
    isLeftPanelVisible.value = !isLeftPanelVisible.value
}

// 关闭左侧面板
const closeLeftPanel = () => {
    isLeftPanelVisible.value = false
}

// 监听窗口尺寸变化
onMounted(() => {
    const handleResize = () => {
        isSmallScreen.value = window.innerWidth < 1024
        if (window.innerWidth >= 1024) { // lg breakpoint
            isLeftPanelVisible.value = true
        }
    }

    window.addEventListener('resize', handleResize)
    handleResize() // 初始化时执行一次

    onUnmounted(() => {
        window.removeEventListener('resize', handleResize)
    })
})

useHead({
    title: '搭建你的AI外脑 - 万能小in',
    meta: [
        {
            name: 'description',
            content: '万能小in知识库，是你的AI数字外脑，可以帮你对文档、网页等资料，进行云存储以及深度学习，如AI 导读、思维导图、在线翻译、文档提问、记笔记等，让知识的阅读、学习、整理变得轻松高效，并可以在此基础上，实现基于个人知识库的提问和写作，更专业、更可靠。',
            tagPriority: 'critical',
        },
        {
            name: 'keywords',
            content: '知识库,个人知识库,智能知识库,AI知识库,外脑',
            tagPriority: 'critical',
        }
    ]
})

</script>

<style scoped>
/* 遮罩层淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
</style>