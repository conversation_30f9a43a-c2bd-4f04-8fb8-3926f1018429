<template>
  <div class="p-6 m-3 bg-white border border-gray-200 rounded-lg">
    <div class="space-y-3 sm:space-y-4">
      <div v-if="isLoading && !messageRecordList.length" class=" min-h-[200px]">
        <div class="flex items-center w-full">
          <div class="w-full space-y-4">
            <USkeleton class="w-full h-4" />
            <USkeleton class="w-3/4 h-4" />
            <USkeleton class="w-2/4 h-4" />
          </div>
        </div>
      </div>
      <EmptyState v-if="!isLoading && messageRecordList.length === 0" />
      <div v-for="item in messageRecordList" :key="item.id" @click.stop="handleChat(item.sessionId, item)"
        class="p-3 transition-all duration-200 bg-white border border-blue-100 rounded-lg cursor-pointer sm:p-4 hover:border-blue-300 hover:shadow-sm">
        <div class="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between sm:gap-4">
          <div class="flex items-center flex-1 min-w-0 gap-2">
            <div class="flex items-center text-sm text-gray-800 truncate">
              {{ item.title }}
              <span v-if="item?.sessionFrom" class="px-3 py-1 ml-2 text-xs text-blue-700 rounded-lg bg-blue-50 ">{{
                SessionFromName[`${item.sessionFrom}` as SessionFrom] }}</span>
              <div @click.stop="handleRename(item)" class="px-3 py-2 cursor-pointer ">
                <write size="13" />
              </div>
            </div>
          </div>
          <div class="flex items-center justify-between flex-shrink-0 gap-4 sm:justify-end sm:gap-6">
            <span class="text-sm text-gray-400">{{ item.createTime }}</span>
            <div class="flex items-center">
              <a-popconfirm title="你确定删除该条记录吗?" ok-text="确定" cancel-text="取消" @confirm="handleDelete(item.id)">
                <button
                  class="px-2 py-1 text-sm text-gray-400 transition-colors rounded-md hover:text-red-500 hover:bg-gray-50"
                  @click.stop>
                  删除
                </button>
              </a-popconfirm>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="pb-16 mt-4 sm:mt-6 md:pb-0">
      <Pagination v-if="reqParams.pages != 0" v-model:current="reqParams.pageNo" :total-pages="reqParams.pages"
        :total="reqParams.total" @change="handlePageChange">
      </Pagination>
    </div>
    <!-- 重命名和移动文件的弹窗 -->
    <RenameSessionMessageModal v-if="showRenameSessionMessageModal" v-model="showRenameSessionMessageModal"
      :item="currentRenameItem" @confirm="handleRenameConfirm" type="folder" />
  </div>
</template>

<script lang="ts" setup>
import EmptyState from '@/components/EmptyState.vue';
import { useChatStore } from '@/stores/chat';
import { Write } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { deleteMessageRecord, getMessageSessionList } from '~/api/appMessage';
import { getSubFolders, repositoryFileGetDetail } from '~/api/repositoryFile';
import Pagination from '~/components/Pagination.vue';
import type { AppMessageRecord } from '~/services/types/appMessage';
import { UserService } from '~/services/user';

const { $eventBus } = useNuxtApp();

definePageMeta({
  layout: 'profile'
})
const messageRecordList = ref<AppMessageRecord[]>([])

const handleDelete = async (id: string) => {
  const res = await deleteMessageRecord({ id: id })
  if (!res.ok) {
    return
  }
  await loadMessageRecordData()
}
const reqParams = reactive({
  pageNo: 1,
  pageSize: 10,
  pages: 0,
  total: 0,
  title: ''
})
const isLoading = ref(true)

const loadMessageRecordData = async () => {
  const params: any = {
    pageNo: reqParams.pageNo,
    pageSize: reqParams.pageSize,
    title: reqParams?.title || ''
  }

  isLoading.value = true;
  const res = await getMessageSessionList(params)
  isLoading.value = false
  if (!res.ok || !res.data) {
    message.error(res.message || '获取消息失败')
    return
  }
  messageRecordList.value = res.data?.records || []
  if (params?.title) {
    reqParams.title = params.title || ''
  }
  reqParams.pageNo = params.pageNo || 1
  reqParams.pages = res.data.pages || 0
  reqParams.total = res.data.total || 0
}
const handlePageChange = () => {
  loadMessageRecordData()
}
const router = useRouter()
const chat = useChatStore()
const handleChat = async (sessionId: string | undefined, item: AppMessageRecord) => {
  chat.setIsNewSessionId(false)
  if (item?.sessionFrom == SessionFrom.KnowledgeSingleFile) {
    // 跳转前先判断文件是存在
    if (item.fileIds) {
      const res = await repositoryFileGetDetail({ spaceId: UserService.getSelfUserId(), id: item.fileIds, silent: true })
      if (!res.success) {
        message.error('该提问的原文件已删除，无法打开提问记录')
        return
      }
    }

    router.push(`/library/${item.fileIds}?type=questions`)
    return
  } else if (item?.sessionFrom == SessionFrom.KnowledgeFolder) {
    if (item?.folderIds && !isNaN(Number(item?.folderIds))) {
      console.log('item?.folderIds', item?.folderIds)
      router.push(`/library?folderId=${item.folderIds}`)
      const res = await getSubFolders(UserService.getSelfUserId(), { folderId: item?.folderIds, silent: true })
      if (!res.success) {
        message.error('该提问的原文件夹已删除，无法打开提问记录')
        return
      }
    }
    router.push(`/library?folderId=${item.folderIds}`)
    return
  } else if (item?.sessionFrom == SessionFrom.KnowledgeFolderRoot) {
    router.push(`/library`)
    return
  }
  router.push(`/chat/${sessionId}`)
}
const handleSearch = (query: { keyword: string }) => {
  reqParams.title = query.keyword
  reqParams.pageNo = 1
  loadMessageRecordData()
}
const showRenameSessionMessageModal = ref(false)
const currentRenameItem = ref<any>(null)
const handleRename = (item: any) => {
  currentRenameItem.value = item
  showRenameSessionMessageModal.value = true
}
const handleRenameConfirm = async (item: { id: string, title: string }) => {
  messageRecordList.value.forEach(d => {
    if (d.id == `${item.id}`) {
      d.title = item.title
    }
  })
}

onMounted(() => {
  loadMessageRecordData()
  $eventBus.on(StarloveConstants.keyOfEventBus.myQuestionSearch, handleSearch)
})

onBeforeUnmount(() => {
  $eventBus.off(StarloveConstants.keyOfEventBus.myQuestionSearch, handleSearch);
});
</script>