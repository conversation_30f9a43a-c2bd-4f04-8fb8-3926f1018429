<template>
    <div
        class="h-screen bg-[#F5F7FF] overflow-hidden flex flex-col px-4 sm:px-8 md:px-12 lg:px-16 xl:px-[16%] pt-8 sm:pt-12 md:pt-16 lg:pt-[4%] space-y-6 sm:space-y-8 md:space-y-10">
        <h1 class="text-xl sm:text-2xl md:text-3xl text-[#2551B5] font-bold text-center">学术搜索</h1>
        <div
            class="w-full h-[60px] sm:h-[65px] md:h-[70px] lg:h-[75px] p-4 sm:p-5 md:p-6 flex items-center justify-between bg-white shadow-[0_1px_20px_0_#C7D6FE] rounded-xl border border-[#2551B5]">
            <input v-model="searchQuery" placeholder="搜索专业学术文献，一键学习使用" maxlength="300"
                class="flex-1 w-full h-full text-sm sm:text-base text-[#999999] outline-none ring-0 focus:outline-none focus:ring-0 focus:border-transparent"
                type="text" @keyup.enter="handleSearch" />
            <button class="relative bg-transparent group" @click="handleSearch">
                <!-- 发光边框效果 -->
                <div
                    class="absolute -inset-0.5 bg-transparent rounded-lg blur-sm group-hover:blur-md transition-all duration-300">
                </div>

                <!-- 按钮主体 -->
                <div class="relative flex items-center justify-center overflow-hidden leading-none transition-all duration-300 bg-transparent rounded-lg hover:from-blue-600 hover:to-indigo-700 "
                    :class="hasContent ? 'text-white' : 'disabled-chat-input text-gray-500 cursor-not-allowed'">
                    <template v-if="hasContent">
                        <svg t="1754883495618" class="transition-transform duration-300 icon group-hover:scale-110"
                            viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4045"
                            width="35" height="35" style="transform-origin: center;">
                            <path
                                d="M0 0.058511m234.043769 0l555.853951 0q234.043769 0 234.043769 234.043769l0 555.853951q0 234.043769-234.043769 234.043769l-555.853951 0q-234.043769 0-234.043769-234.043769l0-555.853951q0-234.043769 234.043769-234.043769Z"
                                fill="#2551B5" p-id="4046"></path>
                            <path
                                d="M777.259357 246.82841c11.702188 11.672933 15.680933 28.670362 10.444203 44.292783l-166.200332 498.513228a43.210331 43.210331 0 0 1-39.056054 29.548026 43.093309 43.093309 0 0 1-41.630535-25.715559l-95.519113-214.822925-214.764414-95.460602a43.093309 43.093309 0 0 1-25.686303-41.659791c0.877664-17.962859 12.521342-33.351237 29.548026-38.997543l498.513227-166.171076a43.181075 43.181075 0 0 1 44.351295 10.473459zM746.365579 277.400377L248.261928 443.571453l214.735158 95.402091c8.39632 3.7447 15.359122 10.005371 19.89372 17.904349l2.077139 4.095766 95.402091 214.58888L746.365579 277.400377z m-119.362322 122.990001a21.649049 21.649049 0 0 1 0 30.659733L545.965602 512.029255a21.649049 21.649049 0 1 1-30.659734-30.659733l81.0084-80.979144a21.649049 21.649049 0 0 1 30.630478 0z"
                                fill="#FFFFFF" p-id="4047"></path>
                        </svg>
                    </template>
                    <template v-else>
                        <svg t="1754882980017" class="transition-transform duration-300 icon group-hover:scale-110"
                            viewBox="0 0 1053 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3887"
                            width="35" height="35" style="transform-origin: center;">
                            <path
                                d="M1.55054 0.058511m234.043769 0l555.853951 0q234.043769 0 234.043769 234.043769l0 555.853951q0 234.043769-234.043769 234.043769l-555.853951 0q-234.043769 0-234.043769-234.043769l0-555.853951q0-234.043769 234.043769-234.043769Z"
                                fill="#C5C9D2" p-id="3888"></path>
                            <path
                                d="M778.809897 246.82841c11.702188 11.672933 15.680933 28.670362 10.444203 44.292783l-166.200332 498.513228a43.210331 43.210331 0 0 1-39.056054 29.548026 43.093309 43.093309 0 0 1-41.630535-25.715559l-95.519113-214.822925-214.764414-95.460602a43.093309 43.093309 0 0 1-25.686303-41.659791c0.877664-17.962859 12.521342-33.351237 29.548026-38.997543l498.513227-166.171076a43.181075 43.181075 0 0 1 44.351295 10.473459zM747.916119 277.400377L249.812468 443.571453l214.735158 95.402091c8.39632 3.7447 15.359122 10.005371 19.89372 17.904349l2.077139 4.095766 95.402091 214.58888L747.916119 277.400377z m-119.362322 122.990001a21.649049 21.649049 0 0 1 0 30.659733L547.516142 512.029255a21.649049 21.649049 0 1 1-30.659734-30.659733l81.0084-80.979144a21.649049 21.649049 0 0 1 30.630478 0z"
                                fill="#FFFFFF" p-id="3889"></path>
                        </svg>
                    </template>
                </div>
            </button>
        </div>


        <div class="flex items-center justify-center w-full space-x-2 text-sm md:text-base ">
            <Iconfont name="xueshuwenzhang" :size="15"></Iconfont>
            <span>1.海量学术资源搜索</span>
            <span>
                <Iconfont name="jiantou" :size="15"></Iconfont>
            </span>
            <span>2.一键加入知识库</span>
            <span>
                <Iconfont name="jiantou" :size="15"></Iconfont>
            </span>
            <span>3.快速学习关键知识点</span>
            <span>
                <Iconfont name="jiantou" :size="15"></Iconfont>
            </span>
            <span>4.创作便捷引用</span>
        </div>
        <div>
            <img src="https://static.xiaoin.cn/pub/xiaoin_nuxt/images/scholar_hero.png" alt="学术搜索" draggable="false" />
        </div>
    </div>
</template>

<script setup lang="ts">

// 搜索查询内容
const searchQuery = ref('')

// 计算属性：判断是否有内容
const hasContent = computed(() => {
    return searchQuery.value.trim().length > 0
})

// 处理搜索点击事件
const handleSearch = () => {
    if (hasContent.value) {
        // 这里可以添加搜索逻辑
        // console.log('搜索内容:', searchQuery.value)
        // TODO: 实现搜索功能
        // 检测是否登陆
        const userStore = useUserStore()
        if (!userStore.isLogined) {
            userStore.openLoginModal()
            return
        }
        navigateTo(`/scholar/search?keyword=${searchQuery.value}`)
    }
}

</script>

<style lang="scss" scoped>
.disabled-chat-input {
    background: #f1f1f1;
}
</style>