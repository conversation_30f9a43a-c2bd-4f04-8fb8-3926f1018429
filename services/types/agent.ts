export interface AgentStream {
    Id: string;
    Type: number;
    Data: AgentStreamData;
    Time: string;
}
export interface AgentStreamDataResult {
    id: string;
    file_id?: string;
    title?: string;
    content: string;
    desc: string;
    meta?: {
        name: string;
        snippet: string;
        url?: string;
        // main_url?: string;
        pdf_url?: string;
    }
}

export interface AgentStreamData {
    type: string;
    sequence_number: number;
    conversation_id: string;
    item_id: string;
    delta: string;
    content: string;
    metadata: Object;

    tool_name: string;
    description: string;
    question?: string;
    answer?: string;
    result: {
        params: {
            round: number;
            params: string;
        },
        results: AgentStreamDataResult[];
        type: string;
        count?: number;
    };
    params: {
        round: number;
        params: string;
    };
    children?: AgentStreamData[];
}

export interface AgentMessageRendered {
    type: string;
    conversation_id: string;
    item_id: string;
    content: string;
}

export interface ReplyData {
    type: string;
    sequence_number: number;
    conversation_id: string;
    item_id: string;
    delta: string;
    content: string;
}


