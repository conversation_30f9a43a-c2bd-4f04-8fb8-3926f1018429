export interface SubmissionEditInfo {
    id: string
    createTime: Date
    userId: string
    submissionId: string
    title: string
    editorData: string
    status: number
    aiUsableCount: number
}


export interface Replacements {
    topic_cn: string
    topic_en: string
}
export interface EditorDataInfo {
    template: string
    replacements: Replacements
    rich_abstract_cn: object
    rich_abstract_en: object
    main_content: object
    text: string
}

export interface DoAiActionParams {
    submissionId: string
    code: string
    content: string
    // teamId?: string
}

export interface SubmissionSaveParams {
    submissionId: string
    title: string
    editorData: string
}
export interface ExportCodeParams {
    id: string
    template: string
}