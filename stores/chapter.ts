import http from '@/utils/book/http'
import { message } from 'ant-design-vue'
import { defineStore } from 'pinia'
import { UserService } from '~/services/user'
import { useTaskStore } from '~/stores/task'

import type { Book, Chapter } from '~/types/book'
import { saveChapterContent } from '~/utils/api/chapter'
import { exportFile } from '~/utils/api/export_docx'
import { getChapterCoin } from '~/utils/api/generate_content'
import { editorBus } from '~/utils/book/editorBus'


export const useChapterStore = defineStore('chapter', () => {
  const taskStore = useTaskStore()

  // 状态
  const currentBook = useState<Book | null>('currentBook', () => null)
  const currentChapter = useState<Chapter | null>('currentChapter', () => null)
  const isLoaded = ref(false)


  // const isShowCreativeAssistant = ref(true) //是否显示创意助手
  const isGenerating = ref(false)
  const isExporting = ref(false)
  const userEditCustomPrompt = ref('')
  const showTableModal = ref(false)
  const showChartModal = ref(false)
  const showMathModal = ref(false)
  const showSvgModal = ref(false)
  const showFileAddModal = ref(false)
  const showImgModal = ref(false)
  // 引用文本
  const referceText = ref('')

  const referceAfterText = ref('')
  const referceBeforeText = ref('')
  const schematicReferceText = ref('')

  const router = useRouter()

  const showPayModal = ref(false)
  // const uploadFileStudyingFeesInsufficient = ref(false) //上传文件学习费用不足

  const uploadFilesToStudyFees = ref(0) //上传文件学习费用 - 硬币

  const payTriggerType = ref<BOOK_PAY_TRIGGER_TYPE>()

  const chapterUploadFileModal = ref()
  const bookUploadTemplateFileList = ref<any[]>([])

  const bookUploadDataBaseFileList = ref<any[]>([])

  // 计算属性
  const bookValue = computed(() => currentBook.value)

  const spaceId = computed(() => {
    return UserService.getCurrentLoginInfo()?.id || ''
  })

  // 获取上一章节和下一章节
  const previousChapter = computed(() => {
    if (!bookValue.value?.flattened_chapters || !currentChapter.value) return null

    const allChapters = bookValue.value.flattened_chapters
    const currentIndex = allChapters.findIndex(c => c.key === currentChapter.value?.key)
    if (currentIndex <= 0) return null
    return allChapters[currentIndex - 1]
  })

  const nextChapter = computed(() => {
    if (!bookValue.value?.flattened_chapters || !currentChapter.value) return null

    const allChapters = bookValue.value.flattened_chapters
    const currentIndex = allChapters.findIndex(c => c.key === currentChapter.value?.key)
    if (currentIndex === -1 || currentIndex === allChapters.length - 1) return null
    return allChapters[currentIndex + 1]
  })

  const loadChapter = async (key: string) => {
    if (!key) return
    isLoaded.value = false
    try {

      // if (!currentChapter.value || currentChapter.value.key !== key) {
      const response = await http.get<Chapter>(`/api/chapters/${key}/`)
      if (response.data) {
        currentChapter.value = response.data
        nextTick(() => {
          isLoaded.value = true
        })
      }
      // } else {
      //   isLoaded.value = true
      // }
    } catch (error) {
      console.error('加载章节信息失败:', error)
      message.error('加载章节失败，请稍后重试')
      isLoaded.value = true
    } finally {
      isLoaded.value = true
    }
  }

  const saveContent = async () => {
    if (!currentChapter.value) return false
    try {
      const saveResponse = await saveChapterContent({
        chapter_key: currentChapter.value.key,
        content: currentChapter.value.content
      })
      if (saveResponse.updated) {
        console.log('保存成功')
        return true
      } else {
        console.log('未保存章节内容:', saveResponse.message)
        return false
      }
    } catch (error) {
      console.error('保存失败:', error)
      return false
    }
  }

  const hasContent = computed(() => {
    if (!currentChapter.value) return false

    const blockList = currentChapter.value.content.content || []
    if (blockList.length === 0) {
      return false
    }

    const firstBlock = blockList[0]
    const firstBlockType = firstBlock.type
    const firstBlockContent = firstBlock.content
    if (!firstBlockContent) {
      return false
    }

    return true
  })


  const exportToWord = async (key: string = 'docx') => {
    if (!bookValue.value?.key) {
      message.warning('请先选择一本书')
      return
    }

    // 验证格式参数
    if (key !== 'docx' && key !== 'pdf') {
      message.warning('不支持的导出格式，仅支持docx和pdf')
      return
    }

    const loadingKey = 'exportLoading'
    message.loading({ content: `正在导出${key.toUpperCase()}...`, key: loadingKey, duration: 0 })
    isExporting.value = true
    try {
      const response = await exportFile({
        book_key: bookValue.value.key,
        format: key // 传递格式参数给API
      })

      if (response.status == 200 && response.data.success) {
        window.location.href = response.data.download_url
        message.success({ content: `${key.toUpperCase()}导出成功`, key: loadingKey })
        return
      }
      message.error({ content: `${key.toUpperCase()}导出失败`, key: loadingKey })

      // TODO 导出的pdf类型打不开，文件格式错误
      // 根据格式设置不同的MIME类型
      // const mimeType = key === 'pdf'
      //   ? 'application/pdf'
      //   : 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'

      // const blob = new Blob([response.data], { type: mimeType })

      // const url = window.URL.createObjectURL(blob)
      // const link = document.createElement('a')
      // link.href = url

      // console.log("urls ==>", url)

      // link.download = `${bookValue.value.title || '万能小in AI写书'}.${key}`

      // document.body.appendChild(link)
      // link.click()

      // document.body.removeChild(link)
      // window.URL.revokeObjectURL(url)


    } catch (error) {
      console.error('导出失败:', error)
      message.error({ content: `${key.toUpperCase()}导出失败`, key: loadingKey })
    } finally {
      isExporting.value = false
    }
  }

  const insertImgContent = (content: any) => {
    editorBus.emit('editor:insertContentAt',
      {
        type: 'image',
        attrs: {
          src: content.content,
          alt: 'Inserted Image',
          title: 'Inserted Image',
        },
      })
    message.success('图片插入成功')
  }

  const insertTableContent = (content: any) => {
    // editorBus.emit('editor:insertContent', content)
    editorBus.emit('editor:insertContentAt', content)
    message.success('表格插入成功')
  }

  const insertChartContent = (content: any) => {
    // editorBus.emit('editor:insertContent', content)
    editorBus.emit('editor:insertContentAt', content.content)
    message.success('图表插入成功')
  }

  const insertSvgContent = (content: any) => {
    // editorBus.emit('editor:insertContent', content)
    editorBus.emit('editor:insertContentAt', content.content)
    message.success('示意图插入成功')
  }

  const insertMathContent = (content: { content: any }) => {
    // if (content.type === 'inline') {
    //   // 对于行内公式，插入文本节点和公式节点
    //   // const nodes = [
    //   //   { type: 'mathInline', attrs: { formula: content.content } },
    //   //   { type: 'text', text: content.description },
    //   // ]
    // } else {
    //   // 对于块级公式，插入段落节点和公式节点
    //   // const nodes = [
    //   { type: 'mathBlock', attrs: { formula: content.content } },
    //   { type: 'paragraph', content: [{ type: 'text', text: content.description }] },
    // ]
    // editorBus.emit('editor:insertContent', content.content)
    editorBus.emit('editor:insertContentAt', content.content)
    // }
    message.success('公式插入成功')
  }

  const insertFileContent = (content: any) => {
    editorBus.emit('editor:insertContent', content)
    message.success('文件插入成功')
  }

  const setPromptTemplate = (type: string) => {
    const templates: Record<string, string> = {
      '继续写作': '请基于上文内容，继续写作下面的部分，保持风格一致：',
      '优化表达': '请帮我优化以下内容的表达方式，使其更加专业和流畅：',
      '扩展内容': '请帮我扩展以下内容，添加更多细节和例子：',
      '修改语气': '请帮我修改以下内容的语气，使其更加：',
      '检查错误': '请帮我检查以下内容中的错误，包括语法、用词等：'
    }

    userEditCustomPrompt.value = templates[type]
  }
  const showPayModalByType = (type: BOOK_PAY_TRIGGER_TYPE) => {
    payTriggerType.value = type
    // bookUploadTemplateFileList.value = fileList
    showPayModal.value = true
  }

  const loadChapterCoinData = async (type: string = FILE_COIN_FREE_TYPE.TEMPLATE) => {
    const params = {
      spaceId: spaceId.value,
      chapter_key: currentChapter.value?.key || '',
      payAction: payTriggerType.value,
      fileIds: [] as any[]
    }
    if (type == FILE_COIN_FREE_TYPE.DATABANK) {
      params.fileIds = (bookUploadDataBaseFileList.value || []).map(item => item.id)
    }
    if (type == FILE_COIN_FREE_TYPE.TEMPLATE) {
      params.fileIds = (bookUploadTemplateFileList.value || []).map(item => item.id)
    }
    const res = await getChapterCoin(params)
    // console.log("res ==>", res)
    if (!res.success) {
      return
    }
    if (res.result.fileList.length > 0) {
      if (type == FILE_COIN_FREE_TYPE.DATABANK) {
        bookUploadDataBaseFileList.value = res.result.fileList
      }
      if (type == FILE_COIN_FREE_TYPE.TEMPLATE) {
        bookUploadTemplateFileList.value = res.result.fileList
      }
    }
    return res.result
  }



  const resetData = () => {
    currentBook.value = null
    currentChapter.value = null
    isLoaded.value = false
    isGenerating.value = false
    isExporting.value = false
    userEditCustomPrompt.value = ''
    showTableModal.value = false
    showChartModal.value = false
    showMathModal.value = false
    showSvgModal.value = false
    showFileAddModal.value = false
    showImgModal.value = false
    referceText.value = ''
    referceBeforeText.value = ''
    referceAfterText.value = ''
    schematicReferceText.value = ''
    payTriggerType.value = undefined
    bookUploadTemplateFileList.value = []
  }


  return {
    currentBook,
    currentChapter,
    isLoaded,
    bookValue,
    previousChapter,
    nextChapter,
    isGenerating,
    spaceId,

    isExporting,
    userEditCustomPrompt,
    showTableModal,
    showChartModal,
    showMathModal,
    showSvgModal,
    showFileAddModal,
    bookUploadTemplateFileList,
    bookUploadDataBaseFileList,
    showImgModal,
    referceText,
    schematicReferceText,
    showPayModal,
    payTriggerType,
    uploadFilesToStudyFees,
    chapterUploadFileModal,
    referceAfterText,
    referceBeforeText,

    loadChapter,
    saveContent,
    exportToWord,
    insertImgContent,
    insertTableContent,
    insertChartContent,
    insertMathContent,
    insertSvgContent,
    insertFileContent,
    setPromptTemplate,
    showPayModalByType,
    loadChapterCoinData,
    resetData
  }
}) 