import { defineStore } from 'pinia';
import { listHotCreator } from '~/api/appCategory';
import { getHomeData } from '~/api/home';

interface AppItem {
    name: string
    avatar: string
    code: string
    description?: string
    lastTime: string
    content: string
    mediaType: string
    isPinned: string
    isRead: string
    type: string
    buttonText: string
}

interface HomeDataItem {
    list: AppItem[]
    title: string
    groupId: string
}

export const useHomeStore = defineStore('home', {
    state: () => ({
        homeData: [] as HomeDataItem[],
        createCount: 0,
        latestSubmissionList: [] as string[],
        isLoading: false,
        isOpenTopBar: true, // 是否显示顶部广告
    }),

    actions: {
        async loadHomeData(params: any, url: string) {
            try {
                this.isLoading = true
                const res = await getHomeData(params, url)
                if (!res.ok) return

                this.createCount = res.data.createCount * 2
                const list = res.data.groupList
                if (list.length > 0) {
                    this.homeData = list
                }
            } catch (error) {
                console.error('Failed to load home data:', error)
            } finally {
                this.isLoading = false
            }
        },

        async loadListHotCreator() {
            try {
                const result = await listHotCreator({})
                if (result.ok && result.data) {
                    this.latestSubmissionList = result.data.map((item) => `${this.getRandomInt()}秒前有用户写作了一篇${item.name}`)
                }
            } catch (error) {

            }
        },

        getRandomInt(): number {
            return Math.ceil(Math.random() * 11)
        }
    },

    getters: {
        featuredApps: (state) => state.homeData[0]?.list || []
    }
}) 