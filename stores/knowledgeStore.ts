import { defineStore } from 'pinia';
import { folderSearch, listAll, listAllFiles } from '~/api/repositoryFile';
import { UserService } from '~/services/user';

interface ProcessData {
    title: string
    file_id: string
    inspiration_status: number
    inspiration_data: string
    parse_status: number
    summary_data: string
    summary_status: number
    word_count: number
    green_status: number
    translate_status: number // 2完成3失败
    error: Error
}
// 定义知识文件的类型
interface KnowledgeFile {
    id: string;
    title: string;
    fileType: string;
    wordCount: number;
    createTime: string;
    isFolder?: boolean;
    level?: number;
    children?: KnowledgeFile[];
    isExpanded?: boolean;
    status: string;
    processData: ProcessData;
}

export const useKnowledgeStore = defineStore('knowledge', {
    state: () => ({
        allKnowledgeFileOptions: [] as KnowledgeFile[], // 设置类型
        knowledgeFileOptions: [] as KnowledgeFile[], // 设置类型
        // folderFiles: [] as KnowledgeFile[], // 存储特定文件夹的文件列表
        selectedFolderIds: [] as string[], // 存储选中的文件夹ID
        appCode: '',
        total: 0,
        totalPages: 0,
        currentPage: 1,
        pageSize: 10,
        isSearchMode: false, // 是否处于搜索模式
        hasSearchKeyword: false, // 是否有搜索关键词
        reqParams: { // 新增 reqParams 字段
            fileNames: '', //文件后缀：doc、pdf等
            // fileName: '',
            status: '', // 文件状态
            fileType: '', //文件类型：html、file
            folderId: 0,
            keywords: '', //搜索关键词
        },
        treeData: [] as KnowledgeFile[],
        // folderFilesMap: new Map(), // 存储文件夹ID和对应的文件列表
        statusCheckTimer: null as NodeJS.Timeout | null, // 状态检查定时器
        isCheckingStatus: false, // 是否正在检查状态
    }),
    actions: {

        setAllKnowledgeFileOptions(options: KnowledgeFile[]) {
            const existingIds = new Set(this.allKnowledgeFileOptions.map(file => file.id));
            const uniqueList = options.filter(file => !existingIds.has(file.id));
            this.allKnowledgeFileOptions.push(...uniqueList);
            // console.log('allKnowledgeFileOptions', this.allKnowledgeFileOptions)
        },

        // 加载文件夹文件数据
        async loadKnowledgeFileData(folderId: string | number = 0, defaultParams: any = {}) {
            const params: Record<string, any> = {
                pageNo: this.currentPage,
                pageSize: this.pageSize,
                ...this.reqParams,
                ...defaultParams,
            };
            console.log('loadKnowledgeFileData', params, defaultParams);
            if (this.reqParams.fileNames.includes('html')) {
                params.fileType = ''
            }
            // 更新 folderId
            params.folderId = folderId;

            params.spaceId = UserService.getCurrentLoginInfo()?.id


            const res = await listAll(params);

            if (res.ok && res.data && res.data.items) {
                const list: KnowledgeFile[] = (res.data.items.records || []).map((item: any) => ({
                    id: item.id,
                    title: item.name,
                    key: item.id,
                    fileId: item.id,
                    fileType: item.fileType,
                    type: item.type,
                    isFolder: item.type == 'folder',
                    wordCount: item.wordCount,
                    createTime: item.createTime,
                    status: item.status,
                    processData: item.processData,
                }));
                // 去重逻辑
                this.setAllKnowledgeFileOptions(list)

                // 更新当前显示的文件列表
                this.knowledgeFileOptions = list;

                this.total = res.data.items.total;
                this.totalPages = res.data.items.pages;
                this.currentPage = params.pageNo;

                // 检查是否有处于 init 状态的文件，如果有则启动定时器
                this.checkAndStartStatusTimer(folderId, defaultParams);
            } else {
                throw new Error(res?.message || '加载知识库列表错误');
            }
        },

        // 搜索文件
        async loadKnowledgeSearchFileData() {

            const params: any = {
                ...this.reqParams,
                pageNo: 1,
                pageSize: 1000,
                keyword: this.reqParams.keywords,
                searchTypes: ['file', 'folder', 'block']
            }

            params.spaceId = UserService.getCurrentLoginInfo()?.id

            const res = await folderSearch(params);
            // console.log('loadKnowledgeSearchFileData', res.data)
            if (res.ok && res.data) {
                const list: KnowledgeFile[] = (res.data || []).map((item: any) => ({
                    id: item.id,
                    title: item.name,
                    key: item.id,
                    fileId: item.id,
                    fileType: item.fileType,
                    type: item.type,
                    isFolder: item.type == 'folder',
                    wordCount: item.wordCount,
                    createTime: item.createTime,
                    status: item.status,
                    processData: item.processData,
                }));

                this.knowledgeFileOptions = list;

                this.setAllKnowledgeFileOptions(list)
            }
        },

        // 获取文件夹下所有文件
        async listAllFiles(folderId: string | number) {

            const params: Record<string, any> = {
                pageNo: 1,
                pageSize: 1000, // 设置较大的数值以获取所有文件
                folderId: folderId,
                fileNames: this.reqParams.fileNames,
                limit: 20,
                status: this.reqParams.status,
            };

            params.spaceId = UserService.getCurrentLoginInfo()?.id

            // console.log('listAllFiles', params)
            const res = await listAllFiles(params);
            // console.log('listAllFiles', res.data)
            if (res.ok && res.data) {
                const newList: KnowledgeFile[] = (res.data || []).map((item: any) => ({
                    id: item.id,
                    title: item.name,
                    key: item.id,
                    fileId: item.id,
                    fileType: item.fileType,
                    type: item.type,
                    isFolder: item.type == 'folder',
                    wordCount: item.wordCount,
                    createTime: item.createTime,
                    status: item.status,
                    processData: item.processData,
                }));
                return newList;
            }
            return [];
        },

        // 
        clearFolderFilesMap() {
            // this.folderFilesMap.clear();
        },

        // 重置数据
        resetData() {
            this.knowledgeFileOptions = [];
            this.allKnowledgeFileOptions = []; // 重置所有数据
            this.selectedFolderIds = []; // 重置选中的文件夹ID
            // this.folderFiles = [];
            this.total = 0;
            this.currentPage = 1;
            this.isSearchMode = false;
            this.hasSearchKeyword = false;
            // 🔧 重置请求参数，避免不同调用之间的参数污染
            this.reqParams = {
                fileNames: '',
                fileType: '',
                folderId: 0,
                keywords: '',
                status: '',
            };
            this.clearFolderFilesMap();
            // 清理定时器
            this.stopStatusCheckTimer();
        },

        // 下一页
        nextPage() {
            if (this.currentPage < Math.ceil(this.total / this.pageSize)) {
                this.currentPage += 1;
            }
        },

        // 上一页
        prevPage() {
            if (this.currentPage > 1) {
                this.currentPage -= 1;
            }
        },

        // 设置文件类型
        setOptions(newOptions: any) {
            // console.log("newOptions ==>", newOptions)
            if (!newOptions) {
                return
            }

            this.reqParams.fileNames = newOptions
        },

        setAppCode(code: string) {
            this.appCode = code
        },

        initKnowledgeFileData() {
            this.allKnowledgeFileOptions = [] as KnowledgeFile[]
            this.knowledgeFileOptions = [] as KnowledgeFile[]
            this.selectedFolderIds = [] as string[] // 初始化选中的文件夹ID
            // this.folderFiles = [] as KnowledgeFile[]
            this.total = 0
            this.totalPages = 0
            this.currentPage = 1
            this.pageSize = 10
            this.reqParams = {
                fileNames: '',
                keywords: '',
                status: 'done',
                fileType: 'file',
                folderId: 0,
            }
            // 清理定时器
            this.stopStatusCheckTimer();
        },

        transformToTree(files: KnowledgeFile[]) {
            return files.map(file => ({
                ...file,
                level: 0,
                isExpanded: false,
                children: []
            }))
        },

        // 添加文件夹到选中列表
        addSelectedFolder(folderId: string) {
            if (!this.selectedFolderIds.includes(folderId)) {
                this.selectedFolderIds.push(folderId);
            }
        },

        // 从选中列表中移除文件夹
        removeSelectedFolder(folderId: string) {
            this.selectedFolderIds = this.selectedFolderIds.filter(id => id !== folderId);
        },

        // 检查文件夹是否被选中
        isFolderSelected(folderId: string) {
            return this.selectedFolderIds.includes(folderId);
        },

        // 检查并启动状态定时器
        checkAndStartStatusTimer(folderId: string | number = 0, defaultParams: any = {}) {
            // 检查是否有处于 init 状态的文件
            const hasInitFiles = this.knowledgeFileOptions.some(file => file.status == 'init');

            if (hasInitFiles && !this.isCheckingStatus) {
                this.startStatusCheckTimer(folderId, defaultParams);
            } else if (!hasInitFiles && this.statusCheckTimer) {
                // 如果没有 init 状态的文件，停止定时器
                this.stopStatusCheckTimer();
            }
        },

        // 启动状态检查定时器
        startStatusCheckTimer(folderId: string | number = 0, defaultParams: any = {}) {
            // 如果已经有定时器在运行，先清除
            if (this.statusCheckTimer) {
                clearInterval(this.statusCheckTimer);
            }

            this.isCheckingStatus = true;
            console.log('启动文件状态检查定时器');

            this.statusCheckTimer = setInterval(async () => {
                try {
                    // 检查是否还有 init 状态的文件
                    const initFiles = this.knowledgeFileOptions.filter(file => file.status == 'init');
                    const hasInitFiles = initFiles.length > 0;

                    console.log(`状态检查 - 处理中文件数: ${initFiles.length}`, initFiles.map(f => ({ id: f.id, title: f.title, status: f.status })));

                    if (hasInitFiles) {
                        console.log('检查文件状态中...');
                        // 重新加载数据，但不重新启动定时器（避免递归）
                        await this.loadKnowledgeFileDataWithoutTimer(folderId, defaultParams);

                        // 检查更新后的状态
                        const updatedInitFiles = this.knowledgeFileOptions.filter(file => file.status == 'init');
                        console.log(`状态更新后 - 处理中文件数: ${updatedInitFiles.length}`);
                    } else {
                        // 所有文件都处理完成，停止定时器
                        console.log('所有文件处理完成，停止状态检查');
                        this.stopStatusCheckTimer();
                    }
                } catch (error) {
                    console.error('状态检查出错:', error);
                    // 出错时也停止定时器
                    this.stopStatusCheckTimer();
                }
            }, 5000); // 每5秒检查一次
        },

        // 停止状态检查定时器
        stopStatusCheckTimer() {
            if (this.statusCheckTimer) {
                clearInterval(this.statusCheckTimer);
                this.statusCheckTimer = null;
            }
            this.isCheckingStatus = false;
            console.log('状态检查定时器已停止');
        },

        // 加载数据但不启动定时器（用于定时器内部调用）
        async loadKnowledgeFileDataWithoutTimer(folderId: string | number = 0, defaultParams: any = {}) {
            const params: Record<string, any> = {
                pageNo: this.currentPage,
                pageSize: this.pageSize,
                ...this.reqParams,
                ...defaultParams,
            };

            if (this.reqParams.fileNames.includes('html')) {
                params.fileType = ''
            }
            params.folderId = folderId;
            params.spaceId = UserService.getCurrentLoginInfo()?.id

            const res = await listAll(params);

            if (res.ok && res.data && res.data.items) {
                const newList: KnowledgeFile[] = (res.data.items.records || []).map((item: any) => ({
                    id: item.id,
                    title: item.name,
                    key: item.id,
                    fileId: item.id,
                    fileType: item.fileType,
                    type: item.type,
                    isFolder: item.type == 'folder',
                    wordCount: item.wordCount,
                    createTime: item.createTime,
                    status: item.status,
                    processData: item.processData,
                }));

                // 更新现有文件的状态，而不是替换整个数组
                this.updateFileStatuses(newList);

                this.total = res.data.items.total;
                this.totalPages = res.data.items.pages;
                this.currentPage = params.pageNo;
            }
        },

        // 更新文件状态的方法
        updateFileStatuses(newList: KnowledgeFile[]) {
            // 创建新文件的映射
            const newFileMap = new Map(newList.map(file => [file.id, file]));

            // 更新现有文件的状态 - 创建新数组以确保响应式更新
            this.knowledgeFileOptions = this.knowledgeFileOptions.map(file => {
                const newFile = newFileMap.get(file.id);
                if (newFile) {
                    // 合并新的状态数据
                    return { ...file, ...newFile };
                }
                return file;
            });

            // 更新 allKnowledgeFileOptions 中的文件状态
            this.allKnowledgeFileOptions = this.allKnowledgeFileOptions.map(file => {
                const newFile = newFileMap.get(file.id);
                if (newFile) {
                    return { ...file, ...newFile };
                }
                return file;
            });

            console.log('文件状态已更新:', this.knowledgeFileOptions.map(f => ({ id: f.id, title: f.title, status: f.status })));
        }
    },
});