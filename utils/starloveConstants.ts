// import { cosBucketPrefixStatic, cosBucketPrefixStellar } from './constants'

const cosBucketPrefixStatic = 'https://static-1256600262.file.myqcloud.com/'
const cosBucketPrefixStellar = 'https://stellar-1256600262.cos.ap-shanghai.myqcloud.com/'

const StarloveConstants = {
  // defaultAvatar: `${cosBucketPrefixStatic}life-circle/user-avatar.png`,
  defaultAvatar: 'https://static-1256600262.file.myqcloud.com/mini-app/roles/niming.png',
  defaultAvatarOfAI:
    'https://stellar-1256600262.cos.ap-shanghai.myqcloud.com/lc-prod/1631490513276301314/image/9a18ee45-395d-4ffa-8533-4f96da60f4c6.jpg',
  defaultNickname: 'UFO',
  defaultAvatarDbValue: 'default_avatar',

  imageUrls: {
    inviteBanner: `${cosBucketPrefixStatic}mini-app/pic_Invite_friends_to_get_vip_background.png`,
    inviteSteps: `${cosBucketPrefixStatic}mini-app/pic_invitation_steps.png`,
    vipIntroduceImage: `${cosBucketPrefixStatic}mini-app/vip_introduce.png?v=${new Date().getTime()}`,
    vipArticleMark: `${cosBucketPrefixStatic}mini-app/icon_vip_exclusive.png`,
    vipMemberMark: `${cosBucketPrefixStatic}mini-app/icon_vip_certification_mark.png`,
    defaultLifecircleBackgroundImage: `${cosBucketPrefixStellar}prod/0/image/default.png`,
    defaultLogoOfLifecircle: `${cosBucketPrefixStatic}mini-app/inschoo_logo.png`,
    sharePosterBg: `${cosBucketPrefixStatic}life-circle/basics_new.png`,
    defaultUserBackgroundImage: {
      vip: `${cosBucketPrefixStatic}mini-app/pic_my_member_background.png`,
      normal: `${cosBucketPrefixStatic}mini-app/pic_my_default_background.png`,
    },
    userShareCardBg: {
      vip: `${cosBucketPrefixStatic}mini-app/card_vip.png`,
      normal: `${cosBucketPrefixStatic}mini-app/user_card_normal.png`,
    },
    userSharePosterBg: {
      vip: `${cosBucketPrefixStatic}mini-app/user_poster_vip.png`,
      normal: `${cosBucketPrefixStatic}mini-app/user_poster_normal.png`,
    },
    momentPosterBg: `${cosBucketPrefixStatic}life-circle/basics_new.png`,

    clickNow: `${cosBucketPrefixStatic}mini-app/click_now.png`,
    whiteDot: `${cosBucketPrefixStatic}mini-app/white_dot.png`,
    h5Image: {
      addNewUser: `${cosBucketPrefixStatic}xiaoin-h5/image/adv-new-user.png`,
      icons: {
        closePopup: `${cosBucketPrefixStatic}/xiaoin-h5/icons/close-popup.png`,
      },
    },

    normalTopOneAvatar: 'https://static-1256600262.file.myqcloud.com/mini-app/roles/general.png',
  },

  primaryColor: '#FF2442',
  tagBgColor: '#FFF5F6',

  keyOflocalStorage: {
    trackId: 'track_id',
    localChannelData: 'local_channel_data',
    sharerUserId: 'sharer_user_id',
    sharerUserIdUpdateTime: 'sharer_user_id_update_time',
    tempVisitLifecircleId: 'temp_visit_lifecircle_id',
    proSwitch: 'pro_switch',
    channelInfo: 'channel_info',
    clickId: 'click_id',
    sourceId: 'source_id',
    qhclickId: 'qhclick_id',
    alreadyLoggedIn: 'already_logged_in',
    isFirstOpenKnowledgePage: 'is_first_open_knowledge_page',
    isFirstOpenKnowledgeGuide: 'is_first_open_knowledge_guide',
    aiChatQuestionContentDraft: 'ai_chat_question_content_draft', //ai知识库联网搜索
    aiChatKnowledgeBaseContentDraft: 'ai_chat_knowledge_base_content_draft', //ai知识库知识库搜索
    aiQuestionMessageRecommendApplyData: 'ai_question_message_recommend_apply_data', //推荐写作 临时保存
    aiQuestionMessageUnderstandsData: 'ai_question_message_understands_data', //理解的问题 临时保存
    aiQuestionModeList: 'ai_question_mode_list', // 搜索模式  联网和知识库
    mouseDeviceInfo: 'mouse_device_info', // 鼠标设备信息
    platform: 'platform', // 鼠标设备信息

    isCurrentOperationAppOnTheDesktop: 'is_current_operation_app_on_the_desktop', // 是否在桌面打开应用

    aMaximumOfThreePPTOutlineData: 'a_maximum_of_three_ppt_outline_data', // 最多三个ppt大纲数据
    hasTheKnowledgeBaseMindMapAlreadyBeenLoaded:
      'has_the_knowledge_base_mind_map_already_been_loaded', // 知识库的思维导图是否已经加载过
    numberOfTimesThePPTOutlineHasBeenLoaded: 'number_of_times_the_ppt_outline_has_been_loaded', // 加载ppt大纲的次数

    outlineGenerationCount: 'outline_generation_count', // 普通写作应用大纲生成次数
    bookEditorTourShown: 'book-editor-tour-shown',
    homeChatQuestion: 'home_chat_question', //首页聊天数据
    redirectUrl: 'redirectUrl', //登录后跳转地址
  },

  keyOfsessionStorage: {
    sessionChannelData: 'session_channel_data',
  },

  keyOfEventBus: {
    theRechargeIsSuccessful: 'theRechargeIsSuccessful', //充值成功
    isVisibleRecharge: 'isVisibleRecharge', // 资讯页面最下方 升级助手 按钮点击传值打开充值弹窗
    askAiQuestionInTheFrontPageOfTheKnolwedgeBase:
      'ask_ai_question_in_the_front_page_of_the_knolwedge_base',
    openKnolwedgeBasePage: 'open_knolwedge_base_page',
    updateTheSubmissionAttachmentInformation: 'update_the_submission_attachment_information',
    createSubmissionSelectionPPTLecture: 'create_submission_selection_ppt_lecture', //选择PPT讲稿的eventbus 的key
    updateSubmissionInfo: 'update_submission_info', //用于确认订单文件读取完成后更新submission
    rechargeModalOpen: 'recharge_modal_open',
    goToLogin: 'go_to_login',
    updateLogin: 'update_login',
    updateMessageDelete: 'update_message_delete',
    beUpdateKnowledgeFilePage: 'be_update_knowledge_file_page', //在文件聊天时，点击文件名称更新左侧页码
    updateCreationSearchBoxKeyword: 'update_creation_search_box_keyword', //更新写作页的搜索框的值
    updateAiQuestionLeftMessageRecords: 'update_ai_question_left_message_records', //点击新搜索发送消息后，更新左侧列表
    highLightTranslate: 'high-light-translate', //点击翻译后的文本高亮
    pdfPreviewPageChange: 'pdf-preview-page-change', //pdf预览页码变化
    updateAiQuestionLeftTitle: 'update_ai_question_left_title', //在搜索列表页更新标题后，同步到左侧列表中
    aiQuestionIsANewQuestionAndTheSessionIdThatIsBeingAnswered:
      'ai_question_is_a_new_question_and_the_session_id_that_is_being_answered', //AI提问是新搜索并且搜索正在回答中的session id
    isUploadTheAiQuestionResponseEnd: 'is_upload_the_ai_question_response_end', //是否更新聊天内容列表
    updateLeftMessageRecords: 'update_left_message_records', //更新搜索左侧菜单搜索记录
    updateLeftManualOperationMessageRecords: 'update_left_manual_operation_message_records', //更新搜索左侧菜单搜索记录，通过list手动换，不掉接口
    addToNotes: 'add_to_notes', // 添加到笔记
    chatScrollToBottom: 'chat_scroll_to_bottom', // 滚动到底部
    // updateMessageRefs: 'update_message_refs', //更新聊天返回refs
    // updateKnowledgeInfoOfCount: 'update_knowledge_info_of_count',
    bookEditorCreateAfterConfirmPayModal: 'book_editor_create_after_confirm_pay_modal', // 创建书籍后确认支付弹窗
    bookEditorCreateAfterConfirmPayCancelModal: 'book_editor_create_after_confirm_pay_cancel_modal', // 文件上传后确认支付弹窗取消
    myQuestionSearch: 'my_question_search',//我的提问记录搜索
    myQuestionSelectKnowledgeFileDeleteAction: 'my_question_select_knowledge_file_delete_action',//知识库选择文件删除
    updateKnowledgeBaseListAfterAddingQuestionResult: 'update_knowledge_base_list_after_adding_question_result' // 在知识库提问后，将结果添加到当前知识库，此时需要更新知识库列表 ，添加到
  },

  logEvent: {
    advShowOk: 'mp_adv_show_ok_event',
    share: 'share_event',
  },

  texts: {
    composerPlaceHolder: '分享这一刻的心情…',
  },

  events: {
    publishOk: 'publishOk',
  },

  sectionIds: {
    sectionIdOfAll: 'all',
    sectionIdOfElite: 'elite',
    sectionIdOfVip: 'vip',
  },
  textColors: {
    vip: '#A95F0B',
    normal: '#5775DB',
  },
  subscribeMessageTemplateIds: {
    newCommentOrReplyOfMoment: 'lVV94-4zNktbY6iAO_px5CXMWEt4wEj9nET5PWQrg4A', //新的评论提醒
    newLikeOfMoment: 'nXpnhn2mPbmWhya6g7u1St13IOLW6EHjjGLb3J3z_g4', //动态点赞通知
    newReplyOfComment: 'RIRi5USEYxzM9FXXVOtcqbkc6XP_ah13PG2zfhMkZo', //评论回复通知
    newLikeOfComment: 'KnFMDC87EGNVD3qLf6bMIFCHSdU19rLIzdpBNGJgO4g', //评论点赞通知
    verifyResult: '3LkU_Mj11oWKHww73YnJ6T9pLLepHv-QtJuJ8wd-DnI', //认证结果通知 just for inschool
    momentApplyForTop: 'IRC4rID1d8W5ljVWtWRc2iFzYjxp_9lXbgGXv2aIM-w', //置顶 提醒 审核结果通知
    circleMomentCanApplyForTop: '-L6BuAOGSDkurWclbIPHaCe_Puki40szqX_ObwA7wDU', //圈子当前有置顶空位 通知
  },

  pagePaths: {
    login: '/pages/xiaoin/auth/index',
    momentDetail: '/pages/moment/detail/detail',
    userHome: '/pages/userHome/userHome',
    editInfo: '/pages/mine/info/index/index',
    selectSchool: '/pages/misc/school/index',
  },

  scenceTargets: {
    user: 'user',
    moment: 'moment',
    inschoolHome: 'inschoolHome',
  },
  dateFormats: {
    long: 'yyyy-MM-dd HH:mm:ss',
    short: 'yyyy-MM-dd',
    shortWithDots: 'yyyy.MM.dd',
  },
  documents: {
    inviteRule: {
      id: '15',
      title: '邀请规则',
    },
    userProtocal: {
      id: '8',
      title: '用户协议',
    },
    privacyPolicy: {
      id: '9',
      title: '隐私政策',
    },
  },
  defaultCheckResultOfText: {
    isValid: true,
    errorMsg: '',
  },
  slogan: {
    joinNow: '快进群！不错过每个校园新鲜事~',
  },
  remoteConfigKeys: {
    enableSmallNote: 'ENABLE_SMALL_NODE',
  },
  mediaTypes: {
    lackIcon: 'lack-icon',
    ing: 'ing',
  },
  categoryType: {
    pro: 'pro',
    normal: 'normal',
  },
  coinLimits: {
    lowCoinForNormal: 500, //首页硬币较少
    lowCoinForPro: 5000, //写作的最低
    lackCoinForNormal: 200, //聊天的最低
    lackCoinForPro: 8000, //聊天的pro最低
    lackWorkCoinForNormal: 1000, //AI答题
    lackCreateCoinForNormal: 15000, //类型文章最低
    lackCreateCoinForPro: 45000, //类型文章pro最低
  },
  autoLoginHosts: ['xiaoin.inschool.top', 'xiaoin.com.cn', 'xiaoin-test.starringshop.com'],
  isWxDebugMode: false,
  sharePageMode: {
    share: 'share',
    view: 'view',
  },
}

export { StarloveConstants }
