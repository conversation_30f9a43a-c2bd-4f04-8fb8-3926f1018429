import { isLocalHost } from '@/utils/cross'
import { storage } from '@/utils/local-storage'
import { cloneDeep } from 'lodash'
import type { Chapter, CreatorRequireFieldInfo, Section } from '~/services/types/appMessage'
import { UserService } from '~/services/user'
import { KnowledgeFileIcon } from './constants'
import { StarloveConstants } from './starloveConstants'

interface HTMLElementWithFullScreen extends HTMLElement {
  requestFullScreen?: () => void;
  webkitRequestFullScreen?: () => void;
  mozRequestFullScreen?: () => void;
}


export const getAIEditorBaseUrl = () => {
  if (isLocalHost()) {
    return 'https://xiaoin-test.inschool.top/ai-editor/#'
  }
  return '/ai-editor/#'
}


// 全屏
export const fullScreen = (element: HTMLElementWithFullScreen) => {
  if (element.requestFullScreen) {
    element.requestFullScreen()
  } else if (element.webkitRequestFullScreen) {
    element.webkitRequestFullScreen()
  } else if (element.mozRequestFullScreen) {
    element.mozRequestFullScreen()
  }
}

export const dateFormat = (date: string | number | Date) => {
  // const seperator1 = '-' // 自定义日期分隔符
  const newDate = new Date(date)
  const year = newDate.getFullYear() // 获取年
  const month = (newDate.getMonth() + 1).toString().padStart(2, '0') // 获取月
  const strDate = newDate.getDate().toString().padStart(2, '0') // 获取日
  const hours = newDate.getHours().toString().padStart(2, '0') // 获取时
  const minutes = newDate.getMinutes().toString().padStart(2, '0') // 获取分
  const second = newDate.getSeconds().toString().padStart(2, '0')

  const currentdate = `${year}-${month}-${strDate} ${hours}:${minutes}:${second}`
  return currentdate
}

export function validatePhoneNumber(phone: string) {
  if (!phone) {
    return false
  }
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

export const isEmpty = (obj: any) => {
  if (typeof obj === 'undefined' || obj === null || obj === '') {
    return true
  } else {
    return false
  }
}
export const clearObjectNullParam = (obj: any) => {
  /* 删除空值 */
  Object.keys(obj).forEach((item) => {
    if (isEmpty(obj[item])) {
      delete obj[item]
    }
  })
  return obj
}
export const isJSON = (str: unknown) => {
  if (typeof str == 'string') {
    try {
      const obj = JSON.parse(str)
      if (typeof obj == 'object' && obj) {
        return true
      } else {
        return false
      }
    } catch (e) {
      // console.log('error：' + str + '!!!' + e)
      return false
    }
  }
  console.log('It is not a string!')
}
export function sleep(time: number) {
  return new Promise((resolve) => setTimeout(resolve, time))
}

export const generateCurrentTimestampString = () => {
  const date = new Date()
  const timestamp = date.getTime()
  return timestamp.toString()
}
export function getAiQuestionModeList() {
  if (import.meta.client) {
    const data = storage.get(StarloveConstants.keyOflocalStorage.aiQuestionModeList)
    if (!data) {
      return [QuestionTypeEnum.useR1, QuestionTypeEnum.useSearch]
    }
    const list = JSON.parse(data)
    return list || [QuestionTypeEnum.useR1, QuestionTypeEnum.useSearch]
  }
  return []
}

interface DateFormatMap {
  M: number;
  d: number;
  h: number;
  m: number;
  s: number;
  q: number;
  S: number;
}

export function dateFormatS(date: Date, format: string): string {
  const map: DateFormatMap = {
    M: date.getMonth() + 1, // 月份
    d: date.getDate(), // 日
    h: date.getHours(), // 小时
    m: date.getMinutes(), // 分钟
    s: date.getSeconds(), // 秒
    q: Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds(), // 毫秒
  }

  format = format.replace(/([yMdhmsqS])+/g, (all: string, t: string) => {
    if (t === 'y') {
      return (date.getFullYear() + '').substr(4 - all.length)
    }
    const key = t as keyof DateFormatMap
    let v = map[key]
    if (v !== undefined) {
      if (all.length > 1) {
        const paddedValue = v.toString().padStart(2, '0')
        return paddedValue.substr(paddedValue.length - all.length)
      }
      return v.toString()
    }
    return all
  })
  return format
}

export function chineseNumberToArabic(chineseNumber: unknown) {
  if (typeof chineseNumber !== 'string') {
    return 0
  }

  const chineseNumberMap: Record<string, number> = {
    一: 1,
    二: 2,
    三: 3,
    四: 4,
    五: 5,
    六: 6,
    七: 7,
    八: 8,
    九: 9,
    十: 10,
    百: 100,
    千: 1000,
  };

  let result = 0;
  let currentNumber = 0;

  for (const char of chineseNumber) {
    const value = chineseNumberMap[char];
    if (value >= 10) {
      result += (currentNumber === 0 ? 1 : currentNumber) * value;
      currentNumber = 0;
    } else {
      currentNumber = currentNumber * 10 + value;
    }
  }
  result += currentNumber;
  return result;
}

// 阿拉伯数字转换为中文
export function arabicNumberToChinese(number: any) {
  const chineseNumbers = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九']
  const unit = ['', '十', '百', '千']
  let result = ''
  const numStr = number.toString()
  for (let i = 0; i < numStr.length; i++) {
    const digit = parseInt(numStr.charAt(i))
    const position = numStr.length - 1 - i
    if (digit !== 0) {
      result += chineseNumbers[digit] + unit[position]
    } else {
      // 处理连续的零，确保只添加一个零
      if (result.charAt(result.length - 1) !== chineseNumbers[0]) {
        result += chineseNumbers[digit]
      }
    }
  }
  // 特殊处理十位上的零
  result = result.replace('一十', '十')
  return result
}

export function getMaxLevelByCreatorRequireFieldInfoList(fieldInfo: CreatorRequireFieldInfo | undefined | null) {
  // const list = creatorRequireFieldInfoList.filter((item) => item.fieldCode == 'outline')
  const defaultLevel = 1
  if (!fieldInfo) {
    return defaultLevel
  }
  // if (list.length < 1) {
  //   return defaultLevel
  // }
  const options = fieldInfo?.options
  if (!options) {
    return defaultLevel
  }
  if (/^\d+$/.test(options)) return ~~options || defaultLevel
}

/**
 * 指定大纲，给网页版本使用
 * @param creatorRequireFieldInfoList 数组
 * @returns number
 */
export function getMaxLevelByCreatorRequireFieldInfoListNew(creatorRequireFieldInfoList: CreatorRequireFieldInfo[]) {
  const list = creatorRequireFieldInfoList.filter((item) => item.fieldCode == 'outline')
  const defaultLevel = 1

  if (list.length < 1) {
    return defaultLevel
  }
  const options = list[0].options
  if (!options) {
    return defaultLevel
  }
  if (/^\d+$/.test(options)) return ~~options || defaultLevel
}

export const transformToUserOutline = (data: any) => {
  return data.map((item: any) => ({
    chapter_title: item.name,
    sections: (item.children || []).map((section: any) => ({
      section_title: section.name,
      nodes: (section.children || []).map((node: any) => ({
        node_title: node.name,
      })),
    })),
  }))
}

export const getFieldTextForInformation = (options: string, fieldValue: string) => {
  if (!options || !fieldValue) {
    return
  }
  const optionList = JSON.parse(options)
  const list = fieldValue.split(',')
  return optionList
    .filter((item: any) => list.includes(item.value))
    .map((child: any) => child.label)
    .toString()
}

export function inRunningInPWA() {
  if (process.server) return false

  if (window.matchMedia('(display-mode: standalone)').matches) {
    return true
  }

  const source = localStorage.getItem(
    StarloveConstants.keyOflocalStorage.isCurrentOperationAppOnTheDesktop
  )

  if (source == 'pwa') {
    return true
  }

  // 移动到 onMounted 或 client-only 组件中处理
  return false
}

// 新增函数处理 PWA 安装事件
export function setupPWAInstallation() {
  // console.log('setupPWAInstallation. process.server ==>', process.server)
  // if ('BeforeInstallPromptEvent' in window) {
  //   console.log('Browser supports beforeinstallprompt')
  // } else {
  //   console.log('Browser does not support beforeinstallprompt')
  // }
  // if (process.server) return
  // console.log('setupPWAInstallation. ==>', window.location.search)
  // window.addEventListener('load', () => {
  //   const urlParams = new URLSearchParams(window.location.search)
  //   const source = urlParams.get('source')
  //   console.log('setupPWAInstallation. source ==>', source)
  //   if (source === 'pwa') {
  //     localStorage.setItem(
  //       StarloveConstants.keyOflocalStorage.isCurrentOperationAppOnTheDesktop,
  //       source
  //     )
  //   }
  // })
}

export function getAttachmentsFromSubmission(submission: any) {
  const attachments = submission.attachments || submission.formData.attachments
  if (Array.isArray(submission?.attachments) && Array.isArray(submission?.formData?.attachments)) {
    const _attachments = cloneDeep(attachments)
    //如要合并对象
    _attachments.forEach((item: any, index: string | number) => {
      const obj = submission?.formData?.uploadAttachments.find((i: any) => i?.fileId == item.id)
      if (obj) {
        _attachments[index] = {
          ...obj,
          ...item,
          key: item.id,
          title: item?.fileName || '',
        }
      }
    })
    return _attachments
  }
  return attachments
}

export const removeQuestionMarkText = (str: string) => {
  return str.replace(/\?.*$/, '')
}

export interface OutlineElement extends HTMLElement {
  getAttribute(name: string): string | null;
  textContent: string;
}

export const formatOutlineTextContent = (pList: OutlineElement[]) => {
  if (!pList || pList.length == 0) {
    return []
  }
  const chapters: Chapter[] = []
  let currentChapter: Chapter | null = null
  let currentSection: Section | null = null

  let isThereHierarchyError = false

  pList.forEach((p) => {
    const level = parseInt(p.getAttribute('data-lv') || '0')
    const title = p.textContent.trim()

    if (level === 1) {
      // New chapter
      currentChapter = { chapter_title: title, sections: [] }
      chapters.push(currentChapter)
      currentSection = null
    } else if (level === 2) {
      // New section
      currentSection = { section_title: title, nodes: [] }
      if (!currentChapter) {
        currentChapter = { chapter_title: '', sections: [] }
      }
      if (!currentChapter.sections) currentChapter.sections = []
      currentChapter.sections.push(currentSection)
    } else if (level === 3) {
      // New node
      const node = { node_title: title }
      if (currentSection) {
        currentSection.nodes = currentSection.nodes || []
        currentSection.nodes.push(node)
      } else {
        currentSection = { section_title: '', nodes: [node] }
        // if (!currentChapter!.sections) currentChapter!.sections = []
        if (!currentChapter) {
          currentChapter = { chapter_title: '', sections: [] }
        }
        if (!currentChapter.sections) currentChapter.sections = []
        currentChapter!.sections.push(currentSection)

        isThereHierarchyError = true
      }
    }
  })
  if (isThereHierarchyError) {
    return []
  }
  return chapters
}

export const getCurrentTimestampString = () => {
  const date = new Date()
  const timestamp = date.getTime()
  return timestamp.toString()
}

export const moneyFormatCent = (num: number | string | undefined | null) => {
  if (num === undefined || num === null || num === '') {
    return '0.00'
  }
  const num1 = Number(num) / 100
  return `${num1.toFixed(2)}`.replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,')
}

export const moneyFormatCentAreNotKept = (num: number | string | undefined | null) => {
  if (num === undefined || num === null || num === '') {
    return '0.00'
  }
  const num1 = Number(num) / 100

  if (num1 < 1) {
    return `${num1.toFixed(2)}`.replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,')
  }

  // 检查是否有小数部分且不为0
  const hasNonZeroDecimals = num1 % 1 !== 0
  const formattedNum = hasNonZeroDecimals
    ? num1.toString() // 如果有小数部分，直接使用原始值（不四舍五入）
    : Math.floor(num1).toString() // 如果没有小数或小数为0，取整数部分

  return formattedNum.replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,')
}

// 格式化硬币数量的函数
export const formatCoinAmount = (amount: number, fixed: number = 2) => {
  // 不使用四舍五入的方式截取小数点后的位数
  const truncateDecimals = (num: number, digits: number) => {
    const multiplier = Math.pow(10, digits);
    return Math.floor(num * multiplier) / multiplier;
  };

  if (amount >= 100000000) {
    // 大于等于一亿
    const billion = truncateDecimals(amount / 100000000, fixed);
    // 格式化数字，确保有指定位数的小数，并移除末尾的0
    // const formattedValue = billion.toFixed(fixed).replace(/\.?0+($| )/, '$1');
    const formattedValue = billion.toFixed(fixed).replace(/\.([0-9]*?)0+$/, '.$1').replace(/\.$/, '');

    return `${formattedValue}亿`;
  } else if (amount >= 10000) {
    // 大于等于一万
    const tenThousand = truncateDecimals(amount / 10000, fixed);
    // 格式化数字，确保有指定位数的小数，并移除末尾的0
    // const formattedValue = tenThousand.toFixed(fixed).replace(/\.?0+($| )/, '$1');
    const formattedValue = tenThousand.toFixed(fixed).replace(/\.([0-9]*?)0+$/, '.$1').replace(/\.$/, '');

    return `${formattedValue}万`;
  }
  // 小于一万直接返回整数
  return `${Math.floor(amount)}`;
}

/**
 * 格式化团队共享写作硬币数量
 * @param amount 初始化的值是千万
 * @param fixed 
 * @returns 
 */
export const formatTeamCoinAmount = (amount: number, fixed: number = 2) => {
  if (amount > 9999) {
    const billion = amount / 10000; // 转换为亿
    const formattedBillion = billion.toFixed(fixed); // 保留两位小数
    return `${formattedBillion}亿`;
  }
  return `${amount}万`;
}

// export const isXioin = window.location.hostname.indexOf('xiaoin.cn') > -1
export const getPayChannel = () => {
  // return PayChannelType.xiaoin
  if (import.meta.client) {
    const utm_source = sessionStorage.getItem('utm_source')
    if (utm_source) {
      if (utm_source === 'lenovo') {
        // 如果 sharerUserId 是桌面端
        if (UserService.getSharerUserId() == LENOVO_DESKTOP_SHARER_USER_ID) {
          return PayChannelType.lenovoDeskTop
        }
        return PayChannelType.lenovo
      } else if (utm_source === 'qihu360') {
        return PayChannelType.qihu360
      } else if (utm_source === 'huawei') {
        return PayChannelType.huawei
      } else if (utm_source === 'thunderobot') {
        return PayChannelType.thunderobot
      }
    }

    if (isXiaoin) {
      return PayChannelType.xiaoin
    }
    return PayChannelType.xiaoinAssistant
  } else {
    return PayChannelType.xiaoin
  }
}
export function downloadFile(url: string, fileName: string = '') {
  const link = document.createElement('a')
  link.href = url
  link.download = fileName
  link.target = '_blank' // 可选，如果希望新窗口中下载文件，请取消注释此行
  link.click()
}

export const getCoinInfo = (coinDrtail: any) => {
  if (!UserService.isLogined()) {
    return
  }
  if (!coinDrtail || !coinDrtail.list) {
    return
  }
  if (coinDrtail.list?.length < 2) {
    return
  }
  // console.log('coinDrtail ==>', coinDrtail.list[1])
  return coinDrtail.list[1]
}

export const getStaleTimeText = (coinDrtail: any) => {
  const coinInfo = getCoinInfo(coinDrtail)

  if (!coinInfo || !coinInfo.balance || !coinInfo.expireTime) {
    return
  }
  const expireTime = coinInfo.expireTime.split(' ')
  return `${coinInfo.balance}硬币将在${expireTime[0]}过期`
}

export const getCountDownText = (coinDrtail: any) => {
  const coinInfo = getCoinInfo(coinDrtail)

  if (!coinInfo || !coinInfo.balance || !coinInfo.expireTime) {
    return
  }
  const expireTime = coinInfo.expireTime.split(' ')
  return `剩余有效期${calculateRemainingDays(expireTime[0])}天`
}

export const calculateRemainingDays = (time: string | Date) => {
  const now = new Date()
  const target = new Date(time)
  const oneDay = 1000 * 60 * 60 * 24
  const difference = Math.ceil((target.getTime() - now.getTime()) / oneDay)
  return difference
}

//检查号码是否符合规范，包括长度，类型
export function isCardNo(data: string) {
  //身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
  const reg = /(^\d{15}$)|(^\d{17}(\d|X)$)/
  if (reg.test(data) === false) {
    return false
  }
  return true
}

interface CityMap {
  [key: string]: string;
}

//取身份证前两位,校验省份
export function checkProvince(data: string) {
  const city: CityMap = {
    11: '北京',
    12: '天津',
    13: '河北',
    14: '山西',
    15: '内蒙古',
    21: '辽宁',
    22: '吉林',
    23: '黑龙江',
    31: '上海',
    32: '江苏',
    33: '浙江',
    34: '安徽',
    35: '福建',
    36: '江西',
    37: '山东',
    41: '河南',
    42: '湖北',
    43: '湖南',
    44: '广东',
    45: '广西',
    46: '海南',
    50: '重庆',
    51: '四川',
    52: '贵州',
    53: '云南',
    54: '西藏',
    61: '陕西',
    62: '甘肃',
    63: '青海',
    64: '宁夏',
    65: '新疆',
    71: '台湾',
    81: '香港',
    82: '澳门',
    91: '国外',
  }
  const province = data.substr(0, 2)
  if (city[province] == undefined) {
    return false
  }
  return true
}

//检查生日是否正确
export function checkBirthday(data: string) {
  const len = data.length
  //份证15位时，次序为省（3位）市（3位）年（2位）月（2位）日（2位）校验位（3位），皆为数字
  if (len === 15) {
    const re_fifteen = /^(\d{6})(\d{2})(\d{2})(\d{2})(\d{3})$/
    const arr_data = data.match(re_fifteen)
    if (!arr_data) return false
    const year = arr_data[2]
    const month = arr_data[3]
    const day = arr_data[4]
    const birthday = new Date('19' + year + '/' + month + '/' + day)
    return verifyBirthday('19' + year, month, day, birthday)
  }
  //身份证18位时，次序为省（3位）市（3位）年（4位）月（2位）日（2位）校验位（4位），校验位末尾可能为X
  if (len === 18) {
    const re_eighteen = /^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X)$/
    const arr_data = data.match(re_eighteen)
    if (!arr_data) return false
    const year = arr_data[2]
    const month = arr_data[3]
    const day = arr_data[4]
    const birthday = new Date(year + '/' + month + '/' + day)
    return verifyBirthday(year, month, day, birthday)
  }
  return false
}

//校验日期
export function verifyBirthday(year: string, month: string, day: string, birthday: Date) {
  const now = new Date()
  const now_year = now.getFullYear()
  //年月日是否合理
  if (
    birthday.getFullYear() == parseInt(year) &&
    birthday.getMonth() + 1 == parseInt(month) &&
    birthday.getDate() == parseInt(day)
  ) {
    //判断年份的范围（0岁到130岁之间)
    const time = now_year - parseInt(year)
    if (time >= 0 && time <= 150) {
      return true
    }
    return false
  }
  return false
}
//校验位的检测
export function checkParity(data: string) {
  //15位转18位
  data = changeFivteenToEighteen(data)
  const len = data.length
  if (len === 18) {
    const arrInt = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    const arrCh = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
    let cardTemp = 0
    for (let i = 0; i < 17; i++) {
      cardTemp += parseInt(data.substr(i, 1)) * arrInt[i]
    }
    const valnum = arrCh[cardTemp % 11]
    if (valnum === data.substr(17, 1)) {
      return true
    }
    return false
  }
  return false
}
//15位转18位身份证号
export function changeFivteenToEighteen(data: string) {
  if (data.length === 15) {
    const arrInt = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    const arrCh = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
    let cardTemp = 0
    data = data.substr(0, 6) + '19' + data.substr(6, data.length - 6)
    for (let i = 0; i < 17; i++) {
      cardTemp += parseInt(data.substr(i, 1)) * arrInt[i]
    }
    data += arrCh[cardTemp % 11]
    return data
  }
  return data
}

interface Parameters {
  [key: string]: string;
}

export function getURLParameters(url: string) {
  if (!url) {
    return
  }
  if (url.indexOf('?') == -1) {
    return
  }
  // 获取问号后面的部分
  const queryString = url.split('?')[1]
  // 分割参数对
  const parameterPairs = queryString.split('&')
  // 创建一个对象来存储参数
  const parameters: Parameters = {}
  // 遍历参数对，将其分割为键和值，并存储在对象中
  parameterPairs.forEach((pair) => {
    const [key, value] = pair.split('=')
    parameters[key] = decodeURIComponent(value)
  })
  return parameters
}

function floorToDecimalPlaces(number: number, decimalPlaces: number) {
  const factor = Math.pow(10, decimalPlaces)
  return Math.floor(number * factor) / factor
}

export function getLearningWordCount(learningWordCount: number) {
  if (learningWordCount > 9999) {
    return `${floorToDecimalPlaces(learningWordCount / 10000, 1)}万`
  }
  // if (learningWordCount > 1000) {
  //   return `${floorToDecimalPlaces(learningWordCount / 1000, 1)}千`
  // }
  return learningWordCount
}

export function getFieldItemSizeValue(child: string): CreateContentSize | null {
  if (child.includes('超短')) return CreateContentSize.tiny
  if (child.startsWith('短')) return CreateContentSize.small
  if (child.includes('中')) return CreateContentSize.middle
  if (child.includes('长')) return CreateContentSize.large
  return null // 返回 null 表示未匹配到
}

export function transformSizeToEn(child: string): string {
  if (child.includes('超短')) return CreateContentSize.tiny
  if (child.startsWith('短')) return CreateContentSize.small
  if (child.includes('中')) return CreateContentSize.middle
  if (child.includes('长')) return CreateContentSize.large
  return child
}

export const isXiaoinNew = (curUrl: string = '') => {
  return curUrl.indexOf('xiaoin.cn') > -1
}

export const getPlatform = () => {
  if (typeof window === 'undefined') return 'web'

  // 根据实际需求判断平台
  // if (/Android/i.test(navigator.userAgent)) return 'android'
  // if (/iPhone|iPad|iPod/i.test(navigator.userAgent)) return 'ios'
  try {
    if (import.meta.client && window?.sessionStorage) {
      const platform = sessionStorage.getItem(StarloveConstants.keyOflocalStorage.platform)
      if (platform) {
        return platform
      }
    }
    return 'web'
  } catch (e) {
    return 'web'
  }
}
export function parentPostMessage(command: string, value = {}) {
  if (window.parent) {
    const postdata = {
      command,
      value
    }
    window.parent.postMessage(postdata, '*')
  }
}
// 定义Base64对象
export function Base64Encode(str: string) {
  if (!str) {
    return
  }
  // Base64字符集
  const base64Chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'

  let result = ''
  for (let i = 0; i < str.length; i += 3) {
    const a = str.charCodeAt(i)
    const b = i + 1 < str.length ? str.charCodeAt(i + 1) : 0
    const c = i + 2 < str.length ? str.charCodeAt(i + 2) : 0

    const a1 = a >> 2,
      a2 = ((a & 3) << 4) | (b >> 4),
      a3 = ((b & 15) << 2) | (c >> 6),
      a4 = c & 63

    result +=
      base64Chars[a1] +
      base64Chars[a2] +
      (i + 1 < str.length ? base64Chars[a3] : '=') +
      (i + 2 < str.length ? base64Chars[a4] : '=')
  }
  return result
}

// 解码函数
export function Base64Decode(str: string) {
  if (!str) {
    return
  }
  // Base64字符集
  const base64Chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
  let result = ''
  let i = 0
  while (i < str.length) {
    const a = base64Chars.indexOf(str.charAt(i++))
    const b = base64Chars.indexOf(str.charAt(i++))
    const c = base64Chars.indexOf(str.charAt(i++))
    const d = base64Chars.indexOf(str.charAt(i++))

    const a1 = (a << 2) | (b >> 4)
    const a2 = ((b & 15) << 4) | (c >> 2)
    const a3 = ((c & 3) << 6) | d

    result += String.fromCharCode(a1)
    if (c != 64) {
      result += String.fromCharCode(a2)
    }
    if (d != 64) {
      result += String.fromCharCode(a3)
    }
  }
  return result
}
export function formatStorageSize(sizeInBytes: number, fixed: number = 2) {
  const unit = 1024
  if (sizeInBytes < unit) {
    return sizeInBytes + 'B';
  } else if (sizeInBytes < unit * unit) {
    return (sizeInBytes / unit).toFixed(fixed) + 'KB';
  } else if (sizeInBytes < unit * unit * unit) {
    return (sizeInBytes / (unit * unit)).toFixed(fixed) + 'MB';
  } else {
    return (sizeInBytes / (unit * unit * unit)).toFixed(fixed) + 'GB';
  }
}

// 计算补差价金额
export const calculateUpgradePrice = (price: number, currentLevelVipPrice: number) => {
  if (!price) return 0;

  // 获取当前等级的商品信息
  // const currentLevelVipInfo = getCurrentLevelVipInfo();
  if (!currentLevelVipPrice) return price;

  // 直接计算价格差
  return Math.max(0, price - currentLevelVipPrice);
};

export const calculateEndDate = (validityDays: number, cData?: string | null) => {
  const currentDate = cData ? new Date(cData) : new Date();
  const endDate = new Date(currentDate.getTime() + validityDays * 24 * 60 * 60 * 1000);
  return dateFormatS(endDate, 'yyyy-MM-dd');
}

/**
 * 获取日期的年月日部分
 * @param dateString 完整的日期时间字符串，格式如: "2023-02-10 12:22:42"
 * @returns 返回年月日部分，格式如: "2023-02-10"，如果输入格式不正确则返回空字符串
 */
export const getDatePart = (dateString: string | null): string => {
  if (!dateString) return ''

  try {
    const parts = dateString.split(' ')
    if (parts.length >= 1) {
      return parts[0]
    }
    return ''
  } catch (error) {
    return ''
  }
}

export function getFileName(url?: string) {
  if (!url) {
    return '';
  }
  if (url.indexOf('?') == -1) {
    const fileName = url.substring(url.lastIndexOf('/') + 1);

    return fileName ? decodeURIComponent(fileName) : '';
  }
  // 先移除查询参数部分（如果存在）
  const urlWithoutQuery = url.split('?')[0];

  // 从最后一个斜杠后获取文件名
  const fileName = urlWithoutQuery.substring(urlWithoutQuery.lastIndexOf('/') + 1);

  // 解码文件名（处理特殊字符）
  return fileName ? decodeURIComponent(fileName) : '';
}


export function getFileDownloadUel(url?: string) {
  if (!url) {
    return
  }
  if (url.indexOf('?') == -1) {
    return url
  }
  const urlWithoutQuery = url.split('?')[0]
  return urlWithoutQuery
}

/**
 * 安全地将日期格式化为 YYYY-MM-DD HH:MM:SS 格式
 * 处理可能的无效日期情况，避免 RangeError: Invalid Date 错误
 * @param date Date对象或日期字符串或时间戳
 * @returns 格式化后的日期字符串，如果日期无效则返回空字符串
 */
export function formatDateTimeString(date: Date | string | number | null | undefined): string {
  // 如果输入为空，直接返回空字符串
  if (date === null || date === undefined) {
    return '';
  }

  try {
    // 创建日期对象
    const dateObj = new Date(date);

    // 检查日期是否有效
    if (isNaN(dateObj.getTime())) {
      console.warn('无效的日期:', date);
      return '';
    }

    // 格式化日期部分：YYYY-MM-DD
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    const datePart = `${year}-${month}-${day}`;

    // 格式化时间部分：HH:MM:SS
    const hours = String(dateObj.getHours()).padStart(2, '0');
    const minutes = String(dateObj.getMinutes()).padStart(2, '0');
    const seconds = String(dateObj.getSeconds()).padStart(2, '0');
    const timePart = `${hours}:${minutes}:${seconds}`;

    // 返回完整的日期时间字符串
    return `${datePart} ${timePart}`;
  } catch (error) {
    console.warn('格式化日期时出错:', error);
    return '';
  }
}

export function transformCatalogs(catalogs: any) {
  return catalogs.map((catalog: any) => ({
    name: catalog.catalog,
    children: catalog.pages.map((page: any) => ({
      name: page.title,
      children: page.points.map((point: any) => ({
        name: point,
      })),
    })),
  }))
}

export const isMarkdown = (text: string) => {
  // 检查是否包含标题（#）、列表（*）、加粗/斜体（*或_）、链接（[]()）等Markdown特征
  const markdownRegex = /(^#{1,6}\s)|(^-|\*\s)|(\*\*|__)|(\[.*?\]\(.*?\))|(\n(?=(\n+)))/gm
  return markdownRegex.test(text)
}

/**
 * 获取文件的扩展名(后缀)
 * @param fileName 文件名或文件路径
 * @param includeDot 是否包含点，默认为false
 * @returns 返回不包含点的文件扩展名，如"jpg"而非".jpg"。如果文件没有扩展名或输入无效，返回空字符串
 */
export function getFileExtension(fileName?: string, includeDot: boolean = false): string {
  if (!fileName) {
    return '';
  }

  // 移除可能存在的查询参数
  const cleanFileName = fileName.split('?')[0];

  // 处理可能存在的路径，只保留文件名部分
  const baseName = cleanFileName.split('/').pop() || cleanFileName;

  // 查找最后一个点的位置
  const lastDotIndex = baseName.lastIndexOf('.');

  // 如果没有找到点或者点在第一位(隐藏文件)，则返回空字符串
  if (lastDotIndex === -1 || lastDotIndex === 0) {
    return '';
  }

  // 根据includeDot参数决定是否包含点
  return includeDot
    ? baseName.substring(lastDotIndex)
    : baseName.substring(lastDotIndex + 1);
}

// 防抖函数
export const createDebounce = (fn: Function, delay: number) => {
  let timer: number | null = null;
  return function (...args: any[]) {
    if (timer) {
      clearTimeout(timer);
    }
    timer = window.setTimeout(() => {
      fn(...args);
      timer = null;
    }, delay);
  };
};

// 文件哈希值
export const getFileSha256 = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = async function (e: ProgressEvent<FileReader>) {
      try {
        if (!e.target?.result) {
          throw new Error('读取文件失败')
        }

        // const hashBuffer = await crypto.subtle.digest('SHA-256', e.target.result as ArrayBuffer)
        // 使用文件名和大小作为简单的替代方案
        const hashString = `${file.name}-${file.size}-${file.lastModified}`
        const encoder = new TextEncoder()
        const data = encoder.encode(hashString)

        // 如果支持 Web Crypto API，使用它
        if (window.crypto && window.crypto.subtle) {
          try {
            const hashBuffer = await crypto.subtle.digest('SHA-256', data)
            const hashArray = Array.from(new Uint8Array(hashBuffer))
            const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
            resolve(hashHex)
          } catch (error) {
            console.warn('Web Crypto API failed:', error)
            // 降级到简单哈希
            resolve(simpleHash(hashString))
          }
        } else {
          // 如果不支持 Web Crypto API，使用简单哈希
          resolve(simpleHash(hashString))
        }
      } catch (error) {
        reject(error)
      }
    }

    reader.onerror = reject
    reader.readAsArrayBuffer(file)
  })
}

// 简单的哈希函数
const simpleHash = (str: string): string => {
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32bit integer
  }
  // 转换为16进制字符串并填充到64位
  return Math.abs(hash).toString(16).padStart(64, '0')
}


// 获取字数
export const getWordCount = (text: string) => {
  switch (text) {
    case 'small':
      return 4000;
    case 'middle':
      return 8000;
    case 'large':
      return 20000;
    default:
      return '';
  }
}
export const getFileIcon = (fileType: string): string => {
  switch (fileType.toLowerCase()) {
    case 'pdf':
      return KnowledgeFileIcon.pdf;
    case 'doc':
    case 'docx':
      return KnowledgeFileIcon.doc;
    case 'ppt':
    case 'pptx':
      return KnowledgeFileIcon.ppt;
    case 'img':
    case 'jpg':
    case 'jpeg':
    case 'png':
      return KnowledgeFileIcon.img;
    case 'txt':
    case 'md':
    case 'text':
      return KnowledgeFileIcon.text;
    case 'xlsx':
    case 'csv':
      return KnowledgeFileIcon.xlsx;
    default:
      return KnowledgeFileIcon.encode;
  }
}